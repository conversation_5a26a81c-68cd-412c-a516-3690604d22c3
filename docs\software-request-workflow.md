# 🏫 Schopio Software Request Workflow Documentation

## 📋 **OVERVIEW**

Comprehensive School Management System Request workflow that handles both demo and production requests with proper terms acceptance, admin approval, and automated billing integration.

## 🎯 **WORKFLOW TYPES**

### 1. **Demo Request**
- **Purpose**: Temporary access for evaluation
- **Duration**: 7 days trial period
- **Requirements**: Basic school information only
- **Terms**: No formal terms/conditions required
- **Access**: Immediate activation after admin approval
- **Billing**: No payment required
- **Upgrade**: Can be upgraded to production request

### 2. **Production Request**
- **Purpose**: Full subscription with paid access
- **Duration**: Ongoing subscription (monthly/yearly)
- **Requirements**: Complete operational data + fee structure
- **Terms**: Full terms & conditions acceptance required
- **Access**: After admin approval + payment setup
- **Billing**: Automated recurring billing via Razorpay

## 🗄️ **DATABASE SCHEMA DESIGN**

### **New Table: `software_requests`**

```sql
CREATE TABLE software_requests (
  -- Primary identification
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID REFERENCES clients(id) NOT NULL,
  request_type VARCHAR(20) NOT NULL, -- 'demo', 'production'
  
  -- School operational data
  student_count INTEGER NOT NULL,
  faculty_count INTEGER NOT NULL,
  complete_address TEXT NOT NULL,
  contact_number VARCHAR(20) NOT NULL,
  primary_email VARCHAR(255) NOT NULL,
  
  -- Fee structure (for production requests only)
  class_1_fee DECIMAL(10,2),
  class_4_fee DECIMAL(10,2),
  class_6_fee DECIMAL(10,2),
  class_10_fee DECIMAL(10,2),
  class_11_fee DECIMAL(10,2),
  class_12_fee DECIMAL(10,2),
  class_11_12_fee DECIMAL(10,2), -- Legacy field for backward compatibility
  calculated_average_fee DECIMAL(10,2), -- Calculated and stored
  
  -- Request status workflow
  status VARCHAR(20) DEFAULT 'pending', 
  -- 'pending', 'under_review', 'approved', 'rejected', 'setup_in_progress', 'activated'
  
  -- Terms acceptance (production only)
  terms_accepted BOOLEAN DEFAULT false,
  terms_accepted_at TIMESTAMP,
  terms_version VARCHAR(10), -- Track which version was accepted
  ip_address INET, -- Log IP for legal compliance
  user_agent TEXT, -- Browser/device info
  
  -- Admin workflow
  reviewed_by UUID REFERENCES admin_users(id),
  review_notes TEXT,
  rejection_reason TEXT,
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  approved_at TIMESTAMP,
  activated_at TIMESTAMP,
  
  -- Constraints
  CONSTRAINT valid_request_type CHECK (request_type IN ('demo', 'production')),
  CONSTRAINT valid_status CHECK (status IN ('pending', 'under_review', 'approved', 'rejected', 'setup_in_progress', 'activated')),
  CONSTRAINT production_requires_terms CHECK (
    (request_type = 'demo') OR 
    (request_type = 'production' AND terms_accepted = true)
  ),
  CONSTRAINT production_requires_fees CHECK (
    (request_type = 'demo') OR 
    (request_type = 'production' AND calculated_average_fee IS NOT NULL)
  )
);
```

### **New Table: `request_status_history`**

```sql
CREATE TABLE request_status_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  request_id UUID REFERENCES software_requests(id) NOT NULL,
  from_status VARCHAR(20),
  to_status VARCHAR(20) NOT NULL,
  changed_by UUID, -- admin_user_id or client_user_id
  change_reason TEXT,
  metadata JSONB, -- Additional context
  created_at TIMESTAMP DEFAULT NOW()
);
```

### **New Table: `terms_conditions`**

```sql
CREATE TABLE terms_conditions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  version VARCHAR(10) NOT NULL UNIQUE, -- 'v1.0', 'v1.1', etc.
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL, -- Full terms content
  effective_date DATE NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP DEFAULT NOW()
);
```

## 📋 **TERMS & CONDITIONS CONTENT**

### **Payment Terms**
- Monthly payment due on same date each month
- 3-day grace period after due date
- Late payment penalty: 2% per day after grace period
- Automatic suspension after 15 days overdue
- Account reactivation fee: ₹500 if suspended
- Data backup retention: 30 days during suspension
- Termination clause: Non-payment beyond 30 days

### **Service Terms**
- 99.5% uptime guarantee
- 24/7 technical support
- Data backup and security
- Feature updates included
- Training and onboarding support

### **Cancellation Policy**
- 30-day notice required for cancellation
- No refunds for partial months
- Data export available for 30 days post-cancellation
- Account reactivation within 90 days possible

## 🔄 **DEMO TO PRODUCTION UPGRADE WORKFLOW**

### **Upgrade Process**
Schools can upgrade their demo request to a production request without creating a new request:

1. **Eligibility Check**
   - Must have an existing demo request
   - Demo request can be in any status (pending, approved, activated, expired)
   - Only one upgrade per demo request allowed

2. **Upgrade Form Requirements**
   - **Class-wise Monthly Fees** (in ₹):
     - Class 1 Monthly Fee
     - Class 4 Monthly Fee
     - Class 6 Monthly Fee
     - Class 10 Monthly Fee
     - Class 11 Monthly Fee
     - Class 12 Monthly Fee
   - **Terms & Conditions Acceptance**
   - **Automatic Average Fee Calculation**

3. **Backend Processing**
   - Updates existing request type from 'demo' to 'production'
   - Stores all class-wise fee structures
   - Calculates and stores average monthly fee
   - Resets status to 'pending' for admin review
   - Creates status history entry for audit trail

4. **Database Changes**
   - No new record created (updates existing software_requests row)
   - Maintains request history and audit trail
   - Preserves original demo request metadata

### **Technical Implementation**
- **API Endpoint**: `POST /api/auth/software-request/upgrade-to-production`
- **Authentication**: JWT token required
- **Validation**: Zod schema validation for all fee fields
- **Error Handling**: Comprehensive error responses
- **Audit Trail**: Complete status change logging

## 🔄 **WORKFLOW STATES**

### **Status Progression**

```
pending → under_review → approved → setup_in_progress → activated
    ↓           ↓
  rejected    rejected
```

### **State Descriptions**

1. **pending**: Initial submission, awaiting admin review
2. **under_review**: Admin is actively reviewing the request
3. **approved**: Admin approved, system setup in progress
4. **rejected**: Admin rejected with reason
5. **setup_in_progress**: Technical setup and configuration
6. **activated**: System is live and accessible

## 💰 **PRICING CALCULATION**

### **Fee Structure Collection**
- Class 1 fee (₹/month)
- Class 4 fee (₹/month) 
- Class 6 fee (₹/month)
- Class 10 fee (₹/month)
- Class 11-12 fee (₹/month)

### **Average Fee Calculation**
```javascript
const calculateAverageFee = (fees) => {
  const validFees = Object.values(fees).filter(fee => fee > 0);
  return validFees.reduce((sum, fee) => sum + fee, 0) / validFees.length;
};
```

### **Subscription Pricing**
- Base rate: ₹80/student/month (monthly plan)
- Yearly discount: 18.75% (₹65/student/month)
- Minimum billing: 50 students
- Maximum billing: Actual student count

## 🔐 **SECURITY MEASURES**

### **Rate Limiting**
- 3 requests per user per day
- 10 requests per IP per hour
- Exponential backoff for repeated failures

### **Audit Trail**
- All status changes logged
- IP address and user agent tracking
- Terms acceptance with timestamp
- Admin action logging

### **Data Protection**
- Encrypted sensitive data
- GDPR compliance for data handling
- Secure data retention policies
- Right to data deletion

## 📧 **EMAIL NOTIFICATIONS**

### **Request Submission**
- Confirmation to client
- Alert to admin team

### **Status Updates**
- Review started notification
- Approval/rejection notification
- Setup progress updates
- Activation confirmation

### **Payment Reminders**
- 7 days before due date
- Due date reminder
- Overdue notifications
- Suspension warnings

## 🚨 **ERROR HANDLING**

### **Validation Errors**
- Required field validation
- Fee structure validation
- Terms acceptance validation
- Duplicate request prevention

### **System Errors**
- Payment gateway failures
- Email delivery failures
- Database transaction rollbacks
- External service timeouts

### **Business Logic Errors**
- Invalid status transitions
- Unauthorized access attempts
- Subscription conflicts
- Billing calculation errors

## 📊 **REPORTING & ANALYTICS**

### **Admin Dashboard Metrics**
- Pending requests count
- Approval/rejection rates
- Average processing time
- Revenue projections

### **Request Analytics**
- Conversion rates (demo to production)
- Geographic distribution
- School size analysis
- Fee structure patterns

## 🔧 **MAINTENANCE PROCEDURES**

### **Daily Tasks**
- Review pending requests
- Process approved requests
- Monitor payment failures
- Check system health

### **Weekly Tasks**
- Generate request reports
- Review rejection patterns
- Update terms if needed
- Backup request data

### **Monthly Tasks**
- Analyze conversion metrics
- Review pricing strategy
- Update documentation
- Security audit

## 🔗 **API ENDPOINTS**

### **Software Request APIs**

#### **1. Create Software Request**
```
POST /api/auth/software-request
```
**Headers**: `Authorization: Bearer <jwt_token>`
**Body**:
```json
{
  "requestType": "demo" | "production",
  "studentCount": number,
  "facultyCount": number,
  "completeAddress": string,
  "contactNumber": string,
  "primaryEmail": string,
  // For production requests only:
  "class1Fee": number,
  "class4Fee": number,
  "class6Fee": number,
  "class10Fee": number,
  "class11Fee": number,
  "class12Fee": number,
  "termsAccepted": boolean,
  "termsVersion": string
}
```

#### **2. Get Request Status**
```
GET /api/auth/software-request/status
```
**Headers**: `Authorization: Bearer <jwt_token>`
**Response**: Current request status and details

#### **3. Upgrade Demo to Production**
```
POST /api/auth/software-request/upgrade-to-production
```
**Headers**: `Authorization: Bearer <jwt_token>`
**Body**:
```json
{
  "class1Fee": number,
  "class4Fee": number,
  "class6Fee": number,
  "class10Fee": number,
  "class11Fee": number,
  "class12Fee": number,
  "termsAccepted": boolean,
  "termsVersion": string
}
```

#### **4. Get Terms & Conditions**
```
GET /api/auth/terms-conditions
```
**Response**: Latest active terms and conditions

### **Technical Notes**
- All fee amounts are in Indian Rupees (₹)
- JWT authentication required for all endpoints
- Comprehensive validation using Zod schemas
- Error responses include detailed validation messages
- Audit trail maintained for all operations
- No transaction support due to Neon HTTP driver limitations

---

*This documentation serves as the complete reference for the Schopio Software Request Workflow system.*
