import { db } from '@/src/db'
import { subscriptions, billingCycles, invoices, clients } from '@/src/db/schema'
import { eq, and, desc, sql } from 'drizzle-orm'

export type SubscriptionStatus = 'active' | 'suspended' | 'cancelled' | 'expired' | 'pending' | 'overdue'

export interface StatusTransition {
  subscriptionId: string
  fromStatus: SubscriptionStatus
  toStatus: SubscriptionStatus
  reason: string
  triggeredBy?: string
  triggeredAt: Date
  metadata?: Record<string, any>
}

export interface SubscriptionStatusInfo {
  id: string
  currentStatus: SubscriptionStatus
  canTransitionTo: SubscriptionStatus[]
  restrictions: string[]
  nextBillingDate: string
  daysUntilDue: number
  outstandingAmount: number
  gracePeriodEnd?: Date
}

export class SubscriptionStatusManager {
  
  /**
   * Define valid status transitions
   */
  private static readonly STATUS_TRANSITIONS: Record<SubscriptionStatus, SubscriptionStatus[]> = {
    pending: ['active', 'cancelled'],
    active: ['suspended', 'cancelled', 'overdue', 'expired'],
    overdue: ['active', 'suspended', 'cancelled'],
    suspended: ['active', 'cancelled'],
    cancelled: [], // Terminal state
    expired: ['active'] // Can be reactivated
  }

  /**
   * Get current status information for a subscription
   */
  static async getSubscriptionStatusInfo(subscriptionId: string): Promise<SubscriptionStatusInfo | null> {
    try {
      const [subscription] = await db.select({
        id: subscriptions.id,
        status: subscriptions.status,
        nextBillingDate: subscriptions.nextBillingDate,
        dueDate: subscriptions.dueDate,
        gracePeriodDays: subscriptions.gracePeriodDays
      })
      .from(subscriptions)
      .where(eq(subscriptions.id, subscriptionId))
      .limit(1)

      if (!subscription) return null

      // Calculate days until due
      const nextBilling = new Date(subscription.nextBillingDate)
      const today = new Date()
      const daysUntilDue = Math.ceil((nextBilling.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))

      // Get outstanding amount from unpaid invoices
      const [outstandingResult] = await db.select({
        total: sql<number>`COALESCE(SUM(CAST(${invoices.totalAmount} AS DECIMAL)), 0)`
      })
      .from(invoices)
      .where(and(
        eq(invoices.clientId, subscription.id),
        sql`${invoices.status} IN ('sent', 'overdue')`
      ))

      const currentStatus = subscription.status as SubscriptionStatus
      const canTransitionTo = this.STATUS_TRANSITIONS[currentStatus] || []
      const restrictions = this.getStatusRestrictions(currentStatus)

      // Calculate grace period end if applicable
      let gracePeriodEnd: Date | undefined
      if (currentStatus === 'overdue' && subscription.gracePeriodDays) {
        gracePeriodEnd = new Date(nextBilling)
        gracePeriodEnd.setDate(gracePeriodEnd.getDate() + subscription.gracePeriodDays)
      }

      return {
        id: subscription.id,
        currentStatus,
        canTransitionTo,
        restrictions,
        nextBillingDate: subscription.nextBillingDate,
        daysUntilDue,
        outstandingAmount: outstandingResult.total || 0,
        gracePeriodEnd
      }

    } catch (error) {
      console.error('Error getting subscription status info:', error)
      return null
    }
  }

  /**
   * Transition subscription to new status
   */
  static async transitionStatus(
    subscriptionId: string,
    newStatus: SubscriptionStatus,
    reason: string,
    triggeredBy?: string,
    metadata?: Record<string, any>
  ): Promise<{ success: boolean; error?: string; transition?: StatusTransition }> {
    try {
      // Get current subscription
      const [subscription] = await db.select({
        id: subscriptions.id,
        status: subscriptions.status,
        clientId: subscriptions.clientId
      })
      .from(subscriptions)
      .where(eq(subscriptions.id, subscriptionId))
      .limit(1)

      if (!subscription) {
        return { success: false, error: 'Subscription not found' }
      }

      const currentStatus = subscription.status as SubscriptionStatus
      const validTransitions = this.STATUS_TRANSITIONS[currentStatus] || []

      // Check if transition is valid
      if (!validTransitions.includes(newStatus)) {
        return {
          success: false,
          error: `Invalid transition from ${currentStatus} to ${newStatus}`
        }
      }

      // Perform pre-transition validations
      const validationResult = await this.validateTransition(subscriptionId, currentStatus, newStatus)
      if (!validationResult.valid) {
        return { success: false, error: validationResult.error }
      }

      // Update subscription status
      await db.update(subscriptions)
        .set({ status: newStatus })
        .where(eq(subscriptions.id, subscriptionId))

      // Perform post-transition actions
      await this.performPostTransitionActions(subscriptionId, currentStatus, newStatus, metadata)

      const transition: StatusTransition = {
        subscriptionId,
        fromStatus: currentStatus,
        toStatus: newStatus,
        reason,
        triggeredBy,
        triggeredAt: new Date(),
        metadata
      }

      // Log the transition (you might want to store this in a transitions table)
      console.log(`📊 Subscription status transition:`, transition)

      return { success: true, transition }

    } catch (error) {
      console.error('Error transitioning subscription status:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Suspend subscription
   */
  static async suspendSubscription(
    subscriptionId: string,
    reason: string,
    triggeredBy?: string
  ): Promise<{ success: boolean; error?: string }> {
    const result = await this.transitionStatus(subscriptionId, 'suspended', reason, triggeredBy)
    return { success: result.success, error: result.error }
  }

  /**
   * Reactivate subscription
   */
  static async reactivateSubscription(
    subscriptionId: string,
    reason: string,
    triggeredBy?: string
  ): Promise<{ success: boolean; error?: string }> {
    const result = await this.transitionStatus(subscriptionId, 'active', reason, triggeredBy)
    return { success: result.success, error: result.error }
  }

  /**
   * Cancel subscription
   */
  static async cancelSubscription(
    subscriptionId: string,
    reason: string,
    triggeredBy?: string,
    effectiveDate?: Date
  ): Promise<{ success: boolean; error?: string }> {
    const metadata = effectiveDate ? { effectiveDate: effectiveDate.toISOString() } : undefined
    const result = await this.transitionStatus(subscriptionId, 'cancelled', reason, triggeredBy, metadata)
    return { success: result.success, error: result.error }
  }

  /**
   * Mark subscription as overdue
   */
  static async markOverdue(
    subscriptionId: string,
    triggeredBy?: string
  ): Promise<{ success: boolean; error?: string }> {
    const result = await this.transitionStatus(
      subscriptionId,
      'overdue',
      'Payment overdue beyond grace period',
      triggeredBy
    )
    return { success: result.success, error: result.error }
  }

  /**
   * Get status restrictions for a given status
   */
  private static getStatusRestrictions(status: SubscriptionStatus): string[] {
    const restrictions: Record<SubscriptionStatus, string[]> = {
      pending: ['No billing until activated', 'Limited access to services'],
      active: [],
      overdue: ['Service access may be limited', 'Late payment penalties apply'],
      suspended: ['Service access blocked', 'No new billing cycles'],
      cancelled: ['No service access', 'No billing', 'Cannot be reactivated'],
      expired: ['Service access blocked', 'Requires renewal']
    }

    return restrictions[status] || []
  }

  /**
   * Validate if a status transition is allowed
   */
  private static async validateTransition(
    subscriptionId: string,
    fromStatus: SubscriptionStatus,
    toStatus: SubscriptionStatus
  ): Promise<{ valid: boolean; error?: string }> {
    // Custom validation logic based on business rules
    
    if (toStatus === 'active') {
      // Check if there are outstanding payments for reactivation
      const [outstandingInvoices] = await db.select({ count: sql<number>`COUNT(*)` })
        .from(invoices)
        .leftJoin(subscriptions, eq(invoices.clientId, subscriptions.clientId))
        .where(and(
          eq(subscriptions.id, subscriptionId),
          sql`${invoices.status} IN ('overdue')`
        ))

      if (outstandingInvoices.count > 0 && fromStatus === 'suspended') {
        return {
          valid: false,
          error: 'Cannot reactivate subscription with outstanding overdue payments'
        }
      }
    }

    if (toStatus === 'cancelled' && fromStatus === 'active') {
      // Could add validation for active billing cycles, etc.
    }

    return { valid: true }
  }

  /**
   * Perform actions after status transition
   */
  private static async performPostTransitionActions(
    subscriptionId: string,
    fromStatus: SubscriptionStatus,
    toStatus: SubscriptionStatus,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      // Update related billing cycles if needed
      if (toStatus === 'suspended' || toStatus === 'cancelled') {
        await db.update(billingCycles)
          .set({ status: 'cancelled' })
          .where(and(
            eq(billingCycles.subscriptionId, subscriptionId),
            sql`${billingCycles.status} IN ('pending', 'active')`
          ))
      }

      // Send notifications (implement based on your notification system)
      // await this.sendStatusChangeNotification(subscriptionId, fromStatus, toStatus)

      // Update client access permissions if needed
      // await this.updateClientAccess(subscriptionId, toStatus)

    } catch (error) {
      console.error('Error in post-transition actions:', error)
      // Don't throw here to avoid rolling back the main transition
    }
  }

  /**
   * Get subscription renewal information
   */
  static async getRenewalInfo(subscriptionId: string): Promise<{
    isEligible: boolean
    nextRenewalDate: Date
    renewalAmount: number
    restrictions: string[]
  } | null> {
    try {
      const statusInfo = await this.getSubscriptionStatusInfo(subscriptionId)
      if (!statusInfo) return null

      const isEligible = ['active', 'expired'].includes(statusInfo.currentStatus)
      const nextRenewalDate = new Date(statusInfo.nextBillingDate)
      
      // Get renewal amount from subscription
      const [subscription] = await db.select({
        monthlyAmount: subscriptions.monthlyAmount,
        billingCycle: subscriptions.billingCycle
      })
      .from(subscriptions)
      .where(eq(subscriptions.id, subscriptionId))
      .limit(1)

      const renewalAmount = subscription ? parseFloat(subscription.monthlyAmount) : 0
      const restrictions = isEligible ? [] : ['Subscription must be active or expired to renew']

      return {
        isEligible,
        nextRenewalDate,
        renewalAmount,
        restrictions
      }

    } catch (error) {
      console.error('Error getting renewal info:', error)
      return null
    }
  }
}
