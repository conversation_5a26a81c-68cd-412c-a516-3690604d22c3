import { db } from '@/src/db'
import { auditLogs } from '@/src/db/schema'

export interface AuditLogEntry {
  userId?: string
  adminId?: string
  clientId?: string
  action: string
  resource: string
  resourceId?: string
  details?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  success: boolean
  errorMessage?: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  category: 'auth' | 'admin' | 'payment' | 'data' | 'security' | 'system'
}

export interface SecurityEvent {
  type: 'login_attempt' | 'failed_login' | 'suspicious_activity' | 'rate_limit_exceeded' | 'unauthorized_access' | 'data_access' | 'payment_activity' | 'system_error' | 'billing_system_error' | 'billing_critical_alert'
  userId?: string
  adminId?: string
  ipAddress: string
  userAgent?: string
  details: Record<string, any>
  severity: 'low' | 'medium' | 'high' | 'critical'
  timestamp?: Date
}

class AuditLogger {
  private static instance: AuditLogger
  private logQueue: AuditLogEntry[] = []
  private isProcessing = false

  private constructor() {
    // Process log queue every 5 seconds
    setInterval(() => {
      this.processLogQueue()
    }, 5000)
  }

  static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger()
    }
    return AuditLogger.instance
  }

  /**
   * Log an audit event
   */
  async log(entry: AuditLogEntry): Promise<void> {
    try {
      const logEntry: AuditLogEntry = {
        ...entry,
        details: entry.details
      }

      // Add to queue for batch processing
      this.logQueue.push(logEntry)

      // For critical events, log immediately
      if (entry.severity === 'critical') {
        await this.processLogQueue()
      }

    } catch (error) {
      console.error('Failed to queue audit log:', error)
    }
  }

  /**
   * Log authentication events
   */
  async logAuth(type: 'login' | 'logout' | 'failed_login' | 'password_change', data: {
    userId?: string
    adminId?: string
    email: string
    ipAddress?: string
    userAgent?: string
    success: boolean
    errorMessage?: string
  }): Promise<void> {
    await this.log({
      userId: data.userId,
      adminId: data.adminId,
      action: type,
      resource: 'authentication',
      details: {
        email: data.email,
        timestamp: new Date().toISOString()
      },
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      success: data.success,
      errorMessage: data.errorMessage,
      severity: data.success ? 'low' : 'medium',
      category: 'auth'
    })
  }

  /**
   * Log admin actions
   */
  async logAdmin(action: string, data: {
    adminId: string
    resource: string
    resourceId?: string
    details?: Record<string, any>
    ipAddress?: string
    userAgent?: string
    success: boolean
    errorMessage?: string
  }): Promise<void> {
    await this.log({
      adminId: data.adminId,
      action,
      resource: data.resource,
      resourceId: data.resourceId,
      details: data.details,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      success: data.success,
      errorMessage: data.errorMessage,
      severity: 'medium',
      category: 'admin'
    })
  }

  /**
   * Log payment activities
   */
  async logPayment(action: string, data: {
    userId?: string
    clientId?: string
    amount?: number
    currency?: string
    paymentId?: string
    orderId?: string
    ipAddress?: string
    success: boolean
    errorMessage?: string
  }): Promise<void> {
    await this.log({
      userId: data.userId,
      clientId: data.clientId,
      action,
      resource: 'payment',
      resourceId: data.paymentId || data.orderId,
      details: {
        amount: data.amount,
        currency: data.currency,
        timestamp: new Date().toISOString()
      },
      ipAddress: data.ipAddress,
      success: data.success,
      errorMessage: data.errorMessage,
      severity: 'high',
      category: 'payment'
    })
  }

  /**
   * Log security events
   */
  async logSecurity(event: SecurityEvent): Promise<void> {
    await this.log({
      userId: event.userId,
      adminId: event.adminId,
      action: event.type,
      resource: 'security',
      details: event.details,
      ipAddress: event.ipAddress,
      userAgent: event.userAgent,
      success: false, // Security events are typically failures/alerts
      severity: event.severity,
      category: 'security'
    })
  }

  /**
   * Log data access events
   */
  async logDataAccess(action: string, data: {
    userId?: string
    adminId?: string
    resource: string
    resourceId?: string
    ipAddress?: string
    userAgent?: string
    success: boolean
  }): Promise<void> {
    await this.log({
      userId: data.userId,
      adminId: data.adminId,
      action,
      resource: data.resource,
      resourceId: data.resourceId,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      success: data.success,
      severity: 'low',
      category: 'data'
    })
  }

  /**
   * Process the log queue and write to database
   */
  private async processLogQueue(): Promise<void> {
    if (this.isProcessing || this.logQueue.length === 0) {
      return
    }

    this.isProcessing = true

    try {
      const logsToProcess = [...this.logQueue]
      this.logQueue = []

      if (logsToProcess.length > 0) {
        await db.insert(auditLogs).values(logsToProcess.map(log => ({
          ...log,
          timestamp: new Date()
        })))

        console.log(`Processed ${logsToProcess.length} audit log entries`)
      }

    } catch (error) {
      console.error('Failed to process audit logs:', error)
      // Re-add failed logs to queue for retry
      this.logQueue.unshift(...this.logQueue)
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * Get audit logs with filtering
   */
  async getLogs(filters: {
    userId?: string
    adminId?: string
    action?: string
    resource?: string
    category?: string
    severity?: string
    startDate?: Date
    endDate?: Date
    limit?: number
    offset?: number
  } = {}): Promise<any[]> {
    try {
      // This would be implemented with proper filtering
      // For now, return basic structure
      return []
    } catch (error) {
      console.error('Failed to get audit logs:', error)
      return []
    }
  }
}

export const auditLogger = AuditLogger.getInstance()
