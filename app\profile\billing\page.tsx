'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  CreditCard, 
  Download, 
  Calendar, 
  IndianRupee,
  Clock,
  CheckCircle,
  AlertCircle,
  Receipt
} from 'lucide-react'

interface Invoice {
  id: string
  invoiceNumber: string
  amount: string
  taxAmount: string
  totalAmount: string
  status: string
  issuedDate: string
  dueDate: string
  billingCycle?: {
    cycleStart: string
    cycleEnd: string
    studentCount: number
  }
}

interface Payment {
  id: string
  amount: string
  currency: string
  status: string
  paymentMethod: string
  processedAt: string
  invoice: {
    invoiceNumber: string
    issuedDate: string
    totalAmount: string
  }
}

export default function SchoolBillingPage() {
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [payments, setPayments] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)
  const [paymentLoading, setPaymentLoading] = useState<string | null>(null)
  const [clientEmail, setClientEmail] = useState('')

  useEffect(() => {
    // Get client email from localStorage or auth context
    const email = localStorage.getItem('clientEmail') || '<EMAIL>'
    setClientEmail(email)
    fetchInvoices(email)
    fetchPaymentHistory(email)
  }, [])

  const fetchInvoices = async (email: string) => {
    try {
      const response = await fetch(`/api/payments/invoices/${email}`)
      if (response.ok) {
        const data = await response.json()
        setInvoices(data.invoices || [])
      }
    } catch (error) {
      console.error('Error fetching invoices:', error)
    }
  }

  const fetchPaymentHistory = async (email: string) => {
    try {
      const response = await fetch(`/api/payments/history/${email}`)
      if (response.ok) {
        const data = await response.json()
        setPayments(data.payments || [])
      }
    } catch (error) {
      console.error('Error fetching payment history:', error)
    } finally {
      setLoading(false)
    }
  }

  const handlePayment = async (invoice: Invoice) => {
    setPaymentLoading(invoice.id)

    try {
      // Create Razorpay order
      const orderResponse = await fetch('/api/payments/create-order', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          invoiceId: invoice.id,
          amount: parseFloat(invoice.totalAmount),
          currency: 'INR',
          clientEmail
        })
      })

      if (!orderResponse.ok) {
        throw new Error('Failed to create payment order')
      }

      const orderData = await orderResponse.json()

      // Initialize Razorpay payment
      const options = {
        key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
        amount: orderData.order.amount,
        currency: orderData.order.currency,
        name: 'Schopio',
        description: `Payment for Invoice ${invoice.invoiceNumber}`,
        order_id: orderData.order.id,
        handler: async (response: any) => {
          // Verify payment
          const verifyResponse = await fetch('/api/payments/verify', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              razorpayOrderId: response.razorpay_order_id,
              razorpayPaymentId: response.razorpay_payment_id,
              razorpaySignature: response.razorpay_signature,
              invoiceId: invoice.id,
              clientEmail
            })
          })

          if (verifyResponse.ok) {
            alert('Payment successful!')
            fetchInvoices(clientEmail)
            fetchPaymentHistory(clientEmail)
          } else {
            alert('Payment verification failed')
          }
        },
        prefill: {
          email: clientEmail,
          contact: '9999999999'
        },
        theme: {
          color: '#2563eb'
        }
      }

      const razorpay = new (window as any).Razorpay(options)
      razorpay.open()

    } catch (error) {
      console.error('Payment error:', error)
      alert('Payment failed. Please try again.')
    } finally {
      setPaymentLoading(null)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-100 text-green-800">Paid</Badge>
      case 'sent':
        return <Badge className="bg-blue-100 text-blue-800">Sent</Badge>
      case 'overdue':
        return <Badge className="bg-red-100 text-red-800">Overdue</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatAmount = (amount: string) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(parseFloat(amount))
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Billing & Payments</h1>
        <Button variant="outline">
          <Download className="h-4 w-4 mr-2" />
          Download Statement
        </Button>
      </div>

      {/* Billing Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Outstanding</CardTitle>
            <IndianRupee className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatAmount(
                invoices
                  .filter(i => ['sent', 'overdue'].includes(i.status))
                  .reduce((sum, i) => sum + parseFloat(i.totalAmount), 0)
                  .toString()
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              {invoices.filter(i => ['sent', 'overdue'].includes(i.status)).length} pending invoices
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatAmount(
                invoices
                  .filter(i => new Date(i.issuedDate).getMonth() === new Date().getMonth())
                  .reduce((sum, i) => sum + parseFloat(i.totalAmount), 0)
                  .toString()
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Current month charges
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Payment</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {payments.length > 0 ? formatAmount(payments[0].amount) : '₹0'}
            </div>
            <p className="text-xs text-muted-foreground">
              {payments.length > 0 ? formatDate(payments[0].processedAt) : 'No payments yet'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Invoices */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Invoices</CardTitle>
          <CardDescription>
            Your latest billing statements and payment status
          </CardDescription>
        </CardHeader>
        <CardContent>
          {invoices.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Receipt className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No invoices found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {invoices.slice(0, 5).map((invoice) => (
                <div key={invoice.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center space-x-4">
                      <div>
                        <p className="font-medium">{invoice.invoiceNumber}</p>
                        <p className="text-sm text-gray-500">
                          {formatDate(invoice.issuedDate)} • Due: {formatDate(invoice.dueDate)}
                        </p>
                        {invoice.billingCycle && (
                          <p className="text-xs text-gray-400">
                            {invoice.billingCycle.studentCount} students • 
                            {formatDate(invoice.billingCycle.cycleStart)} to {formatDate(invoice.billingCycle.cycleEnd)}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <p className="font-medium">{formatAmount(invoice.totalAmount)}</p>
                      <p className="text-sm text-gray-500">
                        Base: {formatAmount(invoice.amount)} + Tax: {formatAmount(invoice.taxAmount)}
                      </p>
                    </div>
                    
                    {getStatusBadge(invoice.status)}
                    
                    {invoice.status === 'sent' && (
                      <Button 
                        size="sm"
                        onClick={() => handlePayment(invoice)}
                        disabled={paymentLoading === invoice.id}
                      >
                        {paymentLoading === invoice.id ? (
                          <>
                            <Clock className="h-4 w-4 mr-2 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          <>
                            <CreditCard className="h-4 w-4 mr-2" />
                            Pay Now
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
