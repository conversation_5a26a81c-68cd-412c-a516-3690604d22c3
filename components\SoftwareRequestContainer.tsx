'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, RefreshCw, AlertCircle, CheckCircle } from 'lucide-react'
import SoftwareRequestForm from './SoftwareRequestForm'
import SoftwareRequestStatus from './SoftwareRequestStatus'
import UpgradeToProductionForm, { UpgradeFormData } from './UpgradeToProductionForm'

interface SoftwareRequestData {
  requestType: 'demo' | 'production'
  studentCount: number
  facultyCount: number
  completeAddress: string
  contactNumber: string
  primaryEmail: string
  class1Fee?: number
  class4Fee?: number
  class6Fee?: number
  class10Fee?: number
  class11Fee?: number
  class12Fee?: number
  class1112Fee?: number // Legacy field for backward compatibility
  termsAccepted?: boolean
  termsVersion?: string
}

interface RequestStatus {
  hasRequest: boolean
  request?: any
  statusHistory?: any[]
  canUpgrade?: boolean
}



export default function SoftwareRequestContainer() {
  const [requestStatus, setRequestStatus] = useState<RequestStatus | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [showUpgradeForm, setShowUpgradeForm] = useState(false)

  useEffect(() => {
    fetchRequestStatus()
  }, [])

  const fetchRequestStatus = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const token = localStorage.getItem('authToken')
      if (!token) {
        setError('Please log in to access this feature')
        return
      }

      const response = await fetch('/api/auth/software-request/status', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        if (response.status === 401) {
          setError('Session expired. Please log in again.')
          return
        }
        throw new Error('Failed to fetch request status')
      }

      const data = await response.json()
      setRequestStatus(data)
    } catch (error: any) {
      console.error('Error fetching request status:', error)
      setError(error.message || 'Failed to load request status')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmitRequest = async (data: SoftwareRequestData) => {
    try {
      setIsSubmitting(true)
      setError(null)
      setSuccess(null)

      const token = localStorage.getItem('authToken')
      if (!token) {
        throw new Error('Please log in to submit a request')
      }

      const response = await fetch('/api/auth/software-request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to submit request')
      }

      setSuccess(`${data.requestType === 'demo' ? 'Demo' : 'Production'} request submitted successfully!`)

      // Refresh the status after successful submission
      setTimeout(() => {
        fetchRequestStatus()
      }, 1000)

    } catch (error: any) {
      console.error('Error submitting request:', error)
      setError(error.message || 'Failed to submit request')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleUpgradeToProduction = async (data: UpgradeFormData) => {
    try {
      setIsSubmitting(true)
      setError(null)
      setSuccess(null)

      const token = localStorage.getItem('authToken')
      if (!token) {
        throw new Error('Please log in to upgrade your request')
      }

      const response = await fetch('/api/auth/software-request/upgrade-to-production', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to upgrade to production')
      }

      const result = await response.json()
      setSuccess(result.message || 'Successfully upgraded to production!')
      setShowUpgradeForm(false)

      // Refresh the status after successful upgrade
      setTimeout(() => {
        fetchRequestStatus()
      }, 1000)

    } catch (error: any) {
      console.error('Error upgrading to production:', error)
      setError(error.message || 'Failed to upgrade to production')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-gray-600">Loading request status...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="py-12">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <div className="flex justify-center mt-4">
            <Button onClick={fetchRequestStatus} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* Success Message */}
      {success && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
        >
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              {success}
            </AlertDescription>
          </Alert>
        </motion.div>
      )}

      {/* Error Message */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
        >
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </motion.div>
      )}

      {/* Main Content */}
      {showUpgradeForm ? (
        <UpgradeToProductionForm
          onSubmit={handleUpgradeToProduction}
          onCancel={() => setShowUpgradeForm(false)}
          isLoading={isSubmitting}
        />
      ) : requestStatus?.hasRequest ? (
        <div className="space-y-6">
          <SoftwareRequestStatus
            request={requestStatus.request}
            statusHistory={requestStatus.statusHistory || []}
            canUpgrade={requestStatus.canUpgrade}
            onUpgrade={() => setShowUpgradeForm(true)}
          />

          {/* Refresh Button */}
          <div className="flex justify-center">
            <Button
              onClick={fetchRequestStatus}
              variant="outline"
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh Status
            </Button>
          </div>
        </div>
      ) : (
        <SoftwareRequestForm
          onSubmit={handleSubmitRequest}
          isLoading={isSubmitting}
        />
      )}
    </div>
  )
}
