'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import {
  User,
  Mail,
  Phone,
  MapPin,
  Users,
  School,
  LogOut,
  Settings,
  Shield,
  Save,
  Edit,
  Eye,
  EyeOff,
  DollarSign,
  ArrowLeft,
  CheckCircle,
  AlertCircle,
  Gift,
  FileText
} from 'lucide-react'
import Link from 'next/link'
import SoftwareRequestContainer from '@/components/SoftwareRequestContainer'

interface UserData {
  id: string
  email: string
  name: string
  role: string
  emailVerified: boolean
  lastLogin: string
  createdAt: string
}

interface ClientData {
  id: string
  schoolName: string
  schoolCode: string
  phone: string
  address: string
  contactPerson: string
  actualStudentCount: number
  classFee: number | null
  onboardingStatus: string
  status: string
  createdAt: string
}

interface ProfileFormData {
  name: string
  phone: string
}

interface SchoolFormData {
  schoolName: string
  address: string
  contactPerson: string
  actualStudentCount: number
  classFee: number | string
}

interface PasswordFormData {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

interface ReferralData {
  hasReferral: boolean
  referral?: {
    code: string
    partnerName: string
    partnerCompany: string
    appliedAt: string
    source: string
    isVerified: boolean
    verifiedAt?: string
  }
}

const ProfileSettingsPage = () => {
  const [user, setUser] = useState<UserData | null>(null)
  const [client, setClient] = useState<ClientData | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'profile' | 'school' | 'security' | 'referral' | 'software'>('profile')
  
  // Form states
  const [profileForm, setProfileForm] = useState<ProfileFormData>({ name: '', phone: '' })
  const [schoolForm, setSchoolForm] = useState<SchoolFormData>({
    schoolName: '',
    address: '',
    contactPerson: '',
    actualStudentCount: 0,
    classFee: ''
  })
  const [passwordForm, setPasswordForm] = useState<PasswordFormData>({ 
    currentPassword: '', 
    newPassword: '', 
    confirmPassword: '' 
  })
  
  // UI states
  const [editingProfile, setEditingProfile] = useState(false)
  const [editingSchool, setEditingSchool] = useState(false)
  const [showPasswords, setShowPasswords] = useState({ current: false, new: false, confirm: false })
  const [referralData, setReferralData] = useState<ReferralData>({ hasReferral: false })
  const [referralCode, setReferralCode] = useState('')
  const [validatingReferral, setValidatingReferral] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  const fetchProfileData = useCallback(async (token: string) => {
    try {
      const response = await fetch('/api/auth/profile', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        if (response.status === 401) {
          handleLogout()
          return
        }
        throw new Error('Failed to fetch profile data')
      }

      const data = await response.json()
      
      setUser(data.user)
      setClient(data.client)
      
      // Initialize form data
      setProfileForm({
        name: data.user.name || '',
        phone: data.client.phone || ''
      })
      
      setSchoolForm({
        schoolName: data.client.schoolName || '',
        address: data.client.address || '',
        contactPerson: data.client.contactPerson || '',
        actualStudentCount: data.client.actualStudentCount || 0,
        classFee: data.client.classFee || ''
      })
      
      localStorage.setItem('user', JSON.stringify(data.user))

      // Fetch referral status
      await fetchReferralStatus()
    } catch (error) {
      console.error('Error fetching profile data:', error)
      setMessage({ type: 'error', text: 'Failed to load profile data' })
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    // Check if user is authenticated
    const token = localStorage.getItem('authToken')
    const userData = localStorage.getItem('user')

    if (!token || !userData) {
      window.location.href = '/auth'
      return
    }

    try {
      const parsedUser = JSON.parse(userData)
      setUser(parsedUser)

      // Fetch complete profile data
      fetchProfileData(token)
    } catch (error) {
      console.error('Error parsing user data:', error)
      handleLogout()
    }
  }, [fetchProfileData])

  const fetchReferralStatus = async () => {
    try {
      const token = localStorage.getItem('authToken')
      const response = await fetch('/api/referral/status', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setReferralData(data)
      }
    } catch (error) {
      console.error('Error fetching referral status:', error)
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('authToken')
    localStorage.removeItem('user')
    window.location.href = '/auth'
  }

  const showMessage = (type: 'success' | 'error', text: string) => {
    setMessage({ type, text })
    setTimeout(() => setMessage(null), 5000)
  }

  const validateReferralCode = async (code: string) => {
    if (!code.trim()) return null

    setValidatingReferral(true)
    try {
      const response = await fetch('/api/referral/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ code: code.trim() })
      })

      const data = await response.json()
      return data
    } catch (error) {
      console.error('Error validating referral code:', error)
      return { valid: false, error: 'Failed to validate referral code' }
    } finally {
      setValidatingReferral(false)
    }
  }

  const applyReferralCode = async () => {
    if (!referralCode.trim()) {
      showMessage('error', 'Please enter a referral code')
      return
    }

    // First validate the code
    const validation = await validateReferralCode(referralCode)
    if (!validation?.valid) {
      showMessage('error', validation?.error || 'Invalid referral code')
      return
    }

    setSubmitting(true)
    try {
      const token = localStorage.getItem('authToken')
      const response = await fetch('/api/referral/apply', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ code: referralCode.trim() })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to apply referral code')
      }

      showMessage('success', 'Referral code applied successfully!')
      setReferralCode('')
      await fetchReferralStatus() // Refresh referral status
    } catch (error: any) {
      showMessage('error', error.message)
    } finally {
      setSubmitting(false)
    }
  }

  // Validation functions
  const validateProfileForm = () => {
    if (!profileForm.name.trim()) {
      showMessage('error', 'Name is required')
      return false
    }
    if (profileForm.name.trim().length < 2) {
      showMessage('error', 'Name must be at least 2 characters')
      return false
    }
    if (profileForm.phone && profileForm.phone.length < 10) {
      showMessage('error', 'Phone number must be at least 10 digits')
      return false
    }
    return true
  }

  const validateSchoolForm = () => {
    if (!schoolForm.schoolName.trim()) {
      showMessage('error', 'School name is required')
      return false
    }
    if (schoolForm.schoolName.trim().length < 2) {
      showMessage('error', 'School name must be at least 2 characters')
      return false
    }
    if (!schoolForm.address.trim()) {
      showMessage('error', 'Address is required')
      return false
    }
    if (schoolForm.address.trim().length < 10) {
      showMessage('error', 'Address must be at least 10 characters')
      return false
    }
    if (!schoolForm.contactPerson.trim()) {
      showMessage('error', 'Contact person is required')
      return false
    }
    if (schoolForm.contactPerson.trim().length < 2) {
      showMessage('error', 'Contact person name must be at least 2 characters')
      return false
    }
    if (schoolForm.actualStudentCount < 1) {
      showMessage('error', 'Student count must be at least 1')
      return false
    }
    if (schoolForm.classFee && (parseFloat(schoolForm.classFee.toString()) < 0)) {
      showMessage('error', 'Class fee must be a positive number')
      return false
    }
    return true
  }

  const validatePasswordForm = () => {
    if (!passwordForm.currentPassword) {
      showMessage('error', 'Current password is required')
      return false
    }
    if (!passwordForm.newPassword) {
      showMessage('error', 'New password is required')
      return false
    }
    if (passwordForm.newPassword.length < 8) {
      showMessage('error', 'New password must be at least 8 characters')
      return false
    }
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      showMessage('error', 'New passwords do not match')
      return false
    }
    if (passwordForm.currentPassword === passwordForm.newPassword) {
      showMessage('error', 'New password must be different from current password')
      return false
    }
    return true
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-emerald-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading profile...</p>
        </div>
      </div>
    )
  }

  if (!user || !client) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-emerald-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <p className="text-gray-600">Failed to load profile data</p>
          <Button onClick={() => window.location.reload()} className="mt-4">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-emerald-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold">
                <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent">
                  Schopio
                </span>
              </Link>
              <span className="ml-4 text-gray-500">|</span>
              <Link href="/profile" className="ml-4 text-gray-700 hover:text-blue-600 flex items-center gap-2">
                <ArrowLeft className="w-4 h-4" />
                Back to Profile
              </Link>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">{user.name}</p>
                <p className="text-xs text-gray-500 capitalize">{user.role}</p>
              </div>
              <Button
                onClick={handleLogout}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <LogOut className="w-4 h-4" />
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Page Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center gap-3">
              <Settings className="w-8 h-8" />
              Account Settings
            </h1>
            <p className="text-gray-600">
              Manage your personal information, school details, and security settings.
            </p>
          </div>

          {/* Success/Error Messages */}
          {message && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className={`mb-6 p-4 rounded-lg flex items-center gap-3 ${
                message.type === 'success' 
                  ? 'bg-green-50 text-green-800 border border-green-200' 
                  : 'bg-red-50 text-red-800 border border-red-200'
              }`}
            >
              {message.type === 'success' ? (
                <CheckCircle className="w-5 h-5" />
              ) : (
                <AlertCircle className="w-5 h-5" />
              )}
              <span>{message.text}</span>
            </motion.div>
          )}

          {/* Tab Navigation */}
          <div className="mb-8">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                {[
                  { id: 'profile', label: 'Personal Info', icon: User },
                  { id: 'school', label: 'School Details', icon: School },
                  { id: 'security', label: 'Security', icon: Shield },
                  { id: 'referral', label: 'Referral Code', icon: Gift },
                  { id: 'software', label: 'Software Request', icon: FileText }
                ].map(({ id, label, icon: Icon }) => (
                  <button
                    key={id}
                    onClick={() => setActiveTab(id as any)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                      activeTab === id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {label}
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Tab Content */}
          <div className="space-y-6">
            {/* Personal Information Tab */}
            {activeTab === 'profile' && (
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                      <User className="w-5 h-5" />
                      Personal Information
                    </h2>
                    <Button
                      onClick={() => setEditingProfile(!editingProfile)}
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      <Edit className="w-4 h-4" />
                      {editingProfile ? 'Cancel' : 'Edit'}
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  {editingProfile ? (
                    <ProfileEditForm
                      formData={profileForm}
                      setFormData={setProfileForm}
                      onSave={handleSaveProfile}
                      submitting={submitting}
                      onCancel={() => setEditingProfile(false)}
                    />
                  ) : (
                    <ProfileDisplayInfo user={user} client={client} />
                  )}
                </CardContent>
              </Card>
            )}

            {/* School Information Tab */}
            {activeTab === 'school' && (
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                      <School className="w-5 h-5" />
                      School Information
                    </h2>
                    <Button
                      onClick={() => setEditingSchool(!editingSchool)}
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      <Edit className="w-4 h-4" />
                      {editingSchool ? 'Cancel' : 'Edit'}
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  {editingSchool ? (
                    <SchoolEditForm
                      formData={schoolForm}
                      setFormData={setSchoolForm}
                      onSave={handleSaveSchool}
                      submitting={submitting}
                      onCancel={() => setEditingSchool(false)}
                    />
                  ) : (
                    <SchoolDisplayInfo client={client} />
                  )}
                </CardContent>
              </Card>
            )}

            {/* Security Tab */}
            {activeTab === 'security' && (
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                    <Shield className="w-5 h-5" />
                    Security Settings
                  </h2>
                </CardHeader>
                <CardContent>
                  <SecuritySettings
                    user={user}
                    passwordForm={passwordForm}
                    setPasswordForm={setPasswordForm}
                    showPasswords={showPasswords}
                    setShowPasswords={setShowPasswords}
                    onChangePassword={handleChangePassword}
                    onResendVerification={handleResendVerification}
                    submitting={submitting}
                  />
                </CardContent>
              </Card>
            )}

            {/* Referral Tab */}
            {activeTab === 'referral' && (
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                    <Gift className="w-5 h-5" />
                    Referral Code
                  </h2>
                  <p className="text-gray-600 text-sm">
                    Apply a referral code to get benefits and support your partner
                  </p>
                </CardHeader>
                <CardContent>
                  {referralData.hasReferral ? (
                    <div className="space-y-4">
                      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-2">
                          <CheckCircle className="w-5 h-5 text-green-600" />
                          <h3 className="font-medium text-green-800">Referral Code Applied</h3>
                        </div>
                        <div className="space-y-2 text-sm text-green-700">
                          <p><strong>Code:</strong> {referralData.referral?.code}</p>
                          <p><strong>Partner:</strong> {referralData.referral?.partnerName}</p>
                          {referralData.referral?.partnerCompany && (
                            <p><strong>Company:</strong> {referralData.referral?.partnerCompany}</p>
                          )}
                          <p><strong>Applied:</strong> {new Date(referralData.referral?.appliedAt || '').toLocaleDateString()}</p>
                          <p><strong>Source:</strong> {referralData.referral?.source === 'registration' ? 'During Registration' : 'Profile Update'}</p>
                          {referralData.referral?.isVerified ? (
                            <div className="flex items-center gap-1 text-green-600">
                              <CheckCircle className="w-4 h-4" />
                              <span>Verified by Admin</span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-1 text-yellow-600">
                              <AlertCircle className="w-4 h-4" />
                              <span>Pending Admin Verification</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h3 className="font-medium text-blue-800 mb-2">Apply Referral Code</h3>
                        <p className="text-sm text-blue-700 mb-4">
                          Enter a referral code from your partner to get special benefits and support.
                        </p>
                        <div className="space-y-3">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Referral Code
                            </label>
                            <input
                              type="text"
                              value={referralCode}
                              onChange={(e) => setReferralCode(e.target.value.toUpperCase())}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Enter referral code (e.g., ABC12345)"
                              maxLength={8}
                            />
                          </div>
                          <Button
                            onClick={applyReferralCode}
                            disabled={submitting || validatingReferral || !referralCode.trim()}
                            className="w-full"
                          >
                            {submitting ? 'Applying...' : validatingReferral ? 'Validating...' : 'Apply Referral Code'}
                          </Button>
                        </div>
                      </div>

                      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <h4 className="font-medium text-gray-800 mb-2">Benefits of Referral Codes</h4>
                        <ul className="text-sm text-gray-600 space-y-1">
                          <li>• Get dedicated support from your referring partner</li>
                          <li>• Access to exclusive features and benefits</li>
                          <li>• Priority customer service</li>
                          <li>• Special pricing and offers</li>
                        </ul>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Software Request Tab */}
            {activeTab === 'software' && (
              <div className="space-y-6">
                <Card className="border-0 shadow-lg">
                  <CardHeader>
                    <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                      <FileText className="w-5 h-5" />
                      Software Request
                    </h2>
                    <p className="text-gray-600 text-sm">
                      Request demo access or production subscription for Schopio School Management System
                    </p>
                  </CardHeader>
                  <CardContent>
                    <SoftwareRequestContainer />
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  )

  // Handler functions
  async function handleSaveProfile() {
    if (!validateProfileForm()) return

    setSubmitting(true)
    try {
      const token = localStorage.getItem('authToken')
      const response = await fetch('/api/auth/profile/user', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(profileForm)
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update profile')
      }

      setUser(data.user)
      localStorage.setItem('user', JSON.stringify(data.user))
      setEditingProfile(false)
      showMessage('success', 'Profile updated successfully!')
    } catch (error: any) {
      showMessage('error', error.message || 'Failed to update profile')
    } finally {
      setSubmitting(false)
    }
  }

  async function handleSaveSchool() {
    if (!validateSchoolForm()) return

    setSubmitting(true)
    try {
      const token = localStorage.getItem('authToken')
      const response = await fetch('/api/auth/profile/school', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(schoolForm)
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update school information')
      }

      setClient(data.client)
      setEditingSchool(false)
      showMessage('success', 'School information updated successfully!')
    } catch (error: any) {
      showMessage('error', error.message || 'Failed to update school information')
    } finally {
      setSubmitting(false)
    }
  }

  async function handleChangePassword() {
    if (!validatePasswordForm()) return

    setSubmitting(true)
    try {
      const token = localStorage.getItem('authToken')
      const response = await fetch('/api/auth/profile/password', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(passwordForm)
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to change password')
      }

      setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' })
      showMessage('success', 'Password changed successfully!')
    } catch (error: any) {
      showMessage('error', error.message || 'Failed to change password')
    } finally {
      setSubmitting(false)
    }
  }

  async function handleResendVerification() {
    setSubmitting(true)
    try {
      const token = localStorage.getItem('authToken')
      const response = await fetch('/api/auth/profile/resend-verification', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send verification email')
      }

      showMessage('success', 'Verification email sent! Please check your inbox.')
    } catch (error: any) {
      showMessage('error', error.message || 'Failed to send verification email')
    } finally {
      setSubmitting(false)
    }
  }
}

// Component for displaying profile information
const ProfileDisplayInfo = ({ user, client }: { user: UserData, client: ClientData }) => (
  <div className="space-y-4">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="flex items-center gap-3">
        <User className="w-5 h-5 text-gray-400" />
        <div>
          <p className="text-sm text-gray-600">Full Name</p>
          <p className="font-medium">{user.name}</p>
        </div>
      </div>
      <div className="flex items-center gap-3">
        <Mail className="w-5 h-5 text-gray-400" />
        <div>
          <p className="text-sm text-gray-600">Email Address</p>
          <p className="font-medium">{user.email}</p>
        </div>
      </div>
      <div className="flex items-center gap-3">
        <Phone className="w-5 h-5 text-gray-400" />
        <div>
          <p className="text-sm text-gray-600">Phone Number</p>
          <p className="font-medium">{client.phone || 'Not provided'}</p>
        </div>
      </div>
      <div className="flex items-center gap-3">
        <Shield className="w-5 h-5 text-gray-400" />
        <div>
          <p className="text-sm text-gray-600">Role</p>
          <p className="font-medium capitalize">{user.role}</p>
        </div>
      </div>
    </div>
  </div>
)

// Component for editing profile information
const ProfileEditForm = ({
  formData,
  setFormData,
  onSave,
  submitting,
  onCancel
}: {
  formData: ProfileFormData
  setFormData: (data: ProfileFormData) => void
  onSave: () => void
  submitting: boolean
  onCancel: () => void
}) => (
  <div className="space-y-4">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Full Name *
        </label>
        <input
          type="text"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter your full name"
          required
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Phone Number
        </label>
        <input
          type="tel"
          value={formData.phone}
          onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter phone number"
        />
      </div>
    </div>
    <div className="flex gap-3 pt-4">
      <Button
        onClick={onSave}
        disabled={submitting}
        className="flex items-center gap-2"
      >
        <Save className="w-4 h-4" />
        {submitting ? 'Saving...' : 'Save Changes'}
      </Button>
      <Button
        onClick={onCancel}
        variant="outline"
        disabled={submitting}
      >
        Cancel
      </Button>
    </div>
  </div>
)

// Component for displaying school information
const SchoolDisplayInfo = ({ client }: { client: ClientData }) => (
  <div className="space-y-4">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="flex items-center gap-3">
        <School className="w-5 h-5 text-gray-400" />
        <div>
          <p className="text-sm text-gray-600">School Name</p>
          <p className="font-medium">{client.schoolName}</p>
        </div>
      </div>
      <div className="flex items-center gap-3">
        <Settings className="w-5 h-5 text-gray-400" />
        <div>
          <p className="text-sm text-gray-600">School Code</p>
          <p className="font-medium">{client.schoolCode}</p>
        </div>
      </div>
      <div className="flex items-center gap-3">
        <MapPin className="w-5 h-5 text-gray-400" />
        <div>
          <p className="text-sm text-gray-600">Address</p>
          <p className="font-medium">{client.address}</p>
        </div>
      </div>
      <div className="flex items-center gap-3">
        <User className="w-5 h-5 text-gray-400" />
        <div>
          <p className="text-sm text-gray-600">Contact Person</p>
          <p className="font-medium">{client.contactPerson}</p>
        </div>
      </div>
      <div className="flex items-center gap-3">
        <Users className="w-5 h-5 text-gray-400" />
        <div>
          <p className="text-sm text-gray-600">Student Count</p>
          <p className="font-medium">{client.actualStudentCount} students</p>
        </div>
      </div>
      <div className="flex items-center gap-3">
        <DollarSign className="w-5 h-5 text-gray-400" />
        <div>
          <p className="text-sm text-gray-600">Class Fee (Per Student/Month)</p>
          <p className="font-medium">
            {client.classFee ? `₹${parseFloat(client.classFee.toString()).toLocaleString()}` : 'Not set'}
          </p>
        </div>
      </div>
    </div>
  </div>
)

// Component for editing school information
const SchoolEditForm = ({
  formData,
  setFormData,
  onSave,
  submitting,
  onCancel
}: {
  formData: SchoolFormData
  setFormData: (data: SchoolFormData) => void
  onSave: () => void
  submitting: boolean
  onCancel: () => void
}) => (
  <div className="space-y-4">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          School Name *
        </label>
        <input
          type="text"
          value={formData.schoolName}
          onChange={(e) => setFormData({ ...formData, schoolName: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter school name"
          required
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Contact Person *
        </label>
        <input
          type="text"
          value={formData.contactPerson}
          onChange={(e) => setFormData({ ...formData, contactPerson: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter contact person name"
          required
        />
      </div>
    </div>
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        School Address *
      </label>
      <textarea
        value={formData.address}
        onChange={(e) => setFormData({ ...formData, address: e.target.value })}
        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        placeholder="Enter complete school address"
        rows={3}
        required
      />
    </div>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Current Student Count *
        </label>
        <input
          type="number"
          value={formData.actualStudentCount}
          onChange={(e) => setFormData({ ...formData, actualStudentCount: parseInt(e.target.value) || 0 })}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Enter current number of students"
          min="1"
          required
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Class Fee (Per Student/Month)
        </label>
        <div className="relative">
          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">₹</span>
          <input
            type="number"
            value={formData.classFee}
            onChange={(e) => setFormData({ ...formData, classFee: e.target.value })}
            className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Enter monthly fee per student"
            min="0"
            step="0.01"
          />
        </div>
        <p className="text-xs text-gray-500 mt-1">
          Optional: Enter the monthly fee you charge per student
        </p>
      </div>
    </div>
    <div className="flex gap-3 pt-4">
      <Button
        onClick={onSave}
        disabled={submitting}
        className="flex items-center gap-2"
      >
        <Save className="w-4 h-4" />
        {submitting ? 'Saving...' : 'Save Changes'}
      </Button>
      <Button
        onClick={onCancel}
        variant="outline"
        disabled={submitting}
      >
        Cancel
      </Button>
    </div>
  </div>
)

// Component for security settings
const SecuritySettings = ({
  user,
  passwordForm,
  setPasswordForm,
  showPasswords,
  setShowPasswords,
  onChangePassword,
  onResendVerification,
  submitting
}: {
  user: UserData
  passwordForm: PasswordFormData
  setPasswordForm: (data: PasswordFormData) => void
  showPasswords: { current: boolean, new: boolean, confirm: boolean }
  setShowPasswords: (data: { current: boolean, new: boolean, confirm: boolean }) => void
  onChangePassword: () => void
  onResendVerification: () => void
  submitting: boolean
}) => (
  <div className="space-y-6">
    {/* Account Information */}
    <div>
      <h3 className="text-lg font-medium text-gray-900 mb-4">Account Information</h3>
      <div className="bg-gray-50 p-4 rounded-lg space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Email Verification</span>
          <div className="flex items-center gap-2">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
              user.emailVerified
                ? 'bg-green-100 text-green-800'
                : 'bg-yellow-100 text-yellow-800'
            }`}>
              {user.emailVerified ? 'Verified' : 'Pending'}
            </span>
            {!user.emailVerified && (
              <Button
                onClick={onResendVerification}
                size="sm"
                variant="outline"
                disabled={submitting}
                className="text-xs"
              >
                {submitting ? 'Sending...' : 'Resend'}
              </Button>
            )}
          </div>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Last Login</span>
          <span className="text-sm text-gray-900">
            {new Date(user.lastLogin).toLocaleDateString()}
          </span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-600">Account Created</span>
          <span className="text-sm text-gray-900">
            {new Date(user.createdAt).toLocaleDateString()}
          </span>
        </div>
      </div>
    </div>

    {/* Change Password */}
    <div>
      <h3 className="text-lg font-medium text-gray-900 mb-4">Change Password</h3>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Current Password *
          </label>
          <div className="relative">
            <input
              type={showPasswords.current ? 'text' : 'password'}
              value={passwordForm.currentPassword}
              onChange={(e) => setPasswordForm({ ...passwordForm, currentPassword: e.target.value })}
              className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter current password"
              required
            />
            <button
              type="button"
              onClick={() => setShowPasswords({ ...showPasswords, current: !showPasswords.current })}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showPasswords.current ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              New Password *
            </label>
            <div className="relative">
              <input
                type={showPasswords.new ? 'text' : 'password'}
                value={passwordForm.newPassword}
                onChange={(e) => setPasswordForm({ ...passwordForm, newPassword: e.target.value })}
                className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter new password"
                required
              />
              <button
                type="button"
                onClick={() => setShowPasswords({ ...showPasswords, new: !showPasswords.new })}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                {showPasswords.new ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Confirm New Password *
            </label>
            <div className="relative">
              <input
                type={showPasswords.confirm ? 'text' : 'password'}
                value={passwordForm.confirmPassword}
                onChange={(e) => setPasswordForm({ ...passwordForm, confirmPassword: e.target.value })}
                className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Confirm new password"
                required
              />
              <button
                type="button"
                onClick={() => setShowPasswords({ ...showPasswords, confirm: !showPasswords.confirm })}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                {showPasswords.confirm ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
          </div>
        </div>

        <Button
          onClick={onChangePassword}
          disabled={submitting || !passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword}
          className="flex items-center gap-2"
        >
          <Shield className="w-4 h-4" />
          {submitting ? 'Changing Password...' : 'Change Password'}
        </Button>
      </div>
    </div>
  </div>
)

export default ProfileSettingsPage
