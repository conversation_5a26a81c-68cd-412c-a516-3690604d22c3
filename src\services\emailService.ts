import { Resend } from 'resend'
import { pdfInvoiceService } from './pdfInvoiceService'

interface EmailOptions {
  to: string | string[]
  subject: string
  html: string
  from?: string
  replyTo?: string
  attachments?: Array<{
    filename: string
    content: Buffer | string
    contentType?: string
  }>
}

interface BillingEmailData {
  schoolName: string | null
  contactPerson: string | null
  email: string | null
  invoiceNumber: string
  amount: string
  dueDate: string
  paymentUrl?: string
  receiptUrl?: string
  daysOverdue?: number
  penaltyAmount?: string
}

class EmailService {
  private resend: Resend
  private defaultFrom: string

  constructor() {
    this.resend = new Resend(process.env.RESEND_API_KEY)
    this.defaultFrom = `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`
  }

  /**
   * Send a basic email
   */
  async sendEmail(options: EmailOptions) {
    try {
      const result = await this.resend.emails.send({
        from: options.from || this.defaultFrom,
        to: options.to,
        subject: options.subject,
        html: options.html,
        replyTo: options.replyTo || process.env.FROM_EMAIL,
        attachments: options.attachments
      })

      console.log('Email sent successfully:', result.data?.id)
      return { success: true, id: result.data?.id }
    } catch (error) {
      console.error('Failed to send email:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  /**
   * Send payment reminder email (3 days before due date)
   */
  async sendPaymentReminder(data: BillingEmailData) {
    if (!data.email) {
      return { success: false, error: 'No email address provided' }
    }

    const html = this.getPaymentReminderTemplate(data)

    return this.sendEmail({
      to: data.email,
      subject: `Payment Reminder - Invoice ${data.invoiceNumber} Due Soon`,
      html
    })
  }

  /**
   * Send overdue payment notice
   */
  async sendOverdueNotice(data: BillingEmailData) {
    if (!data.email) {
      return { success: false, error: 'No email address provided' }
    }

    const html = this.getOverdueNoticeTemplate(data)

    return this.sendEmail({
      to: data.email,
      subject: `URGENT: Overdue Payment - Invoice ${data.invoiceNumber}`,
      html
    })
  }

  /**
   * Send invoice generated notification
   */
  async sendInvoiceGenerated(data: BillingEmailData) {
    if (!data.email) {
      return { success: false, error: 'No email address provided' }
    }

    const html = this.getInvoiceGeneratedTemplate(data)

    return this.sendEmail({
      to: data.email,
      subject: `New Invoice Generated - ${data.invoiceNumber}`,
      html
    })
  }

  /**
   * Send payment confirmation email
   */
  async sendPaymentConfirmation(data: BillingEmailData) {
    if (!data.email) {
      return { success: false, error: 'No email address provided' }
    }

    const html = this.getPaymentConfirmationTemplate(data)

    return this.sendEmail({
      to: data.email,
      subject: `Payment Received - Invoice ${data.invoiceNumber}`,
      html
    })
  }

  /**
   * Send final notice before suspension
   */
  async sendFinalNotice(data: BillingEmailData) {
    if (!data.email) {
      return { success: false, error: 'No email address provided' }
    }

    const html = this.getFinalNoticeTemplate(data)

    return this.sendEmail({
      to: data.email,
      subject: `FINAL NOTICE: Account Suspension Warning - Invoice ${data.invoiceNumber}`,
      html
    })
  }

  /**
   * Send invoice email with PDF attachment
   */
  async sendInvoiceWithPDF(data: BillingEmailData & { invoiceId: string }) {
    if (!data.email) {
      return { success: false, error: 'No email address provided' }
    }

    try {
      // Generate PDF invoice
      const pdfResult = await pdfInvoiceService.generateInvoicePDF(data.invoiceId)

      if (!pdfResult.success) {
        console.error('Failed to generate PDF for email attachment:', pdfResult.error)
        // Fall back to sending email without attachment
        return this.sendInvoiceGenerated(data)
      }

      const html = this.getInvoiceGeneratedTemplate(data)

      return this.sendEmail({
        to: data.email,
        subject: `Invoice ${data.invoiceNumber} - ${data.schoolName}`,
        html,
        attachments: [{
          filename: pdfResult.fileName || `invoice-${data.invoiceNumber}.pdf`,
          content: pdfResult.pdfBuffer!,
          contentType: 'application/pdf'
        }]
      })
    } catch (error) {
      console.error('Error sending invoice with PDF:', error)
      // Fall back to sending email without attachment
      return this.sendInvoiceGenerated(data)
    }
  }

  /**
   * Send payment confirmation email with invoice PDF attachment
   */
  async sendPaymentConfirmationWithPDF(data: BillingEmailData & { invoiceId: string }) {
    if (!data.email) {
      return { success: false, error: 'No email address provided' }
    }

    try {
      // Generate PDF invoice (now marked as paid)
      const pdfResult = await pdfInvoiceService.generateInvoicePDF(data.invoiceId)

      if (!pdfResult.success) {
        console.error('Failed to generate PDF for payment confirmation:', pdfResult.error)
        // Fall back to sending email without attachment
        return this.sendPaymentConfirmation(data)
      }

      const html = this.getPaymentConfirmationTemplate(data)

      return this.sendEmail({
        to: data.email,
        subject: `Payment Confirmed - Invoice ${data.invoiceNumber}`,
        html,
        attachments: [{
          filename: pdfResult.fileName || `receipt-${data.invoiceNumber}.pdf`,
          content: pdfResult.pdfBuffer!,
          contentType: 'application/pdf'
        }]
      })
    } catch (error) {
      console.error('Error sending payment confirmation with PDF:', error)
      // Fall back to sending email without attachment
      return this.sendPaymentConfirmation(data)
    }
  }

  /**
   * Get base email template wrapper
   */
  private getBaseTemplate(content: string): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Schopio</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f9fafb; }
        .email-container { max-width: 600px; margin: 0 auto; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
        .email-header { background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white; padding: 30px 20px; text-align: center; }
        .email-body { padding: 30px 20px; }
        .email-footer { background: #f3f4f6; padding: 20px; text-align: center; border-top: 1px solid #e5e7eb; }
        .btn { display: inline-block; padding: 12px 30px; background: #2563eb; color: white; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 20px 0; }
        .btn-warning { background: #f59e0b; }
        .btn-danger { background: #ef4444; }
        .btn-success { background: #10b981; }
        .invoice-details { background: #f0f9ff; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2563eb; }
        .warning-box { background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b; }
        .danger-box { background: #fee2e2; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ef4444; }
        .amount { font-size: 24px; font-weight: bold; color: #2563eb; }
        .overdue-amount { color: #ef4444; }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <h1 style="margin: 0; font-size: 28px;">Schopio</h1>
            <p style="margin: 5px 0 0 0; opacity: 0.9;">School Management Platform</p>
        </div>
        <div class="email-body">
            ${content}
        </div>
        <div class="email-footer">
            <p style="color: #6b7280; font-size: 14px; margin: 0;">
                © 2025 Schopio. All rights reserved.
            </p>
            <p style="color: #6b7280; font-size: 12px; margin: 10px 0 0 0;">
                <a href="mailto:${process.env.FROM_EMAIL}" style="color: #2563eb;">Support</a> | 
                <a href="#" style="color: #2563eb;">Privacy Policy</a>
            </p>
        </div>
    </div>
</body>
</html>`
  }

  /**
   * Payment reminder template (3 days before due)
   */
  private getPaymentReminderTemplate(data: BillingEmailData): string {
    const content = `
<h2 style="color: #f59e0b; margin-bottom: 20px;">Payment Due in 3 Days</h2>

<p>Dear ${data.contactPerson || 'Admin'},</p>

<p>This is a friendly reminder that your monthly subscription payment for <strong>${data.schoolName || 'your school'}</strong> is due in 3 days.</p>

<div class="invoice-details">
    <h3 style="color: #2563eb; margin-top: 0;">Invoice Details</h3>
    <p><strong>Invoice Number:</strong> ${data.invoiceNumber}</p>
    <p><strong>Amount:</strong> <span class="amount">₹${data.amount}</span></p>
    <p><strong>Due Date:</strong> ${data.dueDate}</p>
</div>

<p>To avoid any service interruption, please make your payment before the due date.</p>

${data.paymentUrl ? `
<div style="text-align: center; margin: 30px 0;">
    <a href="${data.paymentUrl}" class="btn btn-warning">Pay Now</a>
</div>
` : ''}

<p>If you have already made the payment, please ignore this reminder.</p>

<p>Best regards,<br>
The Schopio Team</p>`

    return this.getBaseTemplate(content)
  }

  /**
   * Overdue notice template
   */
  private getOverdueNoticeTemplate(data: BillingEmailData): string {
    const content = `
<h2 style="color: #ef4444; margin-bottom: 20px;">Payment Overdue</h2>

<p>Dear ${data.contactPerson || 'Admin'},</p>

<p>Your payment for <strong>${data.schoolName || 'your school'}</strong> is now overdue. Please make the payment immediately to avoid service interruption.</p>

<div class="danger-box">
    <h3 style="color: #dc2626; margin-top: 0;">Overdue Invoice</h3>
    <p><strong>Invoice Number:</strong> ${data.invoiceNumber}</p>
    <p><strong>Original Amount:</strong> ₹${data.amount}</p>
    ${data.penaltyAmount ? `<p><strong>Penalty Amount:</strong> <span class="overdue-amount">₹${data.penaltyAmount}</span></p>` : ''}
    <p><strong>Days Overdue:</strong> ${data.daysOverdue || 0} days</p>
    <p><strong>Due Date:</strong> ${data.dueDate}</p>
</div>

<p><strong>Important:</strong> Your account will be suspended if payment is not received within 15 days of the due date.</p>

${data.paymentUrl ? `
<div style="text-align: center; margin: 30px 0;">
    <a href="${data.paymentUrl}" class="btn btn-danger">Pay Now</a>
</div>
` : ''}

<p>If you have any questions or need assistance, please contact our support team immediately.</p>

<p>Best regards,<br>
The Schopio Team</p>`

    return this.getBaseTemplate(content)
  }

  /**
   * Invoice generated template
   */
  private getInvoiceGeneratedTemplate(data: BillingEmailData): string {
    const content = `
<h2 style="color: #2563eb; margin-bottom: 20px;">New Invoice Generated</h2>

<p>Dear ${data.contactPerson || 'Admin'},</p>

<p>Your monthly invoice for <strong>${data.schoolName || 'your school'}</strong> has been generated and is ready for payment.</p>

<div class="invoice-details">
    <h3 style="color: #2563eb; margin-top: 0;">Invoice Details</h3>
    <p><strong>Invoice Number:</strong> ${data.invoiceNumber}</p>
    <p><strong>Amount:</strong> <span class="amount">₹${data.amount}</span></p>
    <p><strong>Due Date:</strong> ${data.dueDate}</p>
</div>

<p>Please make the payment by the due date to ensure uninterrupted service.</p>

${data.paymentUrl ? `
<div style="text-align: center; margin: 30px 0;">
    <a href="${data.paymentUrl}" class="btn">Pay Now</a>
</div>
` : ''}

<p>Thank you for choosing Schopio for your school management needs.</p>

<p>Best regards,<br>
The Schopio Team</p>`

    return this.getBaseTemplate(content)
  }

  /**
   * Payment confirmation template
   */
  private getPaymentConfirmationTemplate(data: BillingEmailData): string {
    const content = `
<h2 style="color: #10b981; margin-bottom: 20px;">Payment Received Successfully</h2>

<p>Dear ${data.contactPerson || 'Admin'},</p>

<p>Thank you! We have successfully received your payment for <strong>${data.schoolName || 'your school'}</strong>.</p>

<div class="invoice-details">
    <h3 style="color: #10b981; margin-top: 0;">Payment Details</h3>
    <p><strong>Invoice Number:</strong> ${data.invoiceNumber}</p>
    <p><strong>Amount Paid:</strong> <span class="amount" style="color: #10b981;">₹${data.amount}</span></p>
    <p><strong>Payment Date:</strong> ${new Date().toLocaleDateString('en-IN')}</p>
</div>

<p>Your subscription remains active and all services will continue uninterrupted.</p>

${data.receiptUrl ? `
<div style="text-align: center; margin: 30px 0;">
    <a href="${data.receiptUrl}" class="btn btn-success">Download Receipt</a>
</div>
` : ''}

<p>Thank you for your continued trust in Schopio.</p>

<p>Best regards,<br>
The Schopio Team</p>`

    return this.getBaseTemplate(content)
  }

  /**
   * Final notice template
   */
  private getFinalNoticeTemplate(data: BillingEmailData): string {
    const content = `
<h2 style="color: #ef4444; margin-bottom: 20px;">FINAL NOTICE - Account Suspension Warning</h2>

<p>Dear ${data.contactPerson || 'Admin'},</p>

<p><strong>URGENT:</strong> Your account for <strong>${data.schoolName || 'your school'}</strong> will be suspended if payment is not received immediately.</p>

<div class="danger-box">
    <h3 style="color: #dc2626; margin-top: 0;">Final Notice</h3>
    <p><strong>Invoice Number:</strong> ${data.invoiceNumber}</p>
    <p><strong>Total Amount Due:</strong> <span class="overdue-amount">₹${data.amount}</span></p>
    ${data.penaltyAmount ? `<p><strong>Penalty Amount:</strong> <span class="overdue-amount">₹${data.penaltyAmount}</span></p>` : ''}
    <p><strong>Days Overdue:</strong> ${data.daysOverdue || 0} days</p>
    <p><strong>Original Due Date:</strong> ${data.dueDate}</p>
</div>

<p><strong>Account suspension will result in:</strong></p>
<ul>
    <li>Loss of access to all Schopio services</li>
    <li>Data backup and potential data loss</li>
    <li>Additional recovery fees</li>
    <li>Service restoration delays</li>
</ul>

${data.paymentUrl ? `
<div style="text-align: center; margin: 30px 0;">
    <a href="${data.paymentUrl}" class="btn btn-danger">Pay Immediately</a>
</div>
` : ''}

<p><strong>Contact us immediately</strong> if you need assistance or have questions about this notice.</p>

<p>Best regards,<br>
The Schopio Team</p>`

    return this.getBaseTemplate(content)
  }
}

// Export singleton instance
export const emailService = new EmailService()
export type { BillingEmailData, EmailOptions }
