import { db } from '@/src/db'
import { subscriptions, billingCycles, invoices } from '@/src/db/schema'
import { eq, and, lte, gte, sql } from 'drizzle-orm'

export interface DueDateCalculation {
  dueDate: Date
  gracePeriodEnd: Date
  isOverdue: boolean
  daysOverdue: number
  penaltyAmount: number
  status: 'current' | 'grace_period' | 'overdue' | 'suspended'
}

export interface BillingCycleUpdate {
  id: string
  status: 'pending' | 'active' | 'overdue' | 'suspended'
  penaltyAmount: number
  daysOverdue: number
}

export class DueDateManager {
  private static readonly PENALTY_RATE = 0.02 // 2% daily penalty
  private static readonly SUSPENSION_DAYS = 15 // Suspend after 15 days overdue

  /**
   * Calculate due date for a subscription based on billing cycle and due date preference
   */
  static calculateDueDate(
    cycleStart: Date,
    billingCycle: 'monthly' | 'yearly',
    dueDateOfMonth: number,
    gracePeriodDays: number = 3
  ): DueDateCalculation {
    const dueDate = new Date(cycleStart)
    
    // Set the due date based on billing cycle
    if (billingCycle === 'monthly') {
      dueDate.setMonth(dueDate.getMonth() + 1)
    } else {
      dueDate.setFullYear(dueDate.getFullYear() + 1)
    }
    
    // Set the specific day of the month
    dueDate.setDate(dueDateOfMonth)
    
    // Calculate grace period end
    const gracePeriodEnd = new Date(dueDate)
    gracePeriodEnd.setDate(gracePeriodEnd.getDate() + gracePeriodDays)
    
    // Calculate current status
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    const isOverdue = today > gracePeriodEnd
    const daysOverdue = isOverdue ? Math.floor((today.getTime() - gracePeriodEnd.getTime()) / (1000 * 60 * 60 * 24)) : 0
    
    let status: 'current' | 'grace_period' | 'overdue' | 'suspended' = 'current'
    if (today > dueDate && today <= gracePeriodEnd) {
      status = 'grace_period'
    } else if (isOverdue && daysOverdue < this.SUSPENSION_DAYS) {
      status = 'overdue'
    } else if (daysOverdue >= this.SUSPENSION_DAYS) {
      status = 'suspended'
    }
    
    return {
      dueDate,
      gracePeriodEnd,
      isOverdue,
      daysOverdue,
      penaltyAmount: 0, // Will be calculated separately based on amount
      status
    }
  }

  /**
   * Calculate penalty amount based on overdue days and total amount
   */
  static calculatePenalty(totalAmount: number, daysOverdue: number): number {
    if (daysOverdue <= 0) return 0
    return totalAmount * this.PENALTY_RATE * daysOverdue
  }

  /**
   * Update billing cycle statuses based on due dates
   */
  static async updateOverdueBillingCycles(): Promise<BillingCycleUpdate[]> {
    try {
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      // Use database transaction with row-level locking for atomic operations
      const updates = await db.transaction(async (tx) => {
        // Get all active billing cycles that might be overdue (with row-level locking)
        const cycles = await tx.select({
          id: billingCycles.id,
          subscriptionId: billingCycles.subscriptionId,
          dueDate: billingCycles.dueDate,
          gracePeriodDays: billingCycles.gracePeriodDays,
          totalAmount: billingCycles.totalAmount,
          status: billingCycles.status,
          // Get subscription details for due date calculation
          subscriptionDueDate: subscriptions.dueDate,
          subscriptionGracePeriod: subscriptions.gracePeriodDays
        })
        .from(billingCycles)
        .leftJoin(subscriptions, eq(billingCycles.subscriptionId, subscriptions.id))
        .where(and(
          eq(billingCycles.status, 'active'),
          lte(billingCycles.dueDate, today.toISOString().split('T')[0])
        ))
        .for('update')

        const cycleUpdates: BillingCycleUpdate[] = []

        for (const cycle of cycles) {
          const dueDate = new Date(cycle.dueDate)
          const gracePeriodDays = cycle.gracePeriodDays || cycle.subscriptionGracePeriod || 3
          const gracePeriodEnd = new Date(dueDate)
          gracePeriodEnd.setDate(gracePeriodEnd.getDate() + gracePeriodDays)

          const isInGracePeriod = today > dueDate && today <= gracePeriodEnd
          const isOverdue = today > gracePeriodEnd
          const daysOverdue = isOverdue ? Math.floor((today.getTime() - gracePeriodEnd.getTime()) / (1000 * 60 * 60 * 24)) : 0

          let newStatus: 'pending' | 'active' | 'overdue' | 'suspended' = 'active'
          let penaltyAmount = 0

          if (isInGracePeriod) {
            newStatus = 'active' // Still in grace period
          } else if (isOverdue && daysOverdue < this.SUSPENSION_DAYS) {
            newStatus = 'overdue'
            penaltyAmount = this.calculatePenalty(parseFloat(cycle.totalAmount), daysOverdue)
          } else if (daysOverdue >= this.SUSPENSION_DAYS) {
            newStatus = 'suspended'
            penaltyAmount = this.calculatePenalty(parseFloat(cycle.totalAmount), daysOverdue)
          }

          // Update the billing cycle if status changed
          if (newStatus !== cycle.status || penaltyAmount > 0) {
            await tx.update(billingCycles)
              .set({
                status: newStatus,
                penaltyAmount: penaltyAmount.toString()
              })
              .where(eq(billingCycles.id, cycle.id))

            cycleUpdates.push({
              id: cycle.id,
              status: newStatus,
              penaltyAmount,
              daysOverdue
            })
          }
        }

        return cycleUpdates
      })

      return updates
    } catch (error) {
      console.error('Error updating overdue billing cycles:', error)
      throw error
    }
  }

  /**
   * Update subscription statuses based on billing cycle statuses
   */
  static async updateSubscriptionStatuses(): Promise<void> {
    try {
      // Get subscriptions with overdue or suspended billing cycles
      const overdueSubscriptions = await db.select({
        subscriptionId: subscriptions.id,
        billingStatus: billingCycles.status
      })
      .from(subscriptions)
      .leftJoin(billingCycles, eq(subscriptions.id, billingCycles.subscriptionId))
      .where(and(
        eq(subscriptions.status, 'active'),
        sql`${billingCycles.status} IN ('overdue', 'suspended')`
      ))

      // Group by subscription and determine worst status
      const subscriptionUpdates = new Map<string, string>()
      
      for (const sub of overdueSubscriptions) {
        const currentStatus = subscriptionUpdates.get(sub.subscriptionId) || 'active'
        
        if (sub.billingStatus === 'suspended' || currentStatus === 'suspended') {
          subscriptionUpdates.set(sub.subscriptionId, 'suspended')
        } else if (sub.billingStatus === 'overdue' && currentStatus !== 'suspended') {
          subscriptionUpdates.set(sub.subscriptionId, 'overdue')
        }
      }

      // Update subscription statuses
      for (const [subscriptionId, status] of subscriptionUpdates) {
        await db.update(subscriptions)
          .set({ status })
          .where(eq(subscriptions.id, subscriptionId))
      }

    } catch (error) {
      console.error('Error updating subscription statuses:', error)
      throw error
    }
  }

  /**
   * Get due date information for a specific subscription
   */
  static async getSubscriptionDueDateInfo(subscriptionId: string): Promise<DueDateCalculation | null> {
    try {
      const [subscription] = await db.select({
        id: subscriptions.id,
        nextBillingDate: subscriptions.nextBillingDate,
        dueDate: subscriptions.dueDate,
        gracePeriodDays: subscriptions.gracePeriodDays,
        billingCycle: subscriptions.billingCycle,
        status: subscriptions.status
      })
      .from(subscriptions)
      .where(eq(subscriptions.id, subscriptionId))
      .limit(1)

      if (!subscription) return null

      const nextBilling = new Date(subscription.nextBillingDate)
      const dueDateCalc = this.calculateDueDate(
        nextBilling,
        subscription.billingCycle as 'monthly' | 'yearly',
        subscription.dueDate || 15,
        subscription.gracePeriodDays || 3
      )

      return dueDateCalc
    } catch (error) {
      console.error('Error getting subscription due date info:', error)
      return null
    }
  }

  /**
   * Process all due date updates (to be called by scheduler)
   */
  static async processAllDueDateUpdates(): Promise<{
    billingCycleUpdates: BillingCycleUpdate[]
    subscriptionsUpdated: number
  }> {
    try {
      console.log('Starting due date processing...')
      
      const billingCycleUpdates = await this.updateOverdueBillingCycles()
      await this.updateSubscriptionStatuses()
      
      console.log(`Due date processing completed. Updated ${billingCycleUpdates.length} billing cycles`)
      
      return {
        billingCycleUpdates,
        subscriptionsUpdated: billingCycleUpdates.length
      }
    } catch (error) {
      console.error('Error in due date processing:', error)
      throw error
    }
  }
}
