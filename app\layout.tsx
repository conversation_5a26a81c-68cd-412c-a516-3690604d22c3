import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { SEO } from "@/lib/constants";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import LiveChat from "@/components/ui/LiveChat";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

export const metadata: Metadata = {
  title: {
    default: SEO.siteName,
    template: `%s | ${SEO.siteName}`,
  },
  description: SEO.description,
  keywords: SEO.keywords,
  authors: [{ name: "Schopio Team" }],
  creator: "Schop<PERSON>",
  publisher: "Schopio",
  metadataBase: new URL("https://schopio.com"),
  openGraph: {
    type: "website",
    locale: "en_IN",
    url: "https://schopio.com",
    title: SEO.siteName,
    description: SEO.description,
    siteName: SEO.siteName,
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: SEO.siteName,
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: SEO.siteName,
    description: SEO.description,
    images: ["/og-image.jpg"],
    creator: "@schopio",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "google-site-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="font-sans antialiased bg-white text-neutral-900">
        <Header />
        <div className="pt-16 lg:pt-20">
          {children}
        </div>
        <Footer />
        <LiveChat />
      </body>
    </html>
  );
}
