import { <PERSON>o } from "hono"
import { zValidator } from "@hono/zod-validator"
import { z } from "zod"
import { db } from "@/db"
import { leads } from "@/db/schema"
import { eq, desc, and, like, gte, lte, sql } from "drizzle-orm"

// Validation schemas
const createLeadSchema = z.object({
  contactPerson: z.string().min(2, "Contact person name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(10, "Phone number must be at least 10 digits").optional(),
  schoolName: z.string().min(2, "School name is required"),
  estimatedStudents: z.number().min(1, "Student count must be at least 1").max(10000, "Student count too large").optional(),
  source: z.enum(["landing_page", "referral", "demo_request", "ai_chat", "contact_form", "other"]).default("landing_page"),
  notes: z.string().optional(),
})

const updateLeadSchema = z.object({
  status: z.enum(["new", "contacted", "demo_scheduled", "proposal_sent", "converted", "lost"]).optional(),
  notes: z.string().optional(),
  contactPerson: z.string().optional(),
  phone: z.string().optional(),
  estimatedStudents: z.number().optional(),
})

const leadsQuerySchema = z.object({
  page: z.string().transform(val => parseInt(val) || 1).optional(),
  limit: z.string().transform(val => Math.min(parseInt(val) || 20, 100)).optional(),
  status: z.string().optional(),
  source: z.string().optional(),
  search: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  sortBy: z.enum(["createdAt", "updatedAt", "contactPerson", "estimatedStudents"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
})

// Create Hono app for leads routes
const app = new Hono()

// Create new lead (public endpoint for contact forms)
app.post(
  "/",
  zValidator("json", createLeadSchema),
  async (c) => {
    try {
      const validatedData = c.req.valid("json")

      // Create lead record
      const [newLead] = await db.insert(leads).values({
        email: validatedData.email,
        schoolName: validatedData.schoolName,
        contactPerson: validatedData.contactPerson,
        phone: validatedData.phone,
        estimatedStudents: validatedData.estimatedStudents,
        source: validatedData.source,
        status: "new",
        notes: validatedData.notes,
      }).returning()

      // TODO: Send notification email to sales team
      // TODO: Add to CRM/sales pipeline
      // TODO: Send welcome email to lead

      return c.json({
        success: true,
        data: {
          leadId: newLead.id,
          message: "Thank you for your interest! Our team will contact you within 24 hours.",
        }
      }, 201)

    } catch (error) {
      console.error("Error creating lead:", error)
      return c.json({
        success: false,
        error: "Failed to submit lead. Please try again."
      }, 500)
    }
  }
)

// Get all leads with filtering and pagination (admin only)
app.get(
  "/",
  zValidator("query", leadsQuerySchema),
  async (c) => {
    try {
      const query = c.req.valid("query")
      const { page = 1, limit = 20, search, status, source, dateFrom, dateTo, sortBy, sortOrder } = query
      
      // Build where conditions
      const conditions = []
      
      if (status) {
        conditions.push(eq(leads.status, status as any))
      }

      if (source) {
        conditions.push(eq(leads.source, source as any))
      }

      if (search) {
        conditions.push(
          like(leads.contactPerson, `%${search}%`)
        )
      }

      if (dateFrom) {
        conditions.push(gte(leads.createdAt, new Date(dateFrom)))
      }

      if (dateTo) {
        conditions.push(lte(leads.createdAt, new Date(dateTo)))
      }
      
      // Calculate offset
      const offset = (page - 1) * limit
      
      // Get leads with pagination
      const leadsData = await db
        .select()
        .from(leads)
        .where(conditions.length > 0 ? and(...conditions) : undefined)
        .orderBy(sortOrder === "desc" ? desc(leads[sortBy]) : leads[sortBy])
        .limit(limit)
        .offset(offset)

      // Get total count for pagination
      const [{ count }] = await db
        .select({ count: sql<number>`count(*)` })
        .from(leads)
        .where(conditions.length > 0 ? and(...conditions) : undefined)
      
      const totalPages = Math.ceil(count / limit)
      
      return c.json({
        success: true,
        data: leadsData,
        pagination: {
          page,
          limit,
          total: count,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        }
      })
      
    } catch (error) {
      console.error("Error fetching leads:", error)
      return c.json({
        success: false,
        error: "Failed to fetch leads"
      }, 500)
    }
  }
)

// Get single lead by ID
app.get(
  "/:id",
  async (c) => {
    try {
      const leadId = c.req.param("id")
      
      const [lead] = await db
        .select()
        .from(leads)
        .where(eq(leads.id, leadId))
      
      if (!lead) {
        return c.json({
          success: false,
          error: "Lead not found"
        }, 404)
      }
      
      return c.json({
        success: true,
        data: lead
      })
      
    } catch (error) {
      console.error("Error fetching lead:", error)
      return c.json({
        success: false,
        error: "Failed to fetch lead"
      }, 500)
    }
  }
)

// Update lead (admin only)
app.put(
  "/:id",
  zValidator("json", updateLeadSchema),
  async (c) => {
    try {
      const leadId = c.req.param("id")
      const updates = c.req.valid("json")
      
      // Check if lead exists
      const [existingLead] = await db
        .select()
        .from(leads)
        .where(eq(leads.id, leadId))

      if (!existingLead) {
        return c.json({
          success: false,
          error: "Lead not found"
        }, 404)
      }

      // Update lead
      const [updatedLead] = await db
        .update(leads)
        .set({
          ...updates,
          updatedAt: new Date(),
        })
        .where(eq(leads.id, leadId))
        .returning()
      
      return c.json({
        success: true,
        data: updatedLead,
        message: "Lead updated successfully"
      })
      
    } catch (error) {
      console.error("Error updating lead:", error)
      return c.json({
        success: false,
        error: "Failed to update lead"
      }, 500)
    }
  }
)

// Delete lead (admin only)
app.delete(
  "/:id",
  async (c) => {
    try {
      const leadId = c.req.param("id")
      
      // Check if lead exists
      const [existingLead] = await db
        .select()
        .from(leads)
        .where(eq(leads.id, leadId))

      if (!existingLead) {
        return c.json({
          success: false,
          error: "Lead not found"
        }, 404)
      }

      // Delete lead
      await db
        .delete(leads)
        .where(eq(leads.id, leadId))
      
      return c.json({
        success: true,
        message: "Lead deleted successfully"
      })
      
    } catch (error) {
      console.error("Error deleting lead:", error)
      return c.json({
        success: false,
        error: "Failed to delete lead"
      }, 500)
    }
  }
)

export default app
