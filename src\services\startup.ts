import { billingScheduler } from './billingScheduler'

/**
 * Initialize all background services
 */
export function initializeServices(): void {
  console.log('🚀 Initializing Schopio Services...')
  
  try {
    // Initialize billing scheduler
    billingScheduler.init()
    
    console.log('✅ All services initialized successfully')
  } catch (error) {
    console.error('❌ Failed to initialize services:', error)
    throw error
  }
}

/**
 * Gracefully shutdown all services
 */
export function shutdownServices(): void {
  console.log('🛑 Shutting down Schopio Services...')
  
  try {
    // Stop billing scheduler
    billingScheduler.stop()
    
    console.log('✅ All services shut down successfully')
  } catch (error) {
    console.error('❌ Error during service shutdown:', error)
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...')
  shutdownServices()
  process.exit(0)
})

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...')
  shutdownServices()
  process.exit(0)
})
