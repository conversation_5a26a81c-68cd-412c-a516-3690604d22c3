'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { But<PERSON> } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { ArrowRight, CheckCircle, Star, Users, TrendingUp, Shield } from 'lucide-react'


const HeroSection = () => {
  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const staggerChildren = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const features = [
    'Complete 8+ modules in basic plan',
    'AI-powered predictive insights',
    'Web-based platform access',
    'Modern responsive design'
  ]

  const systemFeatures = [
    { icon: Users, label: '8+ Modules', value: 'Complete Suite' },
    { icon: TrendingUp, label: '99.9%', value: 'Uptime' },
    { icon: Shield, label: 'Bank-grade', value: 'Security' },
    { icon: Star, label: 'AI-Powered', value: 'Intelligence' }
  ]

  return (
    <section className="relative min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#ffffff12_1px,transparent_1px),linear-gradient(to_bottom,#ffffff12_1px,transparent_1px)] bg-[size:24px_24px] opacity-30" />

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-blue-500 rounded-full opacity-20 animate-bounce-gentle blur-sm" />
      <div className="absolute top-40 right-20 w-16 h-16 bg-emerald-500 rounded-full opacity-20 animate-bounce-gentle blur-sm" style={{ animationDelay: '1s' }} />
      <div className="absolute bottom-40 left-20 w-12 h-12 bg-cyan-500 rounded-full opacity-20 animate-bounce-gentle blur-sm" style={{ animationDelay: '2s' }} />

      <div className="relative container mx-auto px-4 py-20">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Content */}
          <motion.div
            variants={staggerChildren}
            initial="initial"
            animate="animate"
            className="space-y-8"
          >
            {/* Badge */}
            <motion.div variants={fadeInUp}>
              <div className="inline-flex items-center gap-2 bg-emerald-500/20 text-emerald-300 border border-emerald-500/30 px-4 py-2 rounded-full text-sm font-medium backdrop-blur-sm">
                <Star className="w-4 h-4 fill-current" />
                AI-Powered School Management Platform
              </div>
            </motion.div>

            {/* Headline */}
            <motion.div variants={fadeInUp} className="space-y-6">
              <h1 className="text-4xl lg:text-6xl font-bold text-white leading-tight">
                While others give you{' '}
                <span className="text-orange-400">pieces</span>,<br />
                we give you the{' '}
                <span className="bg-gradient-to-r from-blue-400 to-emerald-400 bg-clip-text text-transparent">
                  complete picture
                </span>
              </h1>
              <h2 className="text-2xl lg:text-3xl font-semibold text-blue-200">
                AI-Powered School Management That Predicts Student Success
              </h2>
              <p className="text-xl text-blue-100 leading-relaxed max-w-2xl">
                Complete 8+ modules with AI analytics that predict dropout risks, optimize learning outcomes, and transform educational operations. Flexible solutions that grow with your school.
              </p>
            </motion.div>

            {/* Features List */}
            <motion.div variants={fadeInUp} className="space-y-3">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center gap-3">
                  <CheckCircle className="w-5 h-5 text-emerald-400 flex-shrink-0" />
                  <span className="text-blue-100 font-medium">{feature}</span>
                </div>
              ))}
            </motion.div>

            {/* CTA Buttons */}
            <motion.div variants={fadeInUp} className="flex flex-col sm:flex-row gap-4">
              <Button
                size="lg"
                icon={ArrowRight}
                iconPosition="right"
                className="text-lg px-8 py-4 bg-gradient-to-r from-emerald-500 to-blue-500 hover:from-emerald-600 hover:to-blue-600"
                onClick={() => window.location.href = '/demo'}
              >
                Schedule Live Demo
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-4 border-white/30 text-white hover:bg-white/10"
                onClick={() => window.location.href = '/ai-features'}
              >
                Explore AI Features
              </Button>
            </motion.div>

            {/* Video Thumbnail */}
            <motion.div variants={fadeInUp} className="mt-8">
              <div className="relative group cursor-pointer">
                <div className="bg-gradient-to-br from-blue-800/30 to-emerald-800/30 backdrop-blur-sm border border-white/20 rounded-xl p-6 hover:from-blue-800/40 hover:to-emerald-800/40 transition-all duration-300">
                  <div className="flex items-center gap-4">
                    <div className="relative">
                      <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <div className="w-0 h-0 border-l-[12px] border-l-white border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent ml-1" />
                      </div>
                      <div className="absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                        <span className="text-xs text-white font-bold">2:15</span>
                      </div>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-white font-bold text-lg mb-1">2-Minute System Overview</h4>
                      <p className="text-blue-200 text-sm mb-2">See how Schopio transforms school operations</p>
                      <div className="flex items-center gap-4 text-xs text-emerald-300">
                        <span>📊 Live Dashboard Demo</span>
                        <span>🎯 AI Features Showcase</span>
                        <span>⚡ Quick Implementation</span>
                      </div>
                    </div>
                    <div className="text-blue-200 group-hover:text-white transition-colors">
                      <ArrowRight className="w-5 h-5" />
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Secondary CTAs */}
            <motion.div variants={fadeInUp} className="flex flex-wrap gap-4 text-sm">
              <div className="flex items-center gap-2 text-blue-200">
                <CheckCircle className="w-4 h-4 text-emerald-400" />
                14-day free trial
              </div>
              <div className="flex items-center gap-2 text-blue-200">
                <CheckCircle className="w-4 h-4 text-emerald-400" />
                Free setup & migration
              </div>
              <div className="flex items-center gap-2 text-blue-200">
                <CheckCircle className="w-4 h-4 text-emerald-400" />
                No long-term commitments
              </div>
            </motion.div>

            {/* Value Proposition Highlight */}
            <motion.div variants={fadeInUp}>
              <Card className="bg-gradient-to-r from-blue-800/50 to-emerald-800/50 border-blue-500/30 backdrop-blur-sm">
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-blue-200">Complete Solution</p>
                      <p className="text-2xl font-bold text-white">
                        All 8+ Modules Included
                      </p>
                      <p className="text-sm text-emerald-300">
                        Transparent solutions, complete functionality
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-blue-200">Enterprise features</p>
                      <p className="text-lg font-semibold text-white">AI-powered insights</p>
                    </div>
                  </div>
                </div>
              </Card>
            </motion.div>
          </motion.div>

          {/* Right Column - Visual */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative"
          >
            {/* Main Dashboard Preview */}
            <Card className="bg-white/95 backdrop-blur-sm shadow-2xl border border-blue-200/50 transform rotate-2 hover:rotate-0 transition-transform duration-500">
              <div className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-3 h-3 bg-red-500 rounded-full" />
                  <div className="w-3 h-3 bg-yellow-500 rounded-full" />
                  <div className="w-3 h-3 bg-green-500 rounded-full" />
                  <div className="ml-auto text-sm text-slate-600 font-medium">schopio.com</div>
                </div>
                <div className="space-y-4">
                  <div className="h-4 bg-slate-200 rounded w-3/4" />
                  <div className="h-4 bg-blue-300 rounded w-1/2" />
                  <div className="grid grid-cols-2 gap-4">
                    <div className="h-20 bg-gradient-to-br from-blue-100 to-blue-300 rounded-lg flex items-center justify-center">
                      <Users className="w-6 h-6 text-blue-600" />
                    </div>
                    <div className="h-20 bg-gradient-to-br from-emerald-100 to-emerald-300 rounded-lg flex items-center justify-center">
                      <TrendingUp className="w-6 h-6 text-emerald-600" />
                    </div>
                  </div>
                  <div className="h-4 bg-slate-200 rounded w-2/3" />
                </div>
              </div>
            </Card>

            {/* Floating Feature Cards */}
            <motion.div
              animate={{ y: [-10, 10, -10] }}
              transition={{ duration: 4, repeat: Infinity }}
              className="absolute -top-4 -left-4"
            >
              <Card className="bg-white/95 backdrop-blur-sm shadow-lg border border-blue-200/50 p-4 w-32">
                <div className="text-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-full mx-auto mb-2 flex items-center justify-center">
                    <TrendingUp className="w-4 h-4 text-blue-600" />
                  </div>
                  <p className="text-xs text-slate-700 font-medium">AI Insights</p>
                </div>
              </Card>
            </motion.div>

            <motion.div
              animate={{ y: [10, -10, 10] }}
              transition={{ duration: 3, repeat: Infinity, delay: 1 }}
              className="absolute -bottom-4 -right-4"
            >
              <Card className="bg-white/95 backdrop-blur-sm shadow-lg border border-emerald-200/50 p-4 w-32">
                <div className="text-center">
                  <div className="w-8 h-8 bg-emerald-100 rounded-full mx-auto mb-2 flex items-center justify-center">
                    <Shield className="w-4 h-4 text-emerald-600" />
                  </div>
                  <p className="text-xs text-slate-700 font-medium">Secure</p>
                </div>
              </Card>
            </motion.div>
          </motion.div>
        </div>

        {/* System Features */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mt-20"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {systemFeatures.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="w-12 h-12 bg-blue-500/20 border border-blue-400/30 rounded-full mx-auto mb-3 flex items-center justify-center backdrop-blur-sm">
                  <feature.icon className="w-6 h-6 text-blue-300" />
                </div>
                <p className="text-2xl font-bold text-white">{feature.label}</p>
                <p className="text-sm text-blue-200">{feature.value}</p>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default HeroSection
