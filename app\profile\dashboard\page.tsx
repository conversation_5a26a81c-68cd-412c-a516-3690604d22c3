'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/badge'
import { 
  CreditCard, 
  Calendar, 
  Users,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  School
} from 'lucide-react'
import Link from 'next/link'

interface DashboardData {
  subscription: {
    planName: string
    status: string
    nextBillingDate: string
    monthlyAmount: string
  }
  invoices: {
    pending: number
    pendingAmount: string
    overdue: number
    overdueAmount: string
  }
  payments: {
    thisMonth: number
    thisMonthAmount: string
    lastPayment: string
  }
  usage: {
    studentCount: number
    activeModules: number
    storageUsed: string
  }
}

export default function SchoolDashboard() {
  const [data, setData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      // Mock data for now - replace with actual API call
      const mockData: DashboardData = {
        subscription: {
          planName: 'Complete School Management',
          status: 'active',
          nextBillingDate: '2024-08-01',
          monthlyAmount: '15000'
        },
        invoices: {
          pending: 1,
          pendingAmount: '15000',
          overdue: 0,
          overdueAmount: '0'
        },
        payments: {
          thisMonth: 1,
          thisMonthAmount: '15000',
          lastPayment: '2024-07-01'
        },
        usage: {
          studentCount: 450,
          activeModules: 8,
          storageUsed: '2.3 GB'
        }
      }
      
      setData(mockData)
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatAmount = (amount: string) => {
    return `₹${parseFloat(amount).toLocaleString('en-IN')}`
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Active</Badge>
      case 'suspended':
        return <Badge className="bg-red-100 text-red-800"><AlertCircle className="w-3 h-3 mr-1" />Suspended</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Pending</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading dashboard...</p>
          </div>
        </div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error loading dashboard</h3>
          <p className="text-gray-600">Please try refreshing the page.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard</h1>
        <p className="text-gray-600">Welcome to your Schopio school portal</p>
      </div>

      {/* Subscription Status */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <School className="h-5 w-5" />
            Subscription Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Plan</p>
              <p className="text-lg font-semibold">{data.subscription.planName}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Status</p>
              {getStatusBadge(data.subscription.status)}
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Monthly Amount</p>
              <p className="text-lg font-semibold">{formatAmount(data.subscription.monthlyAmount)}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">Next Billing</p>
              <p className="text-lg font-semibold">{formatDate(data.subscription.nextBillingDate)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Invoices</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.invoices.pending}</div>
            <p className="text-xs text-muted-foreground">
              {formatAmount(data.invoices.pendingAmount)} total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month Payments</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.payments.thisMonth}</div>
            <p className="text-xs text-muted-foreground">
              {formatAmount(data.payments.thisMonthAmount)} paid
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.usage.studentCount}</div>
            <p className="text-xs text-muted-foreground">
              {data.usage.activeModules} modules active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Storage Used</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.usage.storageUsed}</div>
            <p className="text-xs text-muted-foreground">
              of unlimited storage
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Billing & Payments
            </CardTitle>
            <CardDescription>
              Manage your invoices and payment history
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.invoices.pending > 0 && (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-1">
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                    <span className="text-sm font-medium text-yellow-800">
                      {data.invoices.pending} pending invoice{data.invoices.pending > 1 ? 's' : ''}
                    </span>
                  </div>
                  <p className="text-sm text-yellow-700">
                    Total amount: {formatAmount(data.invoices.pendingAmount)}
                  </p>
                </div>
              )}
              
              {data.invoices.overdue > 0 && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-1">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <span className="text-sm font-medium text-red-800">
                      {data.invoices.overdue} overdue invoice{data.invoices.overdue > 1 ? 's' : ''}
                    </span>
                  </div>
                  <p className="text-sm text-red-700">
                    Total amount: {formatAmount(data.invoices.overdueAmount)}
                  </p>
                </div>
              )}

              <div className="flex gap-2">
                <Link href="/profile/billing">
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    <CreditCard className="w-4 h-4 mr-2" />
                    View Billing
                  </Button>
                </Link>
                {data.invoices.pending > 0 && (
                  <Link href="/profile/billing">
                    <Button variant="outline">
                      Pay Now
                    </Button>
                  </Link>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Your recent account activity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Payment received</p>
                  <p className="text-xs text-gray-600">
                    {formatAmount(data.payments.thisMonthAmount)} on {formatDate(data.payments.lastPayment)}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Invoice generated</p>
                  <p className="text-xs text-gray-600">
                    Monthly billing for {formatDate(data.subscription.nextBillingDate)}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Subscription renewed</p>
                  <p className="text-xs text-gray-600">
                    {data.subscription.planName} plan
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
