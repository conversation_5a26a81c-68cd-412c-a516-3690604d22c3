import Razorpay from 'razorpay'
import crypto from 'crypto'

interface RazorpayConfig {
  keyId: string
  keySecret: string
  webhookSecret: string
}

interface CreateOrderOptions {
  amount: number // Amount in paise (₹1 = 100 paise)
  currency: string
  receipt: string
  notes?: Record<string, string>
}

interface CreateSubscriptionOptions {
  planId: string
  customerEmail: string
  customerName: string
  customerContact?: string
  totalCount?: number
  notes?: Record<string, string>
}

interface VerifyPaymentOptions {
  razorpayOrderId: string
  razorpayPaymentId: string
  razorpaySignature: string
}

class RazorpayService {
  private razorpay: Razorpay
  private webhookSecret: string
  private keySecret: string

  constructor(config: RazorpayConfig) {
    this.razorpay = new Razorpay({
      key_id: config.keyId,
      key_secret: config.keySecret
    })
    this.webhookSecret = config.webhookSecret
    this.keySecret = config.keySecret
  }

  /**
   * Create a Razorpay order for one-time payment
   */
  async createOrder(options: CreateOrderOptions): Promise<any> {
    try {
      const order = await this.razorpay.orders.create({
        amount: options.amount,
        currency: options.currency,
        receipt: options.receipt,
        notes: options.notes || {}
      })

      return {
        success: true,
        order
      }
    } catch (error) {
      console.error('Razorpay create order error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Create a Razorpay plan for subscription billing
   */
  async createPlan(planData: {
    period: 'monthly' | 'yearly'
    interval: number
    amount: number // Amount in paise
    currency: string
    description: string
  }): Promise<any> {
    try {
      const plan = await this.razorpay.plans.create({
        period: planData.period,
        interval: planData.interval,
        item: {
          name: planData.description,
          amount: planData.amount,
          currency: planData.currency,
          description: planData.description
        }
      })

      return {
        success: true,
        plan
      }
    } catch (error) {
      console.error('Razorpay create plan error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Create a Razorpay subscription
   */
  async createSubscription(options: CreateSubscriptionOptions): Promise<any> {
    try {
      const subscription = await this.razorpay.subscriptions.create({
        plan_id: options.planId,
        customer_notify: 1,
        total_count: options.totalCount || 12, // Default to 12 months
        notes: {
          customer_email: options.customerEmail,
          customer_name: options.customerName,
          ...options.notes
        }
      })

      return {
        success: true,
        subscription
      }
    } catch (error) {
      console.error('Razorpay create subscription error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Verify payment signature
   */
  verifyPaymentSignature(options: VerifyPaymentOptions): boolean {
    try {
      const body = options.razorpayOrderId + '|' + options.razorpayPaymentId
      const expectedSignature = crypto
        .createHmac('sha256', this.keySecret)
        .update(body.toString())
        .digest('hex')

      return expectedSignature === options.razorpaySignature
    } catch (error) {
      console.error('Payment signature verification error:', error)
      return false
    }
  }

  /**
   * Verify webhook signature
   */
  verifyWebhookSignature(body: string, signature: string): boolean {
    try {
      const expectedSignature = crypto
        .createHmac('sha256', this.webhookSecret)
        .update(body)
        .digest('hex')

      return expectedSignature === signature
    } catch (error) {
      console.error('Webhook signature verification error:', error)
      return false
    }
  }

  /**
   * Get payment details
   */
  async getPayment(paymentId: string): Promise<any> {
    try {
      const payment = await this.razorpay.payments.fetch(paymentId)
      return {
        success: true,
        payment
      }
    } catch (error) {
      console.error('Get payment error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get subscription details
   */
  async getSubscription(subscriptionId: string): Promise<any> {
    try {
      const subscription = await this.razorpay.subscriptions.fetch(subscriptionId)
      return {
        success: true,
        subscription
      }
    } catch (error) {
      console.error('Get subscription error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(subscriptionId: string, cancelAtCycleEnd: boolean = false): Promise<any> {
    try {
      const subscription = await this.razorpay.subscriptions.cancel(subscriptionId, cancelAtCycleEnd)
      return {
        success: true,
        subscription
      }
    } catch (error) {
      console.error('Cancel subscription error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Create invoice for subscription
   */
  async createInvoice(invoiceData: {
    customerId?: string
    amount: number
    currency: string
    description: string
    dueDate?: number // Unix timestamp
    receipt?: string
  }): Promise<any> {
    try {
      const invoicePayload: any = {
        type: 'invoice',
        amount: invoiceData.amount,
        currency: invoiceData.currency,
        description: invoiceData.description,
        receipt: invoiceData.receipt
      }

      if (invoiceData.customerId) {
        invoicePayload.customer_id = invoiceData.customerId
      }

      if (invoiceData.dueDate) {
        invoicePayload.due_date = invoiceData.dueDate
      }

      const invoice = await this.razorpay.invoices.create(invoicePayload)

      return {
        success: true,
        invoice
      }
    } catch (error) {
      console.error('Create invoice error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Process webhook event
   */
  processWebhookEvent(event: any): {
    eventType: string
    entityType: string
    entityId: string
    data: any
  } {
    return {
      eventType: event.event,
      entityType: event.entity,
      entityId: event.payload?.[event.entity]?.entity?.id || '',
      data: event.payload?.[event.entity]?.entity || {}
    }
  }
}

// Create singleton instance
const razorpayConfig: RazorpayConfig = {
  keyId: process.env.RAZORPAY_KEY_ID || '',
  keySecret: process.env.RAZORPAY_KEY_SECRET || '',
  webhookSecret: process.env.RAZORPAY_WEBHOOK_SECRET || ''
}

export const razorpayService = new RazorpayService(razorpayConfig)

// Export class for testing
export { RazorpayService }
