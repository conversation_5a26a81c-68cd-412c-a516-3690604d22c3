import * as jsPDF from 'jspdf'
import { db } from '../db'
import { invoices, clients, billingCycles, subscriptions, payments } from '../db/schema'
import { eq, and, desc } from 'drizzle-orm'

interface InvoiceData {
  // Invoice details
  id: string
  invoiceNumber: string
  amount: string
  taxAmount: string | null
  totalAmount: string
  status: string | null
  issuedDate: string
  dueDate: string
  paidDate: string | null
  
  // Client details
  client: {
    schoolName: string
    schoolCode: string
    email: string
    phone: string | null
    address: string | null
    contactPerson: string | null
  }
  
  // Billing cycle details
  billingCycle?: {
    cycleStart: string
    cycleEnd: string
    studentCount: number
    baseAmount: string
    discountAmount: string | null
    setupFee: string | null
  }
  
  // Subscription details
  subscription?: {
    planName: string
    pricePerStudent: string
    billingCycle: string
    studentCount: number
  }
  
  // Payment details (if paid)
  payment?: {
    razorpayPaymentId: string | null
    paymentMethod: string | null
    processedAt: string | null
  }
}

interface PDFGenerationResult {
  success: boolean
  pdfBuffer?: Buffer
  fileName?: string
  error?: string
}

class PDFInvoiceService {
  private readonly companyInfo = {
    name: '<PERSON><PERSON><PERSON>',
    tagline: 'School Management System',
    address: 'Orionix Technologies Pvt. Ltd.',
    city: 'India',
    email: '<EMAIL>',
    website: 'https://schopio.com',
    gst: 'GST: 18AABCO1234A1Z5' // Placeholder GST number
  }

  /**
   * Generate PDF invoice for a given invoice ID
   */
  async generateInvoicePDF(invoiceId: string): Promise<PDFGenerationResult> {
    try {
      // Fetch comprehensive invoice data
      const invoiceData = await this.fetchInvoiceData(invoiceId)
      if (!invoiceData) {
        return { success: false, error: 'Invoice not found' }
      }

      // Generate PDF
      const pdfBuffer = this.createPDFDocument(invoiceData)
      const fileName = `invoice-${invoiceData.invoiceNumber}.pdf`

      return {
        success: true,
        pdfBuffer,
        fileName
      }
    } catch (error) {
      console.error('Error generating PDF invoice:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Fetch comprehensive invoice data from database
   */
  private async fetchInvoiceData(invoiceId: string): Promise<InvoiceData | null> {
    try {
      const [invoiceResult] = await db.select({
        // Invoice fields
        id: invoices.id,
        invoiceNumber: invoices.invoiceNumber,
        amount: invoices.amount,
        taxAmount: invoices.taxAmount,
        totalAmount: invoices.totalAmount,
        status: invoices.status,
        issuedDate: invoices.issuedDate,
        dueDate: invoices.dueDate,
        paidDate: invoices.paidDate,
        billingCycleId: invoices.billingCycleId,
        clientId: invoices.clientId,
        
        // Client fields
        schoolName: clients.schoolName,
        schoolCode: clients.schoolCode,
        email: clients.email,
        phone: clients.phone,
        address: clients.address,
        contactPerson: clients.contactPerson
      })
      .from(invoices)
      .leftJoin(clients, eq(invoices.clientId, clients.id))
      .where(eq(invoices.id, invoiceId))
      .limit(1)

      if (!invoiceResult) {
        return null
      }

      // Fetch billing cycle details if available
      let billingCycleData: any = null
      let subscriptionData: any = null

      if (invoiceResult.billingCycleId) {
        const [cycleResult] = await db.select({
          cycleStart: billingCycles.cycleStart,
          cycleEnd: billingCycles.cycleEnd,
          studentCount: billingCycles.studentCount,
          baseAmount: billingCycles.baseAmount,
          discountAmount: billingCycles.discountAmount,
          setupFee: billingCycles.setupFee,
          subscriptionId: billingCycles.subscriptionId
        })
        .from(billingCycles)
        .where(eq(billingCycles.id, invoiceResult.billingCycleId))
        .limit(1)

        if (cycleResult) {
          billingCycleData = cycleResult

          // Fetch subscription details
          if (cycleResult.subscriptionId) {
            const [subResult] = await db.select({
              planName: subscriptions.planName,
              pricePerStudent: subscriptions.pricePerStudent,
              billingCycle: subscriptions.billingCycle,
              studentCount: subscriptions.studentCount
            })
            .from(subscriptions)
            .where(eq(subscriptions.id, cycleResult.subscriptionId))
            .limit(1)

            if (subResult) {
              subscriptionData = subResult
            }
          }
        }
      }

      // Fetch payment details if invoice is paid
      let paymentData = null
      if (invoiceResult.status === 'paid') {
        const [paymentResult] = await db.select({
          razorpayPaymentId: payments.razorpayPaymentId,
          paymentMethod: payments.paymentMethod,
          processedAt: payments.processedAt
        })
        .from(payments)
        .where(eq(payments.invoiceId, invoiceId))
        .orderBy(desc(payments.createdAt))
        .limit(1)

        if (paymentResult) {
          paymentData = paymentResult
        }
      }

      // Structure the data
      const invoiceData: InvoiceData = {
        id: invoiceResult.id,
        invoiceNumber: invoiceResult.invoiceNumber,
        amount: invoiceResult.amount,
        taxAmount: invoiceResult.taxAmount || '0',
        totalAmount: invoiceResult.totalAmount,
        status: invoiceResult.status || 'draft',
        issuedDate: invoiceResult.issuedDate,
        dueDate: invoiceResult.dueDate,
        paidDate: invoiceResult.paidDate,

        client: {
          schoolName: invoiceResult.schoolName || 'Unknown School',
          schoolCode: invoiceResult.schoolCode || 'N/A',
          email: invoiceResult.email || 'N/A',
          phone: invoiceResult.phone,
          address: invoiceResult.address,
          contactPerson: invoiceResult.contactPerson
        },

        billingCycle: billingCycleData ? {
          cycleStart: billingCycleData.cycleStart,
          cycleEnd: billingCycleData.cycleEnd,
          studentCount: billingCycleData.studentCount,
          baseAmount: billingCycleData.baseAmount,
          discountAmount: billingCycleData.discountAmount || '0',
          setupFee: billingCycleData.setupFee || '0'
        } : undefined,

        subscription: subscriptionData ? {
          planName: subscriptionData.planName || 'Basic Plan',
          pricePerStudent: subscriptionData.pricePerStudent,
          billingCycle: subscriptionData.billingCycle,
          studentCount: subscriptionData.studentCount
        } : undefined,

        payment: paymentData ? {
          razorpayPaymentId: paymentData.razorpayPaymentId,
          paymentMethod: paymentData.paymentMethod,
          processedAt: paymentData.processedAt?.toISOString() || null
        } : undefined
      }

      return invoiceData
    } catch (error) {
      console.error('Error fetching invoice data:', error)
      return null
    }
  }

  /**
   * Create PDF document from invoice data
   */
  private createPDFDocument(data: InvoiceData): Buffer {
    const doc = new (jsPDF as any)()
    
    // Set up fonts and colors
    const primaryColor = '#2563eb' // Blue
    const secondaryColor = '#64748b' // Gray
    const accentColor = '#f59e0b' // Orange
    
    let yPosition = 20

    // Header with company branding
    yPosition = this.addHeader(doc, yPosition, primaryColor)
    
    // Invoice title and number
    yPosition = this.addInvoiceTitle(doc, data, yPosition, primaryColor)
    
    // Client and company information
    yPosition = this.addClientInfo(doc, data, yPosition, secondaryColor)
    
    // Invoice details table
    yPosition = this.addInvoiceDetails(doc, data, yPosition, primaryColor, secondaryColor)
    
    // Payment information (if paid)
    if (data.payment) {
      yPosition = this.addPaymentInfo(doc, data, yPosition, secondaryColor)
    }
    
    // Footer
    this.addFooter(doc, secondaryColor)
    
    return Buffer.from(doc.output('arraybuffer'))
  }

  /**
   * Add header with company branding
   */
  private addHeader(doc: jsPDF, yPos: number, primaryColor: string): number {
    // Company name
    doc.setFontSize(24)
    doc.setTextColor(primaryColor)
    doc.setFont('helvetica', 'bold')
    doc.text(this.companyInfo.name, 20, yPos)

    // Tagline
    doc.setFontSize(12)
    doc.setTextColor('#64748b')
    doc.setFont('helvetica', 'normal')
    doc.text(this.companyInfo.tagline, 20, yPos + 8)

    // Company details (right aligned)
    const pageWidth = doc.internal.pageSize.width
    doc.setFontSize(10)
    doc.text(this.companyInfo.address, pageWidth - 20, yPos, { align: 'right' })
    doc.text(this.companyInfo.city, pageWidth - 20, yPos + 6, { align: 'right' })
    doc.text(this.companyInfo.email, pageWidth - 20, yPos + 12, { align: 'right' })
    doc.text(this.companyInfo.website, pageWidth - 20, yPos + 18, { align: 'right' })

    // Horizontal line
    doc.setDrawColor('#e5e7eb')
    doc.line(20, yPos + 25, pageWidth - 20, yPos + 25)

    return yPos + 35
  }

  /**
   * Add invoice title and number
   */
  private addInvoiceTitle(doc: jsPDF, data: InvoiceData, yPos: number, primaryColor: string): number {
    const pageWidth = doc.internal.pageSize.width

    // Invoice title
    doc.setFontSize(20)
    doc.setTextColor(primaryColor)
    doc.setFont('helvetica', 'bold')
    doc.text('INVOICE', 20, yPos)

    // Invoice number and status
    doc.setFontSize(12)
    doc.setTextColor('#374151')
    doc.setFont('helvetica', 'normal')
    doc.text(`Invoice #: ${data.invoiceNumber}`, pageWidth - 20, yPos, { align: 'right' })

    // Status badge
    const status = data.status || 'draft'
    const statusColor = this.getStatusColor(status)
    doc.setFontSize(10)
    doc.setTextColor(statusColor)
    doc.setFont('helvetica', 'bold')
    doc.text(`Status: ${status.toUpperCase()}`, pageWidth - 20, yPos + 8, { align: 'right' })

    return yPos + 25
  }

  /**
   * Add client and billing information
   */
  private addClientInfo(doc: jsPDF, data: InvoiceData, yPos: number, secondaryColor: string): number {
    const pageWidth = doc.internal.pageSize.width
    const midPoint = pageWidth / 2

    // Bill To section
    doc.setFontSize(12)
    doc.setTextColor('#374151')
    doc.setFont('helvetica', 'bold')
    doc.text('Bill To:', 20, yPos)

    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')
    doc.text(data.client.schoolName, 20, yPos + 8)
    doc.text(`School Code: ${data.client.schoolCode}`, 20, yPos + 16)
    doc.text(`Email: ${data.client.email}`, 20, yPos + 24)

    if (data.client.contactPerson) {
      doc.text(`Contact: ${data.client.contactPerson}`, 20, yPos + 32)
    }
    if (data.client.phone) {
      doc.text(`Phone: ${data.client.phone}`, 20, yPos + 40)
    }
    if (data.client.address) {
      const addressLines = this.wrapText(doc, data.client.address, 80)
      addressLines.forEach((line, index) => {
        doc.text(line, 20, yPos + 48 + (index * 8))
      })
    }

    // Invoice Details section (right side)
    doc.setFontSize(12)
    doc.setTextColor('#374151')
    doc.setFont('helvetica', 'bold')
    doc.text('Invoice Details:', midPoint + 20, yPos)

    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')
    doc.text(`Issue Date: ${this.formatDate(data.issuedDate)}`, midPoint + 20, yPos + 8)
    doc.text(`Due Date: ${this.formatDate(data.dueDate)}`, midPoint + 20, yPos + 16)

    if (data.paidDate) {
      doc.text(`Paid Date: ${this.formatDate(data.paidDate)}`, midPoint + 20, yPos + 24)
    }

    if (data.billingCycle) {
      doc.text(`Billing Period:`, midPoint + 20, yPos + 32)
      doc.text(`${this.formatDate(data.billingCycle.cycleStart)} - ${this.formatDate(data.billingCycle.cycleEnd)}`, midPoint + 20, yPos + 40)
    }

    return yPos + 70
  }

  /**
   * Add invoice details table
   */
  private addInvoiceDetails(doc: jsPDF, data: InvoiceData, yPos: number, primaryColor: string, secondaryColor: string): number {
    const pageWidth = doc.internal.pageSize.width
    const tableWidth = pageWidth - 40
    const startX = 20

    // Table header
    doc.setFillColor(primaryColor)
    doc.rect(startX, yPos, tableWidth, 12, 'F')

    doc.setFontSize(10)
    doc.setTextColor('#ffffff')
    doc.setFont('helvetica', 'bold')
    doc.text('Description', startX + 5, yPos + 8)
    doc.text('Quantity', startX + 100, yPos + 8)
    doc.text('Rate (₹)', startX + 130, yPos + 8)
    doc.text('Amount (₹)', startX + 160, yPos + 8, { align: 'right' })

    yPos += 12

    // Service description
    let description = 'School Management System Subscription'
    if (data.subscription) {
      description = `${data.subscription.planName} - ${data.subscription.billingCycle} subscription`
    }

    // Table row
    doc.setFillColor('#f9fafb')
    doc.rect(startX, yPos, tableWidth, 10, 'F')

    doc.setFontSize(9)
    doc.setTextColor('#374151')
    doc.setFont('helvetica', 'normal')
    doc.text(description, startX + 5, yPos + 7)

    if (data.billingCycle) {
      doc.text(data.billingCycle.studentCount.toString(), startX + 100, yPos + 7)
      doc.text(`₹${parseFloat(data.subscription?.pricePerStudent || '0').toFixed(2)}`, startX + 130, yPos + 7)
    } else {
      doc.text('1', startX + 100, yPos + 7)
      doc.text(`₹${parseFloat(data.amount).toFixed(2)}`, startX + 130, yPos + 7)
    }

    doc.text(`₹${parseFloat(data.amount).toFixed(2)}`, startX + tableWidth - 5, yPos + 7, { align: 'right' })

    yPos += 10

    // Add setup fee if applicable
    if (data.billingCycle?.setupFee && parseFloat(data.billingCycle.setupFee) > 0) {
      doc.setFillColor('#ffffff')
      doc.rect(startX, yPos, tableWidth, 10, 'F')

      doc.text('Setup Fee', startX + 5, yPos + 7)
      doc.text('1', startX + 100, yPos + 7)
      doc.text(`₹${parseFloat(data.billingCycle.setupFee).toFixed(2)}`, startX + 130, yPos + 7)
      doc.text(`₹${parseFloat(data.billingCycle.setupFee).toFixed(2)}`, startX + tableWidth - 5, yPos + 7, { align: 'right' })

      yPos += 10
    }

    // Add discount if applicable
    if (data.billingCycle?.discountAmount && parseFloat(data.billingCycle.discountAmount) > 0) {
      doc.setFillColor('#f9fafb')
      doc.rect(startX, yPos, tableWidth, 10, 'F')

      doc.setTextColor('#059669') // Green for discount
      doc.text('Discount Applied', startX + 5, yPos + 7)
      doc.text('-', startX + 100, yPos + 7)
      doc.text('-', startX + 130, yPos + 7)
      doc.text(`-₹${parseFloat(data.billingCycle.discountAmount).toFixed(2)}`, startX + tableWidth - 5, yPos + 7, { align: 'right' })

      yPos += 10
    }

    // Subtotal, tax, and total
    yPos += 5

    // Subtotal
    doc.setTextColor('#374151')
    doc.setFont('helvetica', 'normal')
    doc.text('Subtotal:', startX + 120, yPos)
    doc.text(`₹${parseFloat(data.amount).toFixed(2)}`, startX + tableWidth - 5, yPos, { align: 'right' })

    yPos += 8

    // Tax
    doc.text('GST (18%):', startX + 120, yPos)
    doc.text(`₹${parseFloat(data.taxAmount || '0').toFixed(2)}`, startX + tableWidth - 5, yPos, { align: 'right' })

    yPos += 8

    // Total
    doc.setFont('helvetica', 'bold')
    doc.setFontSize(12)
    doc.text('Total Amount:', startX + 120, yPos)
    doc.text(`₹${parseFloat(data.totalAmount).toFixed(2)}`, startX + tableWidth - 5, yPos, { align: 'right' })

    // Total box
    doc.setDrawColor(primaryColor)
    doc.setLineWidth(1)
    doc.rect(startX + 115, yPos - 5, tableWidth - 115, 12)

    return yPos + 20
  }

  /**
   * Add payment information (if paid)
   */
  private addPaymentInfo(doc: jsPDF, data: InvoiceData, yPos: number, secondaryColor: string): number {
    if (!data.payment) return yPos

    doc.setFontSize(12)
    doc.setTextColor('#374151')
    doc.setFont('helvetica', 'bold')
    doc.text('Payment Information:', 20, yPos)

    yPos += 10

    doc.setFontSize(10)
    doc.setFont('helvetica', 'normal')

    if (data.payment.razorpayPaymentId) {
      doc.text(`Payment ID: ${data.payment.razorpayPaymentId}`, 20, yPos)
      yPos += 8
    }

    if (data.payment.paymentMethod) {
      doc.text(`Payment Method: ${data.payment.paymentMethod.toUpperCase()}`, 20, yPos)
      yPos += 8
    }

    if (data.payment.processedAt) {
      doc.text(`Processed At: ${this.formatDateTime(data.payment.processedAt)}`, 20, yPos)
      yPos += 8
    }

    return yPos + 10
  }

  /**
   * Add footer with terms and company info
   */
  private addFooter(doc: jsPDF, secondaryColor: string): void {
    const pageHeight = doc.internal.pageSize.height
    const pageWidth = doc.internal.pageSize.width
    const footerY = pageHeight - 40

    // Horizontal line
    doc.setDrawColor('#e5e7eb')
    doc.line(20, footerY - 10, pageWidth - 20, footerY - 10)

    // Terms and conditions
    doc.setFontSize(8)
    doc.setTextColor(secondaryColor)
    doc.setFont('helvetica', 'normal')

    const terms = [
      'Terms & Conditions:',
      '• Payment is due within 15 days of invoice date',
      '• Late payment penalty: 2% per day after grace period',
      '• Service suspension after 15 days of non-payment',
      '• All amounts are in Indian Rupees (INR)'
    ]

    terms.forEach((term, index) => {
      if (index === 0) {
        doc.setFont('helvetica', 'bold')
      } else {
        doc.setFont('helvetica', 'normal')
      }
      doc.text(term, 20, footerY + (index * 6))
    })

    // Company footer info
    doc.setFont('helvetica', 'normal')
    doc.text(`Generated by ${this.companyInfo.name} | ${this.companyInfo.email}`, pageWidth - 20, pageHeight - 10, { align: 'right' })
    doc.text(this.companyInfo.gst, pageWidth - 20, pageHeight - 4, { align: 'right' })
  }

  /**
   * Utility methods
   */
  private getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'paid': return '#059669'
      case 'overdue': return '#dc2626'
      case 'sent': return '#2563eb'
      case 'draft': return '#64748b'
      default: return '#64748b'
    }
  }

  private formatDate(dateString: string): string {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    })
  }

  private formatDateTime(dateString: string): string {
    const date = new Date(dateString)
    return date.toLocaleString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  private wrapText(doc: jsPDF, text: string, maxWidth: number): string[] {
    const words = text.split(' ')
    const lines: string[] = []
    let currentLine = ''

    words.forEach(word => {
      const testLine = currentLine + (currentLine ? ' ' : '') + word
      const textWidth = doc.getTextWidth(testLine)

      if (textWidth > maxWidth && currentLine) {
        lines.push(currentLine)
        currentLine = word
      } else {
        currentLine = testLine
      }
    })

    if (currentLine) {
      lines.push(currentLine)
    }

    return lines
  }
}

// Export singleton instance
export const pdfInvoiceService = new PDFInvoiceService()
