# Webhook Handlers & External Integrations

## 🔗 Webhook Architecture Overview

Comprehensive webhook handling system for external service integrations with proper error handling, retry mechanisms, and security.

## 🔐 Webhook Security

### Signature Verification
```typescript
import crypto from 'crypto';

interface WebhookVerification {
  verifyRazorpaySignature: (payload: string, signature: string) => boolean;
  verifyStripeSignature: (payload: string, signature: string) => boolean;
  verifyGenericSignature: (payload: string, signature: string, secret: string) => boolean;
}

const webhookSecurity: WebhookVerification = {
  verifyRazorpaySignature: (payload: string, signature: string): boolean => {
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_WEBHOOK_SECRET!)
      .update(payload)
      .digest('hex');
    
    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  },

  verifyStripeSignature: (payload: string, signature: string): boolean => {
    const elements = signature.split(',');
    const signatureHash = elements.find(el => el.startsWith('v1='))?.split('=')[1];
    
    if (!signatureHash) return false;
    
    const expectedSignature = crypto
      .createHmac('sha256', process.env.STRIPE_WEBHOOK_SECRET!)
      .update(payload)
      .digest('hex');
    
    return crypto.timingSafeEqual(
      Buffer.from(signatureHash),
      Buffer.from(expectedSignature)
    );
  },

  verifyGenericSignature: (payload: string, signature: string, secret: string): boolean => {
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex');
    
    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  }
};
```

## 💳 Razorpay Webhook Handlers

### Payment Webhook Handler
```typescript
import { Hono } from 'hono';

const razorpayWebhooks = new Hono()
  .post('/payment', async (c) => {
    try {
      const payload = await c.req.text();
      const signature = c.req.header('x-razorpay-signature');
      
      if (!signature || !webhookSecurity.verifyRazorpaySignature(payload, signature)) {
        return c.json({ error: 'Invalid signature' }, 401);
      }
      
      const event = JSON.parse(payload);
      
      switch (event.event) {
        case 'payment.captured':
          await handlePaymentCaptured(event.payload.payment.entity);
          break;
          
        case 'payment.failed':
          await handlePaymentFailed(event.payload.payment.entity);
          break;
          
        case 'order.paid':
          await handleOrderPaid(event.payload.order.entity);
          break;
          
        case 'subscription.charged':
          await handleSubscriptionCharged(event.payload.subscription.entity);
          break;
          
        default:
          console.log(`Unhandled Razorpay event: ${event.event}`);
      }
      
      return c.json({ status: 'success' });
      
    } catch (error) {
      console.error('Razorpay webhook error:', error);
      return c.json({ error: 'Webhook processing failed' }, 500);
    }
  });

// Payment captured handler
const handlePaymentCaptured = async (payment: any) => {
  try {
    // Update payment record
    await db.update(payments)
      .set({
        status: 'success',
        razorpayPaymentId: payment.id,
        processedAt: new Date(),
        paymentMethod: payment.method
      })
      .where(eq(payments.razorpayOrderId, payment.order_id));
    
    // Update invoice status
    const paymentRecord = await db.select()
      .from(payments)
      .where(eq(payments.razorpayOrderId, payment.order_id))
      .limit(1);
    
    if (paymentRecord.length > 0) {
      await db.update(invoices)
        .set({
          status: 'paid',
          paidDate: new Date()
        })
        .where(eq(invoices.id, paymentRecord[0].invoiceId));
      
      // Send payment confirmation email
      await sendPaymentConfirmationEmail(paymentRecord[0].clientId, paymentRecord[0].invoiceId);
      
      // Update subscription status if needed
      await updateSubscriptionStatus(paymentRecord[0].clientId, 'active');
    }
    
    // Log successful payment
    await logWebhookEvent('razorpay', 'payment.captured', payment.id, 'success');
    
  } catch (error) {
    console.error('Error handling payment captured:', error);
    await logWebhookEvent('razorpay', 'payment.captured', payment.id, 'error', error.message);
    throw error;
  }
};

// Payment failed handler
const handlePaymentFailed = async (payment: any) => {
  try {
    // Update payment record
    await db.update(payments)
      .set({
        status: 'failed',
        failureReason: payment.error_description,
        processedAt: new Date()
      })
      .where(eq(payments.razorpayOrderId, payment.order_id));
    
    // Send payment failure notification
    const paymentRecord = await db.select()
      .from(payments)
      .where(eq(payments.razorpayOrderId, payment.order_id))
      .limit(1);
    
    if (paymentRecord.length > 0) {
      await sendPaymentFailureEmail(paymentRecord[0].clientId, payment.error_description);
    }
    
    await logWebhookEvent('razorpay', 'payment.failed', payment.id, 'success');
    
  } catch (error) {
    console.error('Error handling payment failed:', error);
    await logWebhookEvent('razorpay', 'payment.failed', payment.id, 'error', error.message);
    throw error;
  }
};
```

## 📧 Resend Email Webhooks

### Resend Email Delivery Status Handler
```typescript
const emailWebhooks = new Hono()
  .post('/resend', async (c) => {
    try {
      const signature = c.req.header('resend-signature');
      const payload = await c.req.text();

      // Verify Resend signature (if webhook signing is enabled)
      if (process.env.RESEND_WEBHOOK_SECRET && signature) {
        if (!webhookSecurity.verifyGenericSignature(payload, signature, process.env.RESEND_WEBHOOK_SECRET)) {
          return c.json({ error: 'Invalid signature' }, 401);
        }
      }

      const event = JSON.parse(payload);
      await handleResendEvent(event);

      return c.json({ status: 'success' });

    } catch (error) {
      console.error('Resend webhook error:', error);
      return c.json({ error: 'Webhook processing failed' }, 500);
    }
  });

const handleResendEvent = async (event: any) => {
  const eventType = event.type;
  const emailId = event.data?.email_id;

  switch (eventType) {
    case 'email.sent':
      await updateEmailStatus(emailId, 'sent', event.created_at);
      break;

    case 'email.delivered':
      await updateEmailStatus(emailId, 'delivered', event.created_at);
      break;

    case 'email.delivery_delayed':
      await updateEmailStatus(emailId, 'delayed', event.created_at);
      break;

    case 'email.complained':
      await updateEmailStatus(emailId, 'complained', event.created_at);
      break;

    case 'email.bounced':
      await updateEmailStatus(emailId, 'bounced', event.created_at, event.data?.reason);
      break;

    case 'email.opened':
      await updateEmailStatus(emailId, 'opened', event.created_at);
      break;

    case 'email.clicked':
      await updateEmailStatus(emailId, 'clicked', event.created_at);
      break;

    default:
      console.log(`Unhandled Resend event: ${eventType}`);
  }
};

const updateEmailStatus = async (emailId: string, status: string, timestamp: string, reason?: string) => {
  try {
    await db.update(emailLogs)
      .set({
        status: status,
        deliveredAt: status === 'delivered' ? new Date(timestamp) : null,
        openedAt: status === 'opened' ? new Date(timestamp) : null,
        clickedAt: status === 'clicked' ? new Date(timestamp) : null,
        bounceReason: reason,
        updatedAt: new Date()
      })
      .where(eq(emailLogs.emailId, emailId));

  } catch (error) {
    console.error('Error updating email status:', error);
  }
};
```



## 🔄 Retry Mechanism

### Webhook Retry Logic
```typescript
interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

const defaultRetryConfig: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 30000, // 30 seconds
  backoffMultiplier: 2
};

const retryWebhook = async (
  webhookFn: () => Promise<any>,
  config: RetryConfig = defaultRetryConfig
): Promise<any> => {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      return await webhookFn();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === config.maxRetries) {
        break;
      }
      
      const delay = Math.min(
        config.baseDelay * Math.pow(config.backoffMultiplier, attempt),
        config.maxDelay
      );
      
      console.log(`Webhook attempt ${attempt + 1} failed, retrying in ${delay}ms`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
};

// Usage in webhook handlers
const processWebhookWithRetry = async (webhookData: any) => {
  return await retryWebhook(async () => {
    // Your webhook processing logic here
    return await processWebhookData(webhookData);
  });
};
```

## 📊 Webhook Monitoring & Logging

### Webhook Event Logging
```typescript
interface WebhookLog {
  id: string;
  service: string;
  eventType: string;
  eventId: string;
  status: 'success' | 'error' | 'retry';
  errorMessage?: string;
  processingTime: number;
  timestamp: Date;
}

const logWebhookEvent = async (
  service: string,
  eventType: string,
  eventId: string,
  status: 'success' | 'error' | 'retry',
  errorMessage?: string
) => {
  try {
    await db.insert(webhookLogs).values({
      id: generateUUID(),
      service,
      eventType,
      eventId,
      status,
      errorMessage,
      processingTime: Date.now() - startTime,
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Error logging webhook event:', error);
  }
};

// Webhook monitoring dashboard data
const getWebhookStats = async (timeframe: string = '24h') => {
  const stats = await db.select({
    service: webhookLogs.service,
    eventType: webhookLogs.eventType,
    successCount: sql`COUNT(CASE WHEN status = 'success' THEN 1 END)`,
    errorCount: sql`COUNT(CASE WHEN status = 'error' THEN 1 END)`,
    avgProcessingTime: sql`AVG(processing_time)`
  })
  .from(webhookLogs)
  .where(sql`timestamp > NOW() - INTERVAL '${timeframe}'`)
  .groupBy(webhookLogs.service, webhookLogs.eventType);
  
  return stats;
};
```

## 🚨 Error Handling & Alerting

### Webhook Error Handling
```typescript
const webhookErrorHandler = async (error: Error, context: any) => {
  // Log error details
  console.error('Webhook processing error:', {
    error: error.message,
    stack: error.stack,
    context
  });
  
  // Send alert for critical errors
  if (isCriticalError(error)) {
    await sendWebhookAlert({
      service: context.service,
      eventType: context.eventType,
      error: error.message,
      timestamp: new Date()
    });
  }
  
  // Store failed webhook for manual review
  await storeFailedWebhook({
    service: context.service,
    payload: context.payload,
    error: error.message,
    timestamp: new Date()
  });
};

const isCriticalError = (error: Error): boolean => {
  const criticalErrors = [
    'payment.captured',
    'subscription.charged',
    'invoice.payment_failed'
  ];
  
  return criticalErrors.some(critical => 
    error.message.includes(critical)
  );
};
```

## 🔧 Webhook Testing & Development

### Webhook Testing Utilities
```typescript
// Test webhook endpoint
const testWebhooks = new Hono()
  .post('/test/:service', async (c) => {
    const service = c.req.param('service');
    const testPayload = await c.req.json();
    
    try {
      // Process test webhook
      const result = await processTestWebhook(service, testPayload);
      
      return c.json({
        success: true,
        result,
        timestamp: new Date()
      });
      
    } catch (error) {
      return c.json({
        success: false,
        error: error.message,
        timestamp: new Date()
      }, 500);
    }
  });

// Webhook replay functionality
const replayWebhook = async (webhookLogId: string) => {
  const webhookLog = await getWebhookLog(webhookLogId);
  
  if (!webhookLog) {
    throw new Error('Webhook log not found');
  }
  
  // Replay the webhook with original payload
  return await processWebhookWithRetry(webhookLog.payload);
};
```

This comprehensive webhook system ensures reliable processing of external service events with proper security, monitoring, and error handling.
