/**
 * Test script for subscription pricing model validation
 * Tests the new per-student pricing model with yearly discounts
 */

interface SubscriptionTestCase {
  name: string
  pricePerStudent: number
  studentCount: number
  billingCycle: 'monthly' | 'yearly'
  yearlyDiscountPercentage: number
  expectedMonthlyAmount: number
  expectedYearlyAmount?: number
  expectedYearlyDiscount?: number
}

const testCases: SubscriptionTestCase[] = [
  {
    name: "Small School - Monthly Billing",
    pricePerStudent: 50,
    studentCount: 100,
    billingCycle: 'monthly',
    yearlyDiscountPercentage: 16.67,
    expectedMonthlyAmount: 5000, // 50 * 100
  },
  {
    name: "Small School - Yearly Billing",
    pricePerStudent: 50,
    studentCount: 100,
    billingCycle: 'yearly',
    yearlyDiscountPercentage: 16.67,
    expectedMonthlyAmount: 5000, // 50 * 100
    expectedYearlyAmount: 49998, // 5000 * 12 - 16.67% discount (60000 - 10002)
    expectedYearlyDiscount: 10002, // 60000 * 16.67%
  },
  {
    name: "Medium School - Monthly Billing",
    pricePerStudent: 40,
    studentCount: 500,
    billingCycle: 'monthly',
    yearlyDiscountPercentage: 16.67,
    expectedMonthlyAmount: 20000, // 40 * 500
  },
  {
    name: "Medium School - Yearly Billing",
    pricePerStudent: 40,
    studentCount: 500,
    billingCycle: 'yearly',
    yearlyDiscountPercentage: 16.67,
    expectedMonthlyAmount: 20000, // 40 * 500
    expectedYearlyAmount: 199992, // 20000 * 12 - 16.67% discount (240000 - 40008)
    expectedYearlyDiscount: 40008, // 240000 * 16.67%
  },
  {
    name: "Large School - Yearly Billing",
    pricePerStudent: 30,
    studentCount: 1000,
    billingCycle: 'yearly',
    yearlyDiscountPercentage: 16.67,
    expectedMonthlyAmount: 30000, // 30 * 1000
    expectedYearlyAmount: 299988, // 30000 * 12 - 16.67% discount (360000 - 60012)
    expectedYearlyDiscount: 60012, // 360000 * 16.67%
  }
]

/**
 * Calculate subscription pricing based on the new model
 */
function calculateSubscriptionPricing(testCase: SubscriptionTestCase) {
  const monthlyAmount = testCase.pricePerStudent * testCase.studentCount
  
  if (testCase.billingCycle === 'yearly') {
    const annualAmountBeforeDiscount = monthlyAmount * 12
    const discountAmount = (annualAmountBeforeDiscount * testCase.yearlyDiscountPercentage) / 100
    const yearlyAmount = annualAmountBeforeDiscount - discountAmount
    
    return {
      monthlyAmount,
      yearlyAmount,
      discountAmount,
      annualAmountBeforeDiscount
    }
  }
  
  return {
    monthlyAmount,
    yearlyAmount: null,
    discountAmount: 0,
    annualAmountBeforeDiscount: null
  }
}

/**
 * Validate a test case
 */
function validateTestCase(testCase: SubscriptionTestCase): boolean {
  const result = calculateSubscriptionPricing(testCase)
  
  console.log(`\n🧪 Testing: ${testCase.name}`)
  console.log(`   Price per student: ₹${testCase.pricePerStudent}`)
  console.log(`   Student count: ${testCase.studentCount}`)
  console.log(`   Billing cycle: ${testCase.billingCycle}`)
  
  // Validate monthly amount
  if (result.monthlyAmount !== testCase.expectedMonthlyAmount) {
    console.log(`❌ Monthly amount mismatch: Expected ₹${testCase.expectedMonthlyAmount}, Got ₹${result.monthlyAmount}`)
    return false
  }
  console.log(`✅ Monthly amount: ₹${result.monthlyAmount}`)
  
  // Validate yearly calculations if applicable
  if (testCase.billingCycle === 'yearly') {
    if (testCase.expectedYearlyAmount && Math.abs(result.yearlyAmount - testCase.expectedYearlyAmount) > 0.01) {
      console.log(`❌ Yearly amount mismatch: Expected ₹${testCase.expectedYearlyAmount}, Got ₹${result.yearlyAmount}`)
      return false
    }
    
    if (testCase.expectedYearlyDiscount && Math.abs(result.discountAmount - testCase.expectedYearlyDiscount) > 0.01) {
      console.log(`❌ Yearly discount mismatch: Expected ₹${testCase.expectedYearlyDiscount}, Got ₹${result.discountAmount}`)
      return false
    }
    
    console.log(`✅ Yearly amount (after discount): ₹${result.yearlyAmount}`)
    console.log(`✅ Yearly discount (${testCase.yearlyDiscountPercentage}%): ₹${result.discountAmount}`)
    console.log(`   Annual amount before discount: ₹${result.annualAmountBeforeDiscount}`)
  }
  
  return true
}

/**
 * Run all test cases
 */
function runTests() {
  console.log('🚀 Starting Subscription Pricing Model Tests\n')
  console.log('=' .repeat(60))
  
  let passedTests = 0
  let totalTests = testCases.length
  
  for (const testCase of testCases) {
    if (validateTestCase(testCase)) {
      passedTests++
    }
  }
  
  console.log('\n' + '='.repeat(60))
  console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed`)
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Subscription pricing model is working correctly.')
  } else {
    console.log('❌ Some tests failed. Please review the pricing calculations.')
  }
  
  return passedTests === totalTests
}

/**
 * Test operational expenses integration
 */
function testOperationalExpenses() {
  console.log('\n🔧 Testing Operational Expenses Integration')
  console.log('=' .repeat(60))
  
  const expenses = {
    databaseCosts: 2000,
    websiteMaintenance: 3000,
    supportCosts: 3000,
    infrastructureCosts: 2000
  }
  
  const totalExpenses = Object.values(expenses).reduce((sum, cost) => sum + cost, 0)
  const grossRevenue = 50000 // Example monthly revenue
  const netProfit = grossRevenue - totalExpenses
  const partnerSharePercentage = 40
  const partnerEarning = (netProfit * partnerSharePercentage) / 100
  
  console.log(`   Database costs: ₹${expenses.databaseCosts}`)
  console.log(`   Website maintenance: ₹${expenses.websiteMaintenance}`)
  console.log(`   Support costs: ₹${expenses.supportCosts}`)
  console.log(`   Infrastructure costs: ₹${expenses.infrastructureCosts}`)
  console.log(`   Total expenses: ₹${totalExpenses}`)
  console.log(`   Gross revenue: ₹${grossRevenue}`)
  console.log(`   Net profit: ₹${netProfit}`)
  console.log(`   Partner earning (${partnerSharePercentage}%): ₹${partnerEarning}`)
  
  console.log('✅ Operational expenses calculation working correctly')
}

// Run the tests
if (require.main === module) {
  const success = runTests()
  testOperationalExpenses()
  
  process.exit(success ? 0 : 1)
}

export { runTests, testOperationalExpenses, calculateSubscriptionPricing }
