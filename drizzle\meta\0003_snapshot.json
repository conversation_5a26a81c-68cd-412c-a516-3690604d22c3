{"id": "f2f09735-7f02-47de-9feb-6e121aa93514", "prevId": "d00a24d9-a1e0-4db5-8e0d-ec1056647ca1", "version": "7", "dialect": "postgresql", "tables": {"public.admin_users": {"name": "admin_users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password_hash": {"name": "password_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "last_login": {"name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"admin_users_email_unique": {"name": "admin_users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.billing_cycles": {"name": "billing_cycles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "subscription_id": {"name": "subscription_id", "type": "uuid", "primaryKey": false, "notNull": false}, "cycle_start": {"name": "cycle_start", "type": "date", "primaryKey": false, "notNull": true}, "cycle_end": {"name": "cycle_end", "type": "date", "primaryKey": false, "notNull": true}, "student_count": {"name": "student_count", "type": "integer", "primaryKey": false, "notNull": true}, "base_amount": {"name": "base_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "discount_amount": {"name": "discount_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "tax_amount": {"name": "tax_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "total_amount": {"name": "total_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "is_prorated": {"name": "is_prorated", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "prorated_days": {"name": "prorated_days", "type": "integer", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "due_date": {"name": "due_date", "type": "date", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"billing_cycles_subscription_id_subscriptions_id_fk": {"name": "billing_cycles_subscription_id_subscriptions_id_fk", "tableFrom": "billing_cycles", "tableTo": "subscriptions", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.client_users": {"name": "client_users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password_hash": {"name": "password_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'admin'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "otp_code": {"name": "otp_code", "type": "<PERSON><PERSON><PERSON>(6)", "primaryKey": false, "notNull": false}, "otp_expires_at": {"name": "otp_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_login": {"name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"client_users_client_id_clients_id_fk": {"name": "client_users_client_id_clients_id_fk", "tableFrom": "client_users", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"client_users_email_unique": {"name": "client_users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.clients": {"name": "clients", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "lead_id": {"name": "lead_id", "type": "uuid", "primaryKey": false, "notNull": false}, "school_name": {"name": "school_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "school_code": {"name": "school_code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "contact_person": {"name": "contact_person", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "actual_student_count": {"name": "actual_student_count", "type": "integer", "primaryKey": false, "notNull": true}, "estimated_student_count": {"name": "estimated_student_count", "type": "integer", "primaryKey": false, "notNull": false}, "onboarding_status": {"name": "onboarding_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"clients_lead_id_leads_id_fk": {"name": "clients_lead_id_leads_id_fk", "tableFrom": "clients", "tableTo": "leads", "columnsFrom": ["lead_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"clients_school_code_unique": {"name": "clients_school_code_unique", "nullsNotDistinct": false, "columns": ["school_code"]}, "clients_email_unique": {"name": "clients_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.demo_bookings": {"name": "demo_bookings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "lead_id": {"name": "lead_id", "type": "uuid", "primaryKey": false, "notNull": false}, "scheduled_date": {"name": "scheduled_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "demo_type": {"name": "demo_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'scheduled'"}, "meeting_link": {"name": "meeting_link", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"demo_bookings_lead_id_leads_id_fk": {"name": "demo_bookings_lead_id_leads_id_fk", "tableFrom": "demo_bookings", "tableTo": "leads", "columnsFrom": ["lead_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invoices": {"name": "invoices", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "billing_cycle_id": {"name": "billing_cycle_id", "type": "uuid", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": false}, "invoice_number": {"name": "invoice_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "tax_amount": {"name": "tax_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "total_amount": {"name": "total_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'draft'"}, "issued_date": {"name": "issued_date", "type": "date", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "date", "primaryKey": false, "notNull": true}, "paid_date": {"name": "paid_date", "type": "date", "primaryKey": false, "notNull": false}, "pdf_url": {"name": "pdf_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"invoices_billing_cycle_id_billing_cycles_id_fk": {"name": "invoices_billing_cycle_id_billing_cycles_id_fk", "tableFrom": "invoices", "tableTo": "billing_cycles", "columnsFrom": ["billing_cycle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "invoices_client_id_clients_id_fk": {"name": "invoices_client_id_clients_id_fk", "tableFrom": "invoices", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"invoices_invoice_number_unique": {"name": "invoices_invoice_number_unique", "nullsNotDistinct": false, "columns": ["invoice_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.leads": {"name": "leads", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "school_name": {"name": "school_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "contact_person": {"name": "contact_person", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "estimated_students": {"name": "estimated_students", "type": "integer", "primaryKey": false, "notNull": false}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'new'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"leads_email_unique": {"name": "leads_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.operational_expenses": {"name": "operational_expenses", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "category_name": {"name": "category_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "amount_per_school": {"name": "amount_per_school", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "is_percentage": {"name": "is_percentage", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "percentage_value": {"name": "percentage_value", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "applies_to": {"name": "applies_to", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'all'"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"operational_expenses_created_by_admin_users_id_fk": {"name": "operational_expenses_created_by_admin_users_id_fk", "tableFrom": "operational_expenses", "tableTo": "admin_users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.partner_earnings": {"name": "partner_earnings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "partner_id": {"name": "partner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": true}, "invoice_id": {"name": "invoice_id", "type": "uuid", "primaryKey": false, "notNull": true}, "payment_id": {"name": "payment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "gross_amount": {"name": "gross_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "total_expenses": {"name": "total_expenses", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "net_profit": {"name": "net_profit", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "partner_share_percentage": {"name": "partner_share_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "partner_earning": {"name": "partner_earning", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "calculated_at": {"name": "calculated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "available_at": {"name": "available_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "withdrawn_at": {"name": "withdrawn_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expense_breakdown": {"name": "expense_breakdown", "type": "jsonb", "primaryKey": false, "notNull": false}, "calculated_by": {"name": "calculated_by", "type": "uuid", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"partner_earnings_partner_id_partners_id_fk": {"name": "partner_earnings_partner_id_partners_id_fk", "tableFrom": "partner_earnings", "tableTo": "partners", "columnsFrom": ["partner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "partner_earnings_client_id_clients_id_fk": {"name": "partner_earnings_client_id_clients_id_fk", "tableFrom": "partner_earnings", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "partner_earnings_invoice_id_invoices_id_fk": {"name": "partner_earnings_invoice_id_invoices_id_fk", "tableFrom": "partner_earnings", "tableTo": "invoices", "columnsFrom": ["invoice_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "partner_earnings_payment_id_payments_id_fk": {"name": "partner_earnings_payment_id_payments_id_fk", "tableFrom": "partner_earnings", "tableTo": "payments", "columnsFrom": ["payment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "partner_earnings_calculated_by_admin_users_id_fk": {"name": "partner_earnings_calculated_by_admin_users_id_fk", "tableFrom": "partner_earnings", "tableTo": "admin_users", "columnsFrom": ["calculated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.partner_system_config": {"name": "partner_system_config", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "config_key": {"name": "config_key", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "config_value": {"name": "config_value", "type": "text", "primaryKey": false, "notNull": true}, "data_type": {"name": "data_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "updated_by": {"name": "updated_by", "type": "uuid", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"partner_system_config_updated_by_admin_users_id_fk": {"name": "partner_system_config_updated_by_admin_users_id_fk", "tableFrom": "partner_system_config", "tableTo": "admin_users", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"partner_system_config_config_key_unique": {"name": "partner_system_config_config_key_unique", "nullsNotDistinct": false, "columns": ["config_key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.partner_transactions": {"name": "partner_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "partner_id": {"name": "partner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "transaction_type": {"name": "transaction_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "reference_id": {"name": "reference_id", "type": "uuid", "primaryKey": false, "notNull": false}, "reference_type": {"name": "reference_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "balance_before": {"name": "balance_before", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "balance_after": {"name": "balance_after", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_reversible": {"name": "is_reversible", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "reversed_at": {"name": "reversed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "reversed_by": {"name": "reversed_by", "type": "uuid", "primaryKey": false, "notNull": false}, "reversal_reason": {"name": "reversal_reason", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"partner_transactions_partner_id_partners_id_fk": {"name": "partner_transactions_partner_id_partners_id_fk", "tableFrom": "partner_transactions", "tableTo": "partners", "columnsFrom": ["partner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "partner_transactions_reversed_by_admin_users_id_fk": {"name": "partner_transactions_reversed_by_admin_users_id_fk", "tableFrom": "partner_transactions", "tableTo": "admin_users", "columnsFrom": ["reversed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.partners": {"name": "partners", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "partner_code": {"name": "partner_code", "type": "<PERSON><PERSON><PERSON>(8)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password_hash": {"name": "password_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "company_name": {"name": "company_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "bank_account_number": {"name": "bank_account_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "bank_ifsc_code": {"name": "bank_ifsc_code", "type": "<PERSON><PERSON><PERSON>(11)", "primaryKey": false, "notNull": false}, "bank_account_holder_name": {"name": "bank_account_holder_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "profit_share_percentage": {"name": "profit_share_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "last_login": {"name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"partners_created_by_admin_users_id_fk": {"name": "partners_created_by_admin_users_id_fk", "tableFrom": "partners", "tableTo": "admin_users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"partners_partner_code_unique": {"name": "partners_partner_code_unique", "nullsNotDistinct": false, "columns": ["partner_code"]}, "partners_email_unique": {"name": "partners_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment_reminders": {"name": "payment_reminders", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "invoice_id": {"name": "invoice_id", "type": "uuid", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": false}, "reminder_type": {"name": "reminder_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "sent_date": {"name": "sent_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "email_sent": {"name": "email_sent", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "sms_sent": {"name": "sms_sent", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"payment_reminders_invoice_id_invoices_id_fk": {"name": "payment_reminders_invoice_id_invoices_id_fk", "tableFrom": "payment_reminders", "tableTo": "invoices", "columnsFrom": ["invoice_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "payment_reminders_client_id_clients_id_fk": {"name": "payment_reminders_client_id_clients_id_fk", "tableFrom": "payment_reminders", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payments": {"name": "payments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "invoice_id": {"name": "invoice_id", "type": "uuid", "primaryKey": false, "notNull": false}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": false}, "razorpay_payment_id": {"name": "razorpay_payment_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "razorpay_order_id": {"name": "razorpay_order_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": false, "default": "'INR'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "payment_method": {"name": "payment_method", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "failure_reason": {"name": "failure_reason", "type": "text", "primaryKey": false, "notNull": false}, "processed_at": {"name": "processed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"payments_invoice_id_invoices_id_fk": {"name": "payments_invoice_id_invoices_id_fk", "tableFrom": "payments", "tableTo": "invoices", "columnsFrom": ["invoice_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "payments_client_id_clients_id_fk": {"name": "payments_client_id_clients_id_fk", "tableFrom": "payments", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.referral_codes": {"name": "referral_codes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "partner_id": {"name": "partner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(8)", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "max_usage": {"name": "max_usage", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "deactivated_at": {"name": "deactivated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deactivated_by": {"name": "deactivated_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"referral_codes_partner_id_partners_id_fk": {"name": "referral_codes_partner_id_partners_id_fk", "tableFrom": "referral_codes", "tableTo": "partners", "columnsFrom": ["partner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "referral_codes_deactivated_by_admin_users_id_fk": {"name": "referral_codes_deactivated_by_admin_users_id_fk", "tableFrom": "referral_codes", "tableTo": "admin_users", "columnsFrom": ["deactivated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"referral_codes_code_unique": {"name": "referral_codes_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.request_status_history": {"name": "request_status_history", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "request_id": {"name": "request_id", "type": "uuid", "primaryKey": false, "notNull": true}, "from_status": {"name": "from_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "to_status": {"name": "to_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "changed_by": {"name": "changed_by", "type": "uuid", "primaryKey": false, "notNull": false}, "change_reason": {"name": "change_reason", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"request_status_history_request_id_software_requests_id_fk": {"name": "request_status_history_request_id_software_requests_id_fk", "tableFrom": "request_status_history", "tableTo": "software_requests", "columnsFrom": ["request_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.school_referrals": {"name": "school_referrals", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": true}, "partner_id": {"name": "partner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "referral_code_id": {"name": "referral_code_id", "type": "uuid", "primaryKey": false, "notNull": true}, "referred_at": {"name": "referred_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "referral_source": {"name": "referral_source", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "applied_by": {"name": "applied_by", "type": "uuid", "primaryKey": false, "notNull": false}, "verified_at": {"name": "verified_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "verified_by": {"name": "verified_by", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"school_referrals_client_id_clients_id_fk": {"name": "school_referrals_client_id_clients_id_fk", "tableFrom": "school_referrals", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "school_referrals_partner_id_partners_id_fk": {"name": "school_referrals_partner_id_partners_id_fk", "tableFrom": "school_referrals", "tableTo": "partners", "columnsFrom": ["partner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "school_referrals_referral_code_id_referral_codes_id_fk": {"name": "school_referrals_referral_code_id_referral_codes_id_fk", "tableFrom": "school_referrals", "tableTo": "referral_codes", "columnsFrom": ["referral_code_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "school_referrals_applied_by_client_users_id_fk": {"name": "school_referrals_applied_by_client_users_id_fk", "tableFrom": "school_referrals", "tableTo": "client_users", "columnsFrom": ["applied_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "school_referrals_verified_by_admin_users_id_fk": {"name": "school_referrals_verified_by_admin_users_id_fk", "tableFrom": "school_referrals", "tableTo": "admin_users", "columnsFrom": ["verified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.software_requests": {"name": "software_requests", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": true}, "request_type": {"name": "request_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "student_count": {"name": "student_count", "type": "integer", "primaryKey": false, "notNull": true}, "faculty_count": {"name": "faculty_count", "type": "integer", "primaryKey": false, "notNull": true}, "complete_address": {"name": "complete_address", "type": "text", "primaryKey": false, "notNull": true}, "contact_number": {"name": "contact_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "primary_email": {"name": "primary_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "class_1_fee": {"name": "class_1_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "class_4_fee": {"name": "class_4_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "class_6_fee": {"name": "class_6_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "class_10_fee": {"name": "class_10_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "class_11_fee": {"name": "class_11_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "class_12_fee": {"name": "class_12_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "class_11_12_fee": {"name": "class_11_12_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "calculated_average_fee": {"name": "calculated_average_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "terms_accepted": {"name": "terms_accepted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "terms_accepted_at": {"name": "terms_accepted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "terms_version": {"name": "terms_version", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "reviewed_by": {"name": "reviewed_by", "type": "uuid", "primaryKey": false, "notNull": false}, "review_notes": {"name": "review_notes", "type": "text", "primaryKey": false, "notNull": false}, "rejection_reason": {"name": "rejection_reason", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "approved_at": {"name": "approved_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "activated_at": {"name": "activated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"software_requests_client_id_clients_id_fk": {"name": "software_requests_client_id_clients_id_fk", "tableFrom": "software_requests", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "software_requests_reviewed_by_admin_users_id_fk": {"name": "software_requests_reviewed_by_admin_users_id_fk", "tableFrom": "software_requests", "tableTo": "admin_users", "columnsFrom": ["reviewed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscription_plans": {"name": "subscription_plans", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "billing_cycle": {"name": "billing_cycle", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "price_per_student": {"name": "price_per_student", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "discount_percentage": {"name": "discount_percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "features": {"name": "features", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscriptions": {"name": "subscriptions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": false}, "plan_id": {"name": "plan_id", "type": "uuid", "primaryKey": false, "notNull": false}, "student_count": {"name": "student_count", "type": "integer", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'active'"}, "auto_renew": {"name": "auto_renew", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"subscriptions_client_id_clients_id_fk": {"name": "subscriptions_client_id_clients_id_fk", "tableFrom": "subscriptions", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "subscriptions_plan_id_subscription_plans_id_fk": {"name": "subscriptions_plan_id_subscription_plans_id_fk", "tableFrom": "subscriptions", "tableTo": "subscription_plans", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.support_tickets": {"name": "support_tickets", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "client_id": {"name": "client_id", "type": "uuid", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "priority": {"name": "priority", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'medium'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'open'"}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "assigned_to": {"name": "assigned_to", "type": "uuid", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "resolved_at": {"name": "resolved_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"support_tickets_client_id_clients_id_fk": {"name": "support_tickets_client_id_clients_id_fk", "tableFrom": "support_tickets", "tableTo": "clients", "columnsFrom": ["client_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "support_tickets_created_by_client_users_id_fk": {"name": "support_tickets_created_by_client_users_id_fk", "tableFrom": "support_tickets", "tableTo": "client_users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.terms_conditions": {"name": "terms_conditions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "version": {"name": "version", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "effective_date": {"name": "effective_date", "type": "date", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"terms_conditions_created_by_admin_users_id_fk": {"name": "terms_conditions_created_by_admin_users_id_fk", "tableFrom": "terms_conditions", "tableTo": "admin_users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"terms_conditions_version_unique": {"name": "terms_conditions_version_unique", "nullsNotDistinct": false, "columns": ["version"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ticket_messages": {"name": "ticket_messages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "ticket_id": {"name": "ticket_id", "type": "uuid", "primaryKey": false, "notNull": false}, "sender_type": {"name": "sender_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "sender_id": {"name": "sender_id", "type": "uuid", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "attachments": {"name": "attachments", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"ticket_messages_ticket_id_support_tickets_id_fk": {"name": "ticket_messages_ticket_id_support_tickets_id_fk", "tableFrom": "ticket_messages", "tableTo": "support_tickets", "columnsFrom": ["ticket_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.withdrawal_requests": {"name": "withdrawal_requests", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "partner_id": {"name": "partner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "requested_amount": {"name": "requested_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "available_balance": {"name": "available_balance", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'pending'"}, "request_month": {"name": "request_month", "type": "date", "primaryKey": false, "notNull": true}, "requested_at": {"name": "requested_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "reviewed_at": {"name": "reviewed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "processed_at": {"name": "processed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "reviewed_by": {"name": "reviewed_by", "type": "uuid", "primaryKey": false, "notNull": false}, "processed_by": {"name": "processed_by", "type": "uuid", "primaryKey": false, "notNull": false}, "transaction_reference": {"name": "transaction_reference", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "bank_details_snapshot": {"name": "bank_details_snapshot", "type": "jsonb", "primaryKey": false, "notNull": false}, "rejection_reason": {"name": "rejection_reason", "type": "text", "primaryKey": false, "notNull": false}, "processing_fee": {"name": "processing_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "net_amount": {"name": "net_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"withdrawal_requests_partner_id_partners_id_fk": {"name": "withdrawal_requests_partner_id_partners_id_fk", "tableFrom": "withdrawal_requests", "tableTo": "partners", "columnsFrom": ["partner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "withdrawal_requests_reviewed_by_admin_users_id_fk": {"name": "withdrawal_requests_reviewed_by_admin_users_id_fk", "tableFrom": "withdrawal_requests", "tableTo": "admin_users", "columnsFrom": ["reviewed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "withdrawal_requests_processed_by_admin_users_id_fk": {"name": "withdrawal_requests_processed_by_admin_users_id_fk", "tableFrom": "withdrawal_requests", "tableTo": "admin_users", "columnsFrom": ["processed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}