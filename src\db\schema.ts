// Drizzle ORM Schema Definition
import { pgTable, uuid, varchar, text, integer, timestamp, boolean, decimal, date, jsonb, inet, check } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// ===== LEADS MANAGEMENT =====

export const leads = pgTable('leads', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  schoolName: varchar('school_name', { length: 255 }).notNull(),
  contactPerson: varchar('contact_person', { length: 255 }).notNull(),
  phone: varchar('phone', { length: 20 }),
  estimatedStudents: integer('estimated_students'),
  source: varchar('source', { length: 50 }), // 'landing_page', 'referral', 'demo_request', 'ai_chat', 'contact_form', 'other'
  status: varchar('status', { length: 20 }).default('new'), // 'new', 'contacted', 'demo_scheduled', 'proposal_sent', 'converted', 'lost'
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const demoBookings = pgTable('demo_bookings', {
  id: uuid('id').primaryKey().defaultRandom(),
  leadId: uuid('lead_id').references(() => leads.id),
  scheduledDate: timestamp('scheduled_date').notNull(),
  demoType: varchar('demo_type', { length: 20 }), // 'online', 'onsite'
  status: varchar('status', { length: 20 }).default('scheduled'), // 'scheduled', 'completed', 'cancelled', 'rescheduled'
  meetingLink: varchar('meeting_link', { length: 500 }),
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow(),
});

// ===== CLIENT MANAGEMENT =====

export const clients = pgTable('clients', {
  id: uuid('id').primaryKey().defaultRandom(),
  leadId: uuid('lead_id').references(() => leads.id),
  schoolName: varchar('school_name', { length: 255 }).notNull(),
  schoolCode: varchar('school_code', { length: 50 }).notNull().unique(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  phone: varchar('phone', { length: 20 }),
  address: text('address'),
  contactPerson: varchar('contact_person', { length: 255 }),
  actualStudentCount: integer('actual_student_count').notNull(),
  estimatedStudentCount: integer('estimated_student_count'),
  averageMonthlyFee: decimal('average_monthly_fee', { precision: 10, scale: 2 }), // Average fee per student per month (from software request)
  classFee: decimal('class_fee', { precision: 10, scale: 2 }), // Legacy field - keeping for backward compatibility
  onboardingStatus: varchar('onboarding_status', { length: 20 }).default('pending'), // 'pending', 'in_progress', 'completed'
  status: varchar('status', { length: 20 }).default('active'), // 'active', 'suspended', 'cancelled'
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const clientUsers = pgTable('client_users', {
  id: uuid('id').primaryKey().defaultRandom(),
  clientId: uuid('client_id').references(() => clients.id),
  email: varchar('email', { length: 255 }).notNull().unique(),
  passwordHash: varchar('password_hash', { length: 255 }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  role: varchar('role', { length: 20 }).default('admin'), // 'admin', 'billing', 'viewer'
  isActive: boolean('is_active').default(true),
  emailVerified: boolean('email_verified').default(false),
  otpCode: varchar('otp_code', { length: 6 }),
  otpExpiresAt: timestamp('otp_expires_at'),
  lastLogin: timestamp('last_login'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// ===== SUBSCRIPTION MANAGEMENT =====

export const subscriptionPlans = pgTable('subscription_plans', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 100 }).notNull(),
  billingCycle: varchar('billing_cycle', { length: 20 }).notNull(), // 'monthly', 'yearly'
  pricePerStudent: decimal('price_per_student', { precision: 10, scale: 2 }).notNull(),
  discountPercentage: decimal('discount_percentage', { precision: 5, scale: 2 }).default('0'),
  features: jsonb('features'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
});

export const subscriptions = pgTable('subscriptions', {
  id: uuid('id').primaryKey().defaultRandom(),
  clientId: uuid('client_id').references(() => clients.id),
  planId: uuid('plan_id').references(() => subscriptionPlans.id),
  planName: varchar('plan_name', { length: 100 }).default('Basic Plan'), // Plan name for display
  studentCount: integer('student_count').notNull(),
  monthlyAmount: decimal('monthly_amount', { precision: 10, scale: 2 }).notNull(), // Custom monthly amount per client
  startDate: date('start_date').notNull(),
  endDate: date('end_date'),
  nextBillingDate: date('next_billing_date').notNull(), // Next billing cycle date
  status: varchar('status', { length: 20 }).default('active'), // 'active', 'cancelled', 'suspended', 'pending'
  autoRenew: boolean('auto_renew').default(true),
  razorpaySubscriptionId: varchar('razorpay_subscription_id', { length: 100 }), // Razorpay subscription ID
  razorpayPlanId: varchar('razorpay_plan_id', { length: 100 }), // Razorpay plan ID
  billingCycle: varchar('billing_cycle', { length: 20 }).default('monthly'), // 'monthly', 'yearly'
  dueDate: integer('due_date').default(15), // Due date of the month (1-31)
  gracePeriodDays: integer('grace_period_days').default(3), // Grace period in days
  setupFee: decimal('setup_fee', { precision: 10, scale: 2 }).default('0'), // One-time setup fee
  discountPercentage: decimal('discount_percentage', { precision: 5, scale: 2 }).default('0'), // Discount percentage
  notes: text('notes'), // Admin notes about the subscription
  createdBy: uuid('created_by').references(() => adminUsers.id), // Admin who created the subscription
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// ===== BILLING SYSTEM =====

export const billingCycles = pgTable('billing_cycles', {
  id: uuid('id').primaryKey().defaultRandom(),
  subscriptionId: uuid('subscription_id').references(() => subscriptions.id),
  cycleStart: date('cycle_start').notNull(),
  cycleEnd: date('cycle_end').notNull(),
  studentCount: integer('student_count').notNull(),
  baseAmount: decimal('base_amount', { precision: 10, scale: 2 }).notNull(),
  discountAmount: decimal('discount_amount', { precision: 10, scale: 2 }).default('0'),
  setupFee: decimal('setup_fee', { precision: 10, scale: 2 }).default('0'), // Setup fee for this cycle
  taxAmount: decimal('tax_amount', { precision: 10, scale: 2 }).default('0'),
  totalAmount: decimal('total_amount', { precision: 10, scale: 2 }).notNull(),
  isProrated: boolean('is_prorated').default(false),
  proratedDays: integer('prorated_days'),
  status: varchar('status', { length: 20 }).default('pending'), // 'pending', 'active', 'paid', 'overdue', 'cancelled'
  dueDate: date('due_date').notNull(),
  gracePeriodDays: integer('grace_period_days').default(3), // Grace period for this cycle
  penaltyAmount: decimal('penalty_amount', { precision: 10, scale: 2 }).default('0'), // Late payment penalty
  createdAt: timestamp('created_at').defaultNow(),
});

export const invoices = pgTable('invoices', {
  id: uuid('id').primaryKey().defaultRandom(),
  billingCycleId: uuid('billing_cycle_id').references(() => billingCycles.id),
  clientId: uuid('client_id').references(() => clients.id),
  invoiceNumber: varchar('invoice_number', { length: 50 }).notNull().unique(),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  taxAmount: decimal('tax_amount', { precision: 10, scale: 2 }).default('0'),
  totalAmount: decimal('total_amount', { precision: 10, scale: 2 }).notNull(),
  status: varchar('status', { length: 20 }).default('draft'), // 'draft', 'sent', 'paid', 'overdue', 'cancelled'
  issuedDate: date('issued_date').notNull(),
  dueDate: date('due_date').notNull(),
  paidDate: date('paid_date'),
  razorpayOrderId: varchar('razorpay_order_id', { length: 100 }),
  pdfUrl: varchar('pdf_url', { length: 500 }),
  createdAt: timestamp('created_at').defaultNow(),
});

// ===== PAYMENT PROCESSING =====

export const payments = pgTable('payments', {
  id: uuid('id').primaryKey().defaultRandom(),
  invoiceId: uuid('invoice_id').references(() => invoices.id),
  clientId: uuid('client_id').references(() => clients.id),
  razorpayPaymentId: varchar('razorpay_payment_id', { length: 100 }),
  razorpayOrderId: varchar('razorpay_order_id', { length: 100 }),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  currency: varchar('currency', { length: 3 }).default('INR'),
  status: varchar('status', { length: 20 }).default('pending'), // 'pending', 'success', 'failed', 'refunded'
  paymentMethod: varchar('payment_method', { length: 50 }),
  failureReason: text('failure_reason'),
  processedAt: timestamp('processed_at'),
  createdAt: timestamp('created_at').defaultNow(),
});

export const paymentReminders = pgTable('payment_reminders', {
  id: uuid('id').primaryKey().defaultRandom(),
  invoiceId: uuid('invoice_id').references(() => invoices.id),
  clientId: uuid('client_id').references(() => clients.id),
  reminderType: varchar('reminder_type', { length: 20 }), // 'due_soon', 'overdue', 'final_notice'
  sentDate: timestamp('sent_date').notNull(),
  emailSent: boolean('email_sent').default(false),
  smsSent: boolean('sms_sent').default(false),
  createdAt: timestamp('created_at').defaultNow(),
});

// ===== SUPPORT SYSTEM =====

export const supportTickets = pgTable('support_tickets', {
  id: uuid('id').primaryKey().defaultRandom(),
  clientId: uuid('client_id').references(() => clients.id),
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description').notNull(),
  priority: varchar('priority', { length: 20 }).default('medium'), // 'low', 'medium', 'high', 'urgent'
  status: varchar('status', { length: 20 }).default('open'), // 'open', 'in_progress', 'resolved', 'closed'
  category: varchar('category', { length: 50 }), // 'billing', 'technical', 'feature_request', 'bug'
  assignedTo: uuid('assigned_to'),
  createdBy: uuid('created_by').references(() => clientUsers.id),
  resolvedAt: timestamp('resolved_at'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const ticketMessages = pgTable('ticket_messages', {
  id: uuid('id').primaryKey().defaultRandom(),
  ticketId: uuid('ticket_id').references(() => supportTickets.id),
  senderType: varchar('sender_type', { length: 20 }).notNull(), // 'client', 'admin'
  senderId: uuid('sender_id').notNull(),
  message: text('message').notNull(),
  attachments: jsonb('attachments'),
  createdAt: timestamp('created_at').defaultNow(),
})

// Audit Logs Table
export const auditLogs = pgTable('audit_logs', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => clientUsers.id),
  adminId: uuid('admin_id').references(() => adminUsers.id),
  clientId: uuid('client_id').references(() => clients.id),
  action: varchar('action', { length: 100 }).notNull(),
  resource: varchar('resource', { length: 100 }).notNull(),
  resourceId: uuid('resource_id'),
  details: jsonb('details'),
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  success: boolean('success').notNull(),
  errorMessage: text('error_message'),
  severity: varchar('severity', { length: 20 }).notNull(), // 'low', 'medium', 'high', 'critical'
  category: varchar('category', { length: 20 }).notNull(), // 'auth', 'admin', 'payment', 'data', 'security', 'system'
  timestamp: timestamp('timestamp').defaultNow().notNull(),
})

// Rate Limiting Table
export const rateLimits = pgTable('rate_limits', {
  id: uuid('id').primaryKey().defaultRandom(),
  identifier: varchar('identifier', { length: 255 }).notNull(), // IP address or user ID
  endpoint: varchar('endpoint', { length: 255 }).notNull(),
  requestCount: integer('request_count').default(1),
  windowStart: timestamp('window_start').defaultNow().notNull(),
  lastRequest: timestamp('last_request').defaultNow().notNull(),
  blocked: boolean('blocked').default(false),
  blockedUntil: timestamp('blocked_until'),
})

// Security Events Table
export const securityEvents = pgTable('security_events', {
  id: uuid('id').primaryKey().defaultRandom(),
  eventType: varchar('event_type', { length: 50 }).notNull(),
  userId: uuid('user_id').references(() => clientUsers.id),
  adminId: uuid('admin_id').references(() => adminUsers.id),
  ipAddress: varchar('ip_address', { length: 45 }).notNull(),
  userAgent: text('user_agent'),
  details: jsonb('details'),
  severity: varchar('severity', { length: 20 }).notNull(),
  resolved: boolean('resolved').default(false),
  resolvedBy: uuid('resolved_by').references(() => adminUsers.id),
  resolvedAt: timestamp('resolved_at'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// ===== ADMIN USERS =====

export const adminUsers = pgTable('admin_users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  passwordHash: varchar('password_hash', { length: 255 }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  role: varchar('role', { length: 20 }).notNull(), // 'super_admin', 'sales', 'support', 'billing'
  permissions: jsonb('permissions'),
  isActive: boolean('is_active').default(true),
  lastLogin: timestamp('last_login'),
  createdAt: timestamp('created_at').defaultNow(),
});

// ===== SOFTWARE REQUEST WORKFLOW =====

export const termsConditions = pgTable('terms_conditions', {
  id: uuid('id').primaryKey().defaultRandom(),
  version: varchar('version', { length: 10 }).notNull().unique(),
  title: varchar('title', { length: 255 }).notNull(),
  content: text('content').notNull(),
  effectiveDate: date('effective_date').notNull(),
  isActive: boolean('is_active').default(true),
  createdBy: uuid('created_by').references(() => adminUsers.id),
  createdAt: timestamp('created_at').defaultNow(),
});

export const softwareRequests = pgTable('software_requests', {
  id: uuid('id').primaryKey().defaultRandom(),
  clientId: uuid('client_id').references(() => clients.id).notNull(),
  requestType: varchar('request_type', { length: 20 }).notNull(), // 'demo', 'production'

  // School operational data
  studentCount: integer('student_count').notNull(),
  facultyCount: integer('faculty_count').notNull(),
  completeAddress: text('complete_address').notNull(),
  contactNumber: varchar('contact_number', { length: 20 }).notNull(),
  primaryEmail: varchar('primary_email', { length: 255 }).notNull(),

  // Fee structure (for production requests only) - Simplified to single average fee
  averageMonthlyFee: decimal('average_monthly_fee', { precision: 10, scale: 2 }), // Average fee per student per month

  // Legacy fields - keeping for backward compatibility and data migration
  class1Fee: decimal('class_1_fee', { precision: 10, scale: 2 }),
  class4Fee: decimal('class_4_fee', { precision: 10, scale: 2 }),
  class6Fee: decimal('class_6_fee', { precision: 10, scale: 2 }),
  class10Fee: decimal('class_10_fee', { precision: 10, scale: 2 }),
  class11Fee: decimal('class_11_fee', { precision: 10, scale: 2 }),
  class12Fee: decimal('class_12_fee', { precision: 10, scale: 2 }),
  class1112Fee: decimal('class_11_12_fee', { precision: 10, scale: 2 }), // Legacy field - keeping for backward compatibility
  calculatedAverageFee: decimal('calculated_average_fee', { precision: 10, scale: 2 }), // Legacy field - replaced by averageMonthlyFee

  // Request status workflow
  status: varchar('status', { length: 20 }).default('pending'),
  // 'pending', 'under_review', 'approved', 'rejected', 'setup_in_progress', 'activated'

  // Terms acceptance (production only)
  termsAccepted: boolean('terms_accepted').default(false),
  termsAcceptedAt: timestamp('terms_accepted_at'),
  termsVersion: varchar('terms_version', { length: 10 }),
  ipAddress: varchar('ip_address', { length: 45 }), // IPv4/IPv6 support
  userAgent: text('user_agent'),

  // Admin workflow
  reviewedBy: uuid('reviewed_by').references(() => adminUsers.id),
  reviewNotes: text('review_notes'),
  rejectionReason: text('rejection_reason'),

  // Timestamps
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
  approvedAt: timestamp('approved_at'),
  activatedAt: timestamp('activated_at'),
});

export const requestStatusHistory = pgTable('request_status_history', {
  id: uuid('id').primaryKey().defaultRandom(),
  requestId: uuid('request_id').references(() => softwareRequests.id).notNull(),
  fromStatus: varchar('from_status', { length: 20 }),
  toStatus: varchar('to_status', { length: 20 }).notNull(),
  changedBy: uuid('changed_by'), // admin_user_id or client_user_id
  changeReason: text('change_reason'),
  metadata: jsonb('metadata'),
  createdAt: timestamp('created_at').defaultNow(),
});

// ===== RELATIONS =====

export const leadsRelations = relations(leads, ({ one, many }) => ({
  client: one(clients, { fields: [leads.id], references: [clients.leadId] }),
  demoBookings: many(demoBookings),
}));

export const clientsRelations = relations(clients, ({ one, many }) => ({
  lead: one(leads, { fields: [clients.leadId], references: [leads.id] }),
  users: many(clientUsers),
  subscription: one(subscriptions, { fields: [clients.id], references: [subscriptions.clientId] }),
  invoices: many(invoices),
  payments: many(payments),
  supportTickets: many(supportTickets),
  softwareRequests: many(softwareRequests),
  schoolReferral: one(schoolReferrals, { fields: [clients.id], references: [schoolReferrals.clientId] }),
  partnerEarnings: many(partnerEarnings),
}));

export const subscriptionsRelations = relations(subscriptions, ({ one, many }) => ({
  client: one(clients, { fields: [subscriptions.clientId], references: [clients.id] }),
  plan: one(subscriptionPlans, { fields: [subscriptions.planId], references: [subscriptionPlans.id] }),
  billingCycles: many(billingCycles),
}));

export const billingCyclesRelations = relations(billingCycles, ({ one }) => ({
  subscription: one(subscriptions, { fields: [billingCycles.subscriptionId], references: [subscriptions.id] }),
  invoice: one(invoices, { fields: [billingCycles.id], references: [invoices.billingCycleId] }),
}));

export const invoicesRelations = relations(invoices, ({ one, many }) => ({
  billingCycle: one(billingCycles, { fields: [invoices.billingCycleId], references: [billingCycles.id] }),
  client: one(clients, { fields: [invoices.clientId], references: [clients.id] }),
  payments: many(payments),
  reminders: many(paymentReminders),
}));

export const paymentsRelations = relations(payments, ({ one }) => ({
  invoice: one(invoices, { fields: [payments.invoiceId], references: [invoices.id] }),
  client: one(clients, { fields: [payments.clientId], references: [clients.id] }),
}));

export const supportTicketsRelations = relations(supportTickets, ({ one, many }) => ({
  client: one(clients, { fields: [supportTickets.clientId], references: [clients.id] }),
  createdBy: one(clientUsers, { fields: [supportTickets.createdBy], references: [clientUsers.id] }),
  messages: many(ticketMessages),
}));

// Software Request Relations
export const softwareRequestsRelations = relations(softwareRequests, ({ one, many }) => ({
  client: one(clients, { fields: [softwareRequests.clientId], references: [clients.id] }),
  reviewedBy: one(adminUsers, { fields: [softwareRequests.reviewedBy], references: [adminUsers.id] }),
  statusHistory: many(requestStatusHistory),
}));

export const requestStatusHistoryRelations = relations(requestStatusHistory, ({ one }) => ({
  request: one(softwareRequests, { fields: [requestStatusHistory.requestId], references: [softwareRequests.id] }),
}));

export const termsConditionsRelations = relations(termsConditions, ({ one }) => ({
  createdBy: one(adminUsers, { fields: [termsConditions.createdBy], references: [adminUsers.id] }),
}));

export const adminUsersRelations = relations(adminUsers, ({ many }) => ({
  reviewedRequests: many(softwareRequests),
  createdTerms: many(termsConditions),
  createdPartners: many(partners),
  createdExpenses: many(operationalExpenses),
  reviewedWithdrawals: many(withdrawalRequests),
  processedWithdrawals: many(withdrawalRequests),
  partnerTransactions: many(partnerTransactions),
}));

// ===== PARTNER REFERRAL SYSTEM =====

export const partners = pgTable('partners', {
  id: uuid('id').primaryKey().defaultRandom(),
  partnerCode: varchar('partner_code', { length: 8 }).notNull().unique(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  passwordHash: varchar('password_hash', { length: 255 }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  companyName: varchar('company_name', { length: 255 }),
  phone: varchar('phone', { length: 20 }).notNull(),
  address: text('address').notNull(),

  // Bank details (encrypted)
  bankAccountNumber: varchar('bank_account_number', { length: 100 }), // Encrypted
  bankIfscCode: varchar('bank_ifsc_code', { length: 11 }),
  bankAccountHolderName: varchar('bank_account_holder_name', { length: 255 }),

  // Profit sharing
  profitSharePercentage: decimal('profit_share_percentage', { precision: 5, scale: 2 }), // Individual override or NULL for global

  // Status and metadata
  isActive: boolean('is_active').default(true),
  lastLogin: timestamp('last_login'),
  emailVerified: boolean('email_verified').default(false),

  // Audit fields
  createdBy: uuid('created_by').references(() => adminUsers.id).notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const referralCodes = pgTable('referral_codes', {
  id: uuid('id').primaryKey().defaultRandom(),
  partnerId: uuid('partner_id').references(() => partners.id).notNull(),
  code: varchar('code', { length: 8 }).notNull().unique(),
  isActive: boolean('is_active').default(true),
  usageCount: integer('usage_count').default(0),
  maxUsage: integer('max_usage'), // NULL for unlimited
  createdAt: timestamp('created_at').defaultNow(),
  deactivatedAt: timestamp('deactivated_at'),
  deactivatedBy: uuid('deactivated_by').references(() => adminUsers.id),
});

export const schoolReferrals = pgTable('school_referrals', {
  id: uuid('id').primaryKey().defaultRandom(),
  clientId: uuid('client_id').references(() => clients.id).notNull(),
  partnerId: uuid('partner_id').references(() => partners.id).notNull(),
  referralCodeId: uuid('referral_code_id').references(() => referralCodes.id).notNull(),
  referredAt: timestamp('referred_at').defaultNow(),
  referralSource: varchar('referral_source', { length: 20 }).notNull(), // 'registration', 'profile_update'
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  isActive: boolean('is_active').default(true),

  // Audit trail
  appliedBy: uuid('applied_by').references(() => clientUsers.id),
  verifiedAt: timestamp('verified_at'),
  verifiedBy: uuid('verified_by').references(() => adminUsers.id),
});

export const operationalExpenses = pgTable('operational_expenses', {
  id: uuid('id').primaryKey().defaultRandom(),
  categoryName: varchar('category_name', { length: 100 }).notNull(),
  description: text('description'),

  // Amount configuration
  amountPerSchool: decimal('amount_per_school', { precision: 10, scale: 2 }),
  isPercentage: boolean('is_percentage').default(false),
  percentageValue: decimal('percentage_value', { precision: 5, scale: 2 }),

  // Applicability
  isActive: boolean('is_active').default(true),
  appliesTo: varchar('applies_to', { length: 20 }).default('all'), // 'all', 'specific_partners', 'specific_schools'

  // Audit fields
  createdBy: uuid('created_by').references(() => adminUsers.id).notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const partnerEarnings = pgTable('partner_earnings', {
  id: uuid('id').primaryKey().defaultRandom(),
  partnerId: uuid('partner_id').references(() => partners.id).notNull(),
  clientId: uuid('client_id').references(() => clients.id).notNull(),
  invoiceId: uuid('invoice_id').references(() => invoices.id).notNull(),
  paymentId: uuid('payment_id').references(() => payments.id),

  // Financial calculations
  grossAmount: decimal('gross_amount', { precision: 10, scale: 2 }).notNull(), // School payment amount
  totalExpenses: decimal('total_expenses', { precision: 10, scale: 2 }).notNull(), // Sum of all expenses
  netProfit: decimal('net_profit', { precision: 10, scale: 2 }).notNull(), // Gross - Expenses
  partnerSharePercentage: decimal('partner_share_percentage', { precision: 5, scale: 2 }).notNull(),
  partnerEarning: decimal('partner_earning', { precision: 10, scale: 2 }).notNull(), // Net profit × percentage

  // Status tracking
  status: varchar('status', { length: 20 }).default('pending'), // 'pending', 'available', 'withdrawn'
  calculatedAt: timestamp('calculated_at').defaultNow(),
  availableAt: timestamp('available_at'), // When school payment confirmed
  withdrawnAt: timestamp('withdrawn_at'),

  // Expense breakdown (JSON for transparency)
  expenseBreakdown: jsonb('expense_breakdown'), // Detailed expense categories and amounts

  // Audit trail
  calculatedBy: uuid('calculated_by').references(() => adminUsers.id),
  notes: text('notes'),
});

export const withdrawalRequests = pgTable('withdrawal_requests', {
  id: uuid('id').primaryKey().defaultRandom(),
  partnerId: uuid('partner_id').references(() => partners.id).notNull(),
  requestedAmount: decimal('requested_amount', { precision: 10, scale: 2 }).notNull(),
  availableBalance: decimal('available_balance', { precision: 10, scale: 2 }).notNull(), // Balance at time of request

  // Status workflow
  status: varchar('status', { length: 20 }).default('pending'), // 'pending', 'approved', 'processed', 'rejected'
  requestMonth: date('request_month').notNull(), // YYYY-MM-01 format for monthly limit

  // Timestamps
  requestedAt: timestamp('requested_at').defaultNow(),
  reviewedAt: timestamp('reviewed_at'),
  processedAt: timestamp('processed_at'),

  // Admin workflow
  reviewedBy: uuid('reviewed_by').references(() => adminUsers.id),
  processedBy: uuid('processed_by').references(() => adminUsers.id),
  transactionReference: varchar('transaction_reference', { length: 100 }),

  // Bank details snapshot (for audit)
  bankDetailsSnapshot: jsonb('bank_details_snapshot'),

  // Rejection handling
  rejectionReason: text('rejection_reason'),

  // Processing details
  processingFee: decimal('processing_fee', { precision: 10, scale: 2 }).default('0'),
  netAmount: decimal('net_amount', { precision: 10, scale: 2 }), // Requested - Processing fee

  // Metadata
  metadata: jsonb('metadata'), // Additional processing information
});

export const partnerTransactions = pgTable('partner_transactions', {
  id: uuid('id').primaryKey().defaultRandom(),
  partnerId: uuid('partner_id').references(() => partners.id).notNull(),

  // Transaction details
  transactionType: varchar('transaction_type', { length: 20 }).notNull(), // 'EARNING', 'WITHDRAWAL', 'ADJUSTMENT', 'BONUS', 'PENALTY'
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  description: text('description').notNull(),

  // Reference linking
  referenceId: uuid('reference_id'), // Links to earnings, withdrawals, etc.
  referenceType: varchar('reference_type', { length: 50 }), // 'partner_earning', 'withdrawal_request', 'manual_adjustment'

  // Balance tracking
  balanceBefore: decimal('balance_before', { precision: 10, scale: 2 }).notNull(),
  balanceAfter: decimal('balance_after', { precision: 10, scale: 2 }).notNull(),

  // Audit trail
  createdBy: uuid('created_by'), // admin_user_id for manual transactions
  createdAt: timestamp('created_at').defaultNow(),

  // Additional data
  metadata: jsonb('metadata'), // Additional transaction details
  isReversible: boolean('is_reversible').default(false),
  reversedAt: timestamp('reversed_at'),
  reversedBy: uuid('reversed_by').references(() => adminUsers.id),
  reversalReason: text('reversal_reason'),
});

// Global configuration for partner system
export const partnerSystemConfig = pgTable('partner_system_config', {
  id: uuid('id').primaryKey().defaultRandom(),
  configKey: varchar('config_key', { length: 100 }).notNull().unique(),
  configValue: text('config_value').notNull(),
  dataType: varchar('data_type', { length: 20 }).notNull(), // 'string', 'number', 'boolean', 'json'
  description: text('description'),
  isActive: boolean('is_active').default(true),
  updatedBy: uuid('updated_by').references(() => adminUsers.id),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// ===== PARTNER REFERRAL RELATIONS =====

export const partnersRelations = relations(partners, ({ one, many }) => ({
  createdBy: one(adminUsers, { fields: [partners.createdBy], references: [adminUsers.id] }),
  referralCodes: many(referralCodes),
  schoolReferrals: many(schoolReferrals),
  earnings: many(partnerEarnings),
  withdrawalRequests: many(withdrawalRequests),
  transactions: many(partnerTransactions),
}));

export const referralCodesRelations = relations(referralCodes, ({ one, many }) => ({
  partner: one(partners, { fields: [referralCodes.partnerId], references: [partners.id] }),
  deactivatedBy: one(adminUsers, { fields: [referralCodes.deactivatedBy], references: [adminUsers.id] }),
  schoolReferrals: many(schoolReferrals),
}));

export const schoolReferralsRelations = relations(schoolReferrals, ({ one }) => ({
  client: one(clients, { fields: [schoolReferrals.clientId], references: [clients.id] }),
  partner: one(partners, { fields: [schoolReferrals.partnerId], references: [partners.id] }),
  referralCode: one(referralCodes, { fields: [schoolReferrals.referralCodeId], references: [referralCodes.id] }),
  appliedBy: one(clientUsers, { fields: [schoolReferrals.appliedBy], references: [clientUsers.id] }),
  verifiedBy: one(adminUsers, { fields: [schoolReferrals.verifiedBy], references: [adminUsers.id] }),
}));

export const operationalExpensesRelations = relations(operationalExpenses, ({ one }) => ({
  createdBy: one(adminUsers, { fields: [operationalExpenses.createdBy], references: [adminUsers.id] }),
}));

export const partnerEarningsRelations = relations(partnerEarnings, ({ one }) => ({
  partner: one(partners, { fields: [partnerEarnings.partnerId], references: [partners.id] }),
  client: one(clients, { fields: [partnerEarnings.clientId], references: [clients.id] }),
  invoice: one(invoices, { fields: [partnerEarnings.invoiceId], references: [invoices.id] }),
  payment: one(payments, { fields: [partnerEarnings.paymentId], references: [payments.id] }),
  calculatedBy: one(adminUsers, { fields: [partnerEarnings.calculatedBy], references: [adminUsers.id] }),
}));

export const withdrawalRequestsRelations = relations(withdrawalRequests, ({ one }) => ({
  partner: one(partners, { fields: [withdrawalRequests.partnerId], references: [partners.id] }),
  reviewedBy: one(adminUsers, { fields: [withdrawalRequests.reviewedBy], references: [adminUsers.id] }),
  processedBy: one(adminUsers, { fields: [withdrawalRequests.processedBy], references: [adminUsers.id] }),
}));

export const partnerTransactionsRelations = relations(partnerTransactions, ({ one }) => ({
  partner: one(partners, { fields: [partnerTransactions.partnerId], references: [partners.id] }),
  createdBy: one(adminUsers, { fields: [partnerTransactions.createdBy], references: [adminUsers.id] }),
  reversedBy: one(adminUsers, { fields: [partnerTransactions.reversedBy], references: [adminUsers.id] }),
}));

export const partnerSystemConfigRelations = relations(partnerSystemConfig, ({ one }) => ({
  updatedBy: one(adminUsers, { fields: [partnerSystemConfig.updatedBy], references: [adminUsers.id] }),
}));
