# Schopio Subscription & Billing System Documentation

## Overview
This document outlines the comprehensive subscription and billing system for Schopio, including automated monthly billing, Razorpay integration, and security best practices.

## System Architecture

### 1. Subscription Model
- **One-time Subscription Creation**: Each client gets a single subscription when approved
- **Monthly Recurring Billing**: Automated billing cycles every month
- **Custom Pricing**: Each client can have different monthly amounts
- **Status Management**: Active, Suspended, Cancelled states

### 2. Billing Cycle Flow
```
1. Subscription Created (One-time)
   ↓
2. Monthly Billing Cycle Generated (Automated)
   ↓
3. Invoice Created with Razorpay Order
   ↓
4. Client Receives Invoice Notification
   ↓
5. Client Pays via Razorpay Gateway
   ↓
6. Payment Webhook Updates Status
   ↓
7. Next Billing Cycle Scheduled
```

### 3. Database Schema

#### Subscriptions Table
```sql
- id (UUID, Primary Key)
- clientId (UUID, Foreign Key to clients)
- planName (VARCHAR) - e.g., "Basic Plan", "Premium Plan"
- monthlyAmount (DECIMAL) - Custom amount per client
- studentCount (INTEGER) - Number of students
- status (ENUM) - 'active', 'suspended', 'cancelled'
- startDate (DATE) - Subscription start date
- nextBillingDate (DATE) - Next billing cycle date
- autoRenew (BOOLEAN) - Auto-renewal setting
- razorpaySubscriptionId (VARCHAR) - Razorpay subscription ID
- createdAt (TIMESTAMP)
- updatedAt (TIMESTAMP)
```

#### Billing Cycles Table
```sql
- id (UUID, Primary Key)
- subscriptionId (UUID, Foreign Key)
- cycleStartDate (DATE)
- cycleEndDate (DATE)
- amount (DECIMAL)
- status (ENUM) - 'pending', 'invoiced', 'paid', 'failed'
- createdAt (TIMESTAMP)
```

#### Invoices Table
```sql
- id (UUID, Primary Key)
- billingCycleId (UUID, Foreign Key)
- clientId (UUID, Foreign Key)
- invoiceNumber (VARCHAR, Unique)
- amount (DECIMAL)
- taxAmount (DECIMAL) - GST 18%
- totalAmount (DECIMAL)
- status (ENUM) - 'draft', 'sent', 'paid', 'overdue', 'cancelled'
- issuedDate (DATE)
- dueDate (DATE) - 15 days from issue
- paidDate (DATE)
- razorpayOrderId (VARCHAR) - Razorpay order ID
- razorpayPaymentId (VARCHAR) - Payment ID after success
- pdfUrl (VARCHAR) - Invoice PDF URL
- createdAt (TIMESTAMP)
```

#### Payments Table
```sql
- id (UUID, Primary Key)
- invoiceId (UUID, Foreign Key)
- clientId (UUID, Foreign Key)
- amount (DECIMAL)
- currency (VARCHAR) - 'INR'
- status (ENUM) - 'pending', 'success', 'failed'
- paymentMethod (VARCHAR) - 'razorpay', 'manual'
- razorpayPaymentId (VARCHAR)
- razorpayOrderId (VARCHAR)
- razorpaySignature (VARCHAR)
- processedAt (TIMESTAMP)
- createdAt (TIMESTAMP)
```

## Security Best Practices

### 1. Payment Security
- **PCI DSS Compliance**: Use Razorpay's secure payment gateway
- **Webhook Verification**: Verify Razorpay webhook signatures
- **HTTPS Only**: All payment-related communications over HTTPS
- **Token-based Auth**: Secure API endpoints with JWT tokens

### 2. Data Protection
- **Encryption**: Sensitive data encrypted at rest
- **Access Control**: Role-based permissions for billing operations
- **Audit Logs**: Track all billing and payment activities
- **Data Retention**: Secure storage of financial records

### 3. Business Logic Security
- **Idempotency**: Prevent duplicate billing cycles
- **Rate Limiting**: Protect payment endpoints from abuse
- **Validation**: Strict input validation for all financial data
- **Reconciliation**: Regular payment reconciliation with Razorpay

## Implementation Plan

### Phase 1: Core Subscription System
1. ✅ Database schema updates
2. ✅ Subscription creation API
3. ✅ Billing cycle automation
4. ✅ Invoice generation

### Phase 2: Razorpay Integration
1. 🔄 Razorpay subscription setup
2. 🔄 Payment gateway integration
3. 🔄 Webhook handling
4. 🔄 Client payment portal

### Phase 3: Automation & Monitoring
1. 🔄 Automated billing scheduler
2. 🔄 Payment failure handling
3. 🔄 Notification system
4. 🔄 Financial reporting

## API Endpoints

### Subscription Management
- `POST /api/admin/subscriptions` - Create subscription
- `GET /api/admin/subscriptions` - List subscriptions
- `PUT /api/admin/subscriptions/:id` - Update subscription
- `PUT /api/admin/subscriptions/:id/status` - Change status

### Billing Operations
- `POST /api/admin/billing/generate-monthly` - Generate monthly bills
- `GET /api/admin/billing/dashboard` - Billing analytics
- `POST /api/admin/invoices/:id/mark-paid` - Manual payment recording

### Client Portal
- `GET /api/client/invoices` - Client's invoices
- `POST /api/client/payments/create` - Create Razorpay order
- `POST /api/client/payments/verify` - Verify payment

### Webhooks
- `POST /api/webhooks/razorpay` - Razorpay payment webhooks

## Automated Billing Process

### Monthly Billing Scheduler
```javascript
// Runs on 1st of every month
async function generateMonthlyBilling() {
  1. Get all active subscriptions
  2. Check if billing cycle already exists for current month
  3. Create new billing cycle
  4. Generate invoice with Razorpay order
  5. Send notification to client
  6. Update next billing date
}
```

### Payment Processing Flow
```javascript
// Client initiates payment
1. Client views invoice in portal
2. Clicks "Pay Now" button
3. Razorpay order created
4. Payment gateway opens
5. Client completes payment
6. Webhook receives confirmation
7. Invoice marked as paid
8. Next billing cycle scheduled
```

## Error Handling & Recovery

### Payment Failures
- Retry mechanism for failed payments
- Grace period before suspension
- Automated dunning management
- Manual intervention options

### System Failures
- Transaction rollback on errors
- Duplicate prevention mechanisms
- Data consistency checks
- Backup and recovery procedures

## Monitoring & Analytics

### Key Metrics
- Monthly Recurring Revenue (MRR)
- Churn rate and retention
- Payment success rates
- Outstanding receivables

### Alerts & Notifications
- Failed payment alerts
- Overdue invoice notifications
- System error monitoring
- Revenue milestone tracking
