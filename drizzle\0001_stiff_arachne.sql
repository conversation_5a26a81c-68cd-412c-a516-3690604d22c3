CREATE TABLE IF NOT EXISTS "request_status_history" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"request_id" uuid NOT NULL,
	"from_status" varchar(20),
	"to_status" varchar(20) NOT NULL,
	"changed_by" uuid,
	"change_reason" text,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "software_requests" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"client_id" uuid NOT NULL,
	"request_type" varchar(20) NOT NULL,
	"student_count" integer NOT NULL,
	"faculty_count" integer NOT NULL,
	"complete_address" text NOT NULL,
	"contact_number" varchar(20) NOT NULL,
	"primary_email" varchar(255) NOT NULL,
	"class_1_fee" numeric(10, 2),
	"class_4_fee" numeric(10, 2),
	"class_6_fee" numeric(10, 2),
	"class_10_fee" numeric(10, 2),
	"class_11_12_fee" numeric(10, 2),
	"calculated_average_fee" numeric(10, 2),
	"status" varchar(20) DEFAULT 'pending',
	"terms_accepted" boolean DEFAULT false,
	"terms_accepted_at" timestamp,
	"terms_version" varchar(10),
	"ip_address" varchar(45),
	"user_agent" text,
	"reviewed_by" uuid,
	"review_notes" text,
	"rejection_reason" text,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	"approved_at" timestamp,
	"activated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "terms_conditions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"version" varchar(10) NOT NULL,
	"title" varchar(255) NOT NULL,
	"content" text NOT NULL,
	"effective_date" date NOT NULL,
	"is_active" boolean DEFAULT true,
	"created_by" uuid,
	"created_at" timestamp DEFAULT now(),
	CONSTRAINT "terms_conditions_version_unique" UNIQUE("version")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "request_status_history" ADD CONSTRAINT "request_status_history_request_id_software_requests_id_fk" FOREIGN KEY ("request_id") REFERENCES "public"."software_requests"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "software_requests" ADD CONSTRAINT "software_requests_client_id_clients_id_fk" FOREIGN KEY ("client_id") REFERENCES "public"."clients"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "software_requests" ADD CONSTRAINT "software_requests_reviewed_by_admin_users_id_fk" FOREIGN KEY ("reviewed_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "terms_conditions" ADD CONSTRAINT "terms_conditions_created_by_admin_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."admin_users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
