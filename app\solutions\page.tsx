import RoleBasedFeaturesSection from '@/components/sections/RoleBasedFeaturesSection'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Tailored Solutions for Every Role | Schopio School Management',
  description: 'Discover how Schopio provides specialized solutions for administrators, teachers, students, and parents. Role-based features designed for educational excellence.',
  keywords: 'school management solutions, educational software roles, admin features, teacher tools, student portal, parent engagement',
  openGraph: {
    title: 'Tailored Solutions for Every Role | Schopio',
    description: 'Discover how Schopio provides specialized solutions for administrators, teachers, students, and parents.',
    type: 'website',
  },
}

export default function SolutionsPage() {
  return (
    <main className="min-h-screen bg-white">
      {/* Page Header */}
      <section className="py-16 bg-gradient-to-br from-blue-50 to-emerald-50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-700 border border-emerald-200 px-4 py-2 rounded-full text-sm font-bold mb-6">
              <span className="w-2 h-2 bg-emerald-600 rounded-full"></span>
              Role-Based Solutions
            </div>
            <h1 className="text-4xl lg:text-6xl font-bold text-slate-900 mb-6">
              Tailored Solutions for 
              <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent"> Every Role</span>
            </h1>
            <p className="text-xl text-slate-600 leading-relaxed">
              Whether you&apos;re an administrator streamlining operations, a teacher focusing on student success, 
              a student tracking progress, or a parent staying connected - Schopio provides specialized 
              tools designed for your unique needs and responsibilities.
            </p>
          </div>
        </div>
      </section>

      {/* Role-Based Features Section */}
      <RoleBasedFeaturesSection />

      {/* Additional Benefits Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-slate-900 mb-4">
              One Platform, Multiple Perspectives
            </h2>
            <p className="text-lg text-slate-600 max-w-3xl mx-auto">
              Schopio&apos;s unified platform ensures seamless communication and data flow between all stakeholders 
              while providing each role with the specific tools and insights they need to excel.
            </p>
          </div>
        </div>
      </section>
    </main>
  )
}
