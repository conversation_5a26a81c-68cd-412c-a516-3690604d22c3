import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import bcrypt from 'bcryptjs'
import crypto from 'crypto'
import { db } from '@/src/db'
import {
  adminUsers,
  clients,
  clientUsers,
  leads,
  softwareRequests,
  partners,
  referralCodes,
  schoolReferrals,
  operationalExpenses,
  partnerEarnings,
  withdrawalRequests,
  partnerTransactions,
  subscriptions,
  subscriptionPlans,
  billingCycles,
  invoices,
  payments,
  paymentReminders,
  supportTickets,
  ticketMessages,
  auditLogs,
  rateLimits,
  securityEvents
} from '@/src/db/schema'
import { eq, and, desc, count, sql, inArray, lte, gte, isNotNull } from 'drizzle-orm'
import {
  adminAuthMiddleware,
  requireAdminRole,
  requirePermission,
  adminSecurityHeaders,
  adminRateLimit,
  adminLoginRateLimit,
  generateAdminToken,
  getCurrentAdmin,
  ADMIN_ROLES
} from '@/src/middleware/admin-auth'
import { auditLogger } from '@/src/services/auditLogger'
import { securityMonitor } from '@/src/services/securityMonitor'
import { inputValidator } from '@/src/services/inputValidator'
import { billingMonitor } from '@/src/services/billingMonitor'
import { dunningManager } from '@/src/services/dunningManager'
import { paymentFailureHandler } from '@/src/services/paymentFailureHandler'
import { razorpayService } from '@/src/services/razorpayService'
import { DueDateManager } from '@/src/services/dueDateManager'
import { billingScheduler } from '@/src/services/billingScheduler'
import { SubscriptionStatusManager } from '@/src/services/subscriptionStatusManager'

const app = new Hono()

// Apply security headers and rate limiting to all admin routes
app.use('*', adminSecurityHeaders)
app.use('*', adminRateLimit)

// ===== ADMIN AUTHENTICATION =====

// Validation schemas
const adminLoginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required')
})

// ===== AUTHENTICATION ENDPOINTS =====

// Admin login endpoint with enhanced security
app.post('/login', adminLoginRateLimit, zValidator('json', adminLoginSchema), async (c) => {
  const ipAddress = c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown'
  const userAgent = c.req.header('user-agent') || 'unknown'
  const requestId = crypto.randomUUID()

  try {
    const { email, password } = c.req.valid('json')

    // Enhanced input validation
    const emailValidation = inputValidator.validateEmail(email)
    if (!emailValidation.isValid) {
      await auditLogger.logAuth('failed_login', {
        email,
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'Invalid email format'
      })

      return c.json({
        error: 'Invalid email format',
        requestId
      }, 400)
    }

    const sanitizedEmail = emailValidation.sanitizedData!

    // Find admin user
    const [admin] = await db.select().from(adminUsers).where(eq(adminUsers.email, sanitizedEmail)).limit(1)

    if (!admin) {
      await auditLogger.logAuth('failed_login', {
        email: sanitizedEmail,
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'Admin not found'
      })

      await securityMonitor.logSecurityEvent({
        type: 'failed_login',
        ipAddress,
        userAgent,
        details: {
          email: sanitizedEmail,
          reason: 'Admin not found',
          requestId
        },
        severity: 'medium'
      })

      return c.json({
        error: 'Invalid email or password',
        requestId
      }, 401)
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, admin.passwordHash)
    if (!isValidPassword) {
      await auditLogger.logAuth('failed_login', {
        adminId: admin.id,
        email: admin.email,
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'Invalid password'
      })

      await securityMonitor.logSecurityEvent({
        type: 'failed_login',
        adminId: admin.id,
        ipAddress,
        userAgent,
        details: {
          email: admin.email,
          reason: 'Invalid password',
          requestId
        },
        severity: 'medium'
      })

      return c.json({
        error: 'Invalid email or password',
        requestId
      }, 401)
    }

    // Check if account is active
    if (!admin.isActive) {
      await auditLogger.logAuth('failed_login', {
        adminId: admin.id,
        email: admin.email,
        ipAddress,
        userAgent,
        success: false,
        errorMessage: 'Account deactivated'
      })

      await securityMonitor.logSecurityEvent({
        type: 'unauthorized_access',
        adminId: admin.id,
        ipAddress,
        userAgent,
        details: {
          email: admin.email,
          reason: 'Deactivated account login attempt',
          requestId
        },
        severity: 'high'
      })

      return c.json({
        error: 'Admin account is deactivated. Please contact system administrator.',
        requestId
      }, 403)
    }

    // Update last login
    await db.update(adminUsers)
      .set({ lastLogin: new Date() })
      .where(eq(adminUsers.id, admin.id))

    // Generate token
    const permissions = Array.isArray(admin.permissions) ? admin.permissions : []
    const token = generateAdminToken(admin.id, admin.email, admin.role, permissions)

    // Log successful login
    await auditLogger.logAuth('login', {
      adminId: admin.id,
      email: admin.email,
      ipAddress,
      userAgent,
      success: true
    })

    await auditLogger.logAdmin('admin_login', {
      adminId: admin.id,
      resource: 'authentication',
      details: {
        loginTime: new Date().toISOString(),
        ipAddress,
        userAgent,
        requestId
      },
      ipAddress,
      userAgent,
      success: true
    })

    return c.json({
      message: 'Admin login successful',
      token,
      admin: {
        id: admin.id,
        email: admin.email,
        name: admin.name,
        role: admin.role,
        permissions: permissions
      },
      requestId
    })

  } catch (error) {
    console.error('Admin login error:', error)

    await auditLogger.logAuth('failed_login', {
      email: 'unknown',
      ipAddress,
      userAgent,
      success: false,
      errorMessage: error instanceof Error ? error.message : 'Unknown error'
    })

    return c.json({
      error: 'Login failed. Please try again.',
      requestId
    }, 500)
  }
})

// Admin profile endpoint
app.get('/profile', adminAuthMiddleware, async (c) => {
  try {
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    return c.json({
      admin: {
        id: admin.id,
        email: admin.email,
        name: admin.name,
        role: admin.role,
        permissions: admin.permissions
      }
    })
  } catch (error) {
    console.error('Admin profile error:', error)
    return c.json({ error: 'Failed to fetch profile' }, 500)
  }
})

// ===== DASHBOARD ENDPOINTS =====

// Admin dashboard with analytics
app.get('/dashboard', adminAuthMiddleware, async (c) => {
  try {
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Get basic statistics
    const [totalLeads] = await db.select({ count: count() }).from(leads)
    const [totalClients] = await db.select({ count: count() }).from(clients)
    const [totalClientUsers] = await db.select({ count: count() }).from(clientUsers)
    const [pendingRequests] = await db.select({ count: count() }).from(softwareRequests).where(eq(softwareRequests.status, 'pending'))

    // Partner system statistics
    const [totalPartners] = await db.select({ count: count() }).from(partners)
    const [activePartners] = await db.select({ count: count() }).from(partners).where(eq(partners.isActive, true))
    const [totalReferrals] = await db.select({ count: count() }).from(schoolReferrals)
    const [pendingWithdrawals] = await db.select({ count: count() }).from(withdrawalRequests).where(eq(withdrawalRequests.status, 'pending'))

    const [partnerEarningsStats] = await db
      .select({
        totalEarnings: sql<number>`COALESCE(SUM(${partnerEarnings.partnerEarning}), 0)`,
        availableBalance: sql<number>`COALESCE(SUM(CASE WHEN ${partnerEarnings.status} = 'available' THEN ${partnerEarnings.partnerEarning} ELSE 0 END), 0)`
      })
      .from(partnerEarnings)

    // Get recent leads (last 10)
    const recentLeads = await db
      .select({
        id: leads.id,
        email: leads.email,
        schoolName: leads.schoolName,
        contactPerson: leads.contactPerson,
        estimatedStudents: leads.estimatedStudents,
        status: leads.status,
        createdAt: leads.createdAt
      })
      .from(leads)
      .orderBy(desc(leads.createdAt))
      .limit(10)

    // Get recent clients (last 10)
    const recentClients = await db
      .select({
        id: clients.id,
        schoolName: clients.schoolName,
        schoolCode: clients.schoolCode,
        email: clients.email,
        actualStudentCount: clients.actualStudentCount,
        onboardingStatus: clients.onboardingStatus,
        createdAt: clients.createdAt
      })
      .from(clients)
      .orderBy(desc(clients.createdAt))
      .limit(10)

    return c.json({
      statistics: {
        totalLeads: totalLeads.count,
        totalClients: totalClients.count,
        totalClientUsers: totalClientUsers.count,
        pendingRequests: pendingRequests.count
      },
      partnerStats: {
        totalPartners: totalPartners.count,
        activePartners: activePartners.count,
        totalReferrals: totalReferrals.count,
        pendingWithdrawals: pendingWithdrawals.count,
        totalEarnings: partnerEarningsStats?.totalEarnings || 0,
        availableBalance: partnerEarningsStats?.availableBalance || 0
      },
      recentLeads,
      recentClients,
      adminInfo: {
        name: admin.name,
        role: admin.role
      }
    })

  } catch (error) {
    console.error('Admin dashboard error:', error)
    return c.json({ error: 'Failed to fetch dashboard data' }, 500)
  }
})

// ===== LEAD MANAGEMENT =====

// Get all leads with pagination and filters
app.get('/leads', adminAuthMiddleware, requirePermission('leads:read'), async (c) => {
  try {
    const { status, page = '1', limit = '20', search } = c.req.query()

    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    // Build where conditions
    const whereConditions = []

    if (status) {
      whereConditions.push(eq(leads.status, status))
    }

    if (search) {
      whereConditions.push(
        sql`${leads.schoolName} ILIKE ${`%${search}%`} OR ${leads.email} ILIKE ${`%${search}%`} OR ${leads.contactPerson} ILIKE ${`%${search}%`}`
      )
    }

    // Execute query with conditions
    const leadsData = whereConditions.length > 0
      ? await db.select().from(leads).where(and(...whereConditions)).orderBy(desc(leads.createdAt)).limit(limitNum).offset(offset)
      : await db.select().from(leads).orderBy(desc(leads.createdAt)).limit(limitNum).offset(offset)

    // Get total count for pagination
    const [totalCount] = whereConditions.length > 0
      ? await db.select({ count: count() }).from(leads).where(and(...whereConditions))
      : await db.select({ count: count() }).from(leads)

    return c.json({
      leads: leadsData,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limitNum)
      }
    })

  } catch (error) {
    console.error('Admin leads error:', error)
    return c.json({ error: 'Failed to fetch leads' }, 500)
  }
})

// Update lead status
app.put('/leads/:id', adminAuthMiddleware, requirePermission('leads:write'), async (c) => {
  try {
    const leadId = c.req.param('id')
    const { status, notes } = await c.req.json()

    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    await db.update(leads)
      .set({
        status,
        notes,
        updatedAt: new Date()
      })
      .where(eq(leads.id, leadId))

    return c.json({
      message: 'Lead updated successfully',
      updatedBy: admin.name
    })

  } catch (error) {
    console.error('Admin update lead error:', error)
    return c.json({ error: 'Failed to update lead' }, 500)
  }
})

// ===== CLIENT MANAGEMENT =====

// Get all clients with pagination and filters
app.get('/clients', adminAuthMiddleware, requirePermission('clients:read'), async (c) => {
  try {
    const { status, page = '1', limit = '20', search, include_subscription } = c.req.query()

    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    // Build where conditions
    const whereConditions = []

    if (status) {
      whereConditions.push(eq(clients.onboardingStatus, status))
    }

    if (search) {
      whereConditions.push(
        sql`${clients.schoolName} ILIKE ${`%${search}%`} OR ${clients.email} ILIKE ${`%${search}%`} OR ${clients.schoolCode} ILIKE ${`%${search}%`}`
      )
    }

    let clientsData
    if (include_subscription === 'true') {
      // Include subscription data and partner attribution
      const query = db
        .select({
          client: clients,
          subscription: subscriptions,
          partner: {
            id: schoolReferrals.partnerId,
            name: partners.name,
            code: partners.partnerCode,
            referralCode: referralCodes.code,
            referredAt: schoolReferrals.referredAt,
            referralSource: schoolReferrals.referralSource
          }
        })
        .from(clients)
        .leftJoin(subscriptions, and(
          eq(subscriptions.clientId, clients.id),
          eq(subscriptions.status, 'active')
        ))
        .leftJoin(schoolReferrals, and(
          eq(schoolReferrals.clientId, clients.id),
          eq(schoolReferrals.isActive, true)
        ))
        .leftJoin(partners, eq(schoolReferrals.partnerId, partners.id))
        .leftJoin(referralCodes, eq(schoolReferrals.referralCodeId, referralCodes.id))

      clientsData = whereConditions.length > 0
        ? await query.where(and(...whereConditions)).orderBy(desc(clients.createdAt)).limit(limitNum).offset(offset)
        : await query.orderBy(desc(clients.createdAt)).limit(limitNum).offset(offset)

      // Flatten the data structure
      clientsData = clientsData.map(row => ({
        ...row.client,
        subscription: row.subscription,
        partner: row.partner.id ? row.partner : null
      }))
    } else {
      // Regular client data without subscription but with partner attribution
      const query = db
        .select({
          client: clients,
          partner: {
            id: schoolReferrals.partnerId,
            name: partners.name,
            code: partners.partnerCode,
            referralCode: referralCodes.code,
            referredAt: schoolReferrals.referredAt,
            referralSource: schoolReferrals.referralSource
          }
        })
        .from(clients)
        .leftJoin(schoolReferrals, and(
          eq(schoolReferrals.clientId, clients.id),
          eq(schoolReferrals.isActive, true)
        ))
        .leftJoin(partners, eq(schoolReferrals.partnerId, partners.id))
        .leftJoin(referralCodes, eq(schoolReferrals.referralCodeId, referralCodes.id))

      const rawData = whereConditions.length > 0
        ? await query.where(and(...whereConditions)).orderBy(desc(clients.createdAt)).limit(limitNum).offset(offset)
        : await query.orderBy(desc(clients.createdAt)).limit(limitNum).offset(offset)

      // Flatten the data structure
      clientsData = rawData.map(row => ({
        ...row.client,
        partner: row.partner.id ? row.partner : null
      }))
    }

    // Get total count for pagination
    const [totalCount] = whereConditions.length > 0
      ? await db.select({ count: count() }).from(clients).where(and(...whereConditions))
      : await db.select({ count: count() }).from(clients)

    return c.json({
      clients: clientsData,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limitNum)
      }
    })

  } catch (error) {
    console.error('Admin clients error:', error)
    return c.json({ error: 'Failed to fetch clients' }, 500)
  }
})

// Get client analytics including average fees
app.get('/clients/analytics', adminAuthMiddleware, requirePermission('clients:read'), async (c) => {
  try {
    // Get total clients
    const [totalClients] = await db.select({ count: count() }).from(clients)

    // Get clients with fees set
    const [clientsWithFees] = await db.select({ count: count() })
      .from(clients)
      .where(isNotNull(clients.classFee))

    // Calculate average fee across all schools
    const [avgFeeResult] = await db.select({
      averageFee: sql<number>`COALESCE(AVG(CAST(${clients.classFee} AS DECIMAL)), 0)`
    })
    .from(clients)
    .where(isNotNull(clients.classFee))

    // Get total students across all schools
    const [totalStudents] = await db.select({
      total: sql<number>`COALESCE(SUM(${clients.actualStudentCount}), 0)`
    })
    .from(clients)

    // Get fee distribution (min, max)
    const [feeRange] = await db.select({
      minFee: sql<number>`COALESCE(MIN(CAST(${clients.classFee} AS DECIMAL)), 0)`,
      maxFee: sql<number>`COALESCE(MAX(CAST(${clients.classFee} AS DECIMAL)), 0)`
    })
    .from(clients)
    .where(isNotNull(clients.classFee))

    // Get partner attribution statistics
    const [partnerAttributed] = await db.select({
      count: sql<number>`COUNT(DISTINCT ${schoolReferrals.clientId})`
    })
    .from(schoolReferrals)
    .where(eq(schoolReferrals.isActive, true))

    return c.json({
      analytics: {
        totalClients: totalClients.count,
        clientsWithFees: clientsWithFees.count,
        averageFee: Math.round(avgFeeResult.averageFee * 100) / 100, // Round to 2 decimal places
        totalStudents: totalStudents.total,
        feeRange: {
          min: feeRange.minFee,
          max: feeRange.maxFee
        },
        partnerAttributed: partnerAttributed.count
      }
    })

  } catch (error) {
    console.error('Client analytics error:', error)
    return c.json({ error: 'Failed to fetch client analytics' }, 500)
  }
})

// Get specific client details
app.get('/clients/:id', adminAuthMiddleware, requirePermission('clients:read'), async (c) => {
  try {
    const clientId = c.req.param('id')

    const [client] = await db.select().from(clients).where(eq(clients.id, clientId)).limit(1)

    if (!client) {
      return c.json({ error: 'Client not found' }, 404)
    }

    // Get client users
    const users = await db.select().from(clientUsers).where(eq(clientUsers.clientId, clientId))

    // Get software requests for this client
    const requests = await db.select().from(softwareRequests).where(eq(softwareRequests.clientId, clientId))

    return c.json({
      client,
      users,
      requests
    })

  } catch (error) {
    console.error('Admin client details error:', error)
    return c.json({ error: 'Failed to fetch client details' }, 500)
  }
})

// Update client information
app.put('/clients/:id', adminAuthMiddleware, requirePermission('clients:write'), async (c) => {
  try {
    const clientId = c.req.param('id')
    const updateData = await c.req.json()

    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    await db.update(clients)
      .set({
        ...updateData,
        updatedAt: new Date()
      })
      .where(eq(clients.id, clientId))

    return c.json({
      message: 'Client updated successfully',
      updatedBy: admin.name
    })

  } catch (error) {
    console.error('Admin update client error:', error)
    return c.json({ error: 'Failed to update client' }, 500)
  }
})

// ===== SUBSCRIPTION & BILLING MANAGEMENT =====

// Validation schemas for subscription management
const createSubscriptionSchema = z.object({
  clientId: z.string().uuid(),
  planId: z.string().uuid().optional(),
  planName: z.string().default('Basic Plan'),
  studentCount: z.number().min(1).max(10000),
  monthlyAmount: z.number().min(100).max(1000000), // Minimum ₹100, Maximum ₹10,00,000
  startDate: z.string().refine((date) => {
    const parsedDate = new Date(date)
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return parsedDate >= today
  }, { message: "Start date must be today or in the future" }),
  billingCycle: z.enum(['monthly', 'yearly']).default('monthly'),
  autoRenew: z.boolean().default(true),
  dueDate: z.number().min(1).max(31).default(15), // Due date of the month (1-31)
  gracePeriodDays: z.number().min(0).max(30).default(3), // Grace period in days
  setupFee: z.number().min(0).default(0),
  discountPercentage: z.number().min(0).max(100).default(0),
  notes: z.string().optional()
})

const updateSubscriptionSchema = z.object({
  studentCount: z.number().min(1).optional(),
  monthlyAmount: z.number().min(0).optional(),
  status: z.enum(['active', 'cancelled', 'suspended']).optional(),
  autoRenew: z.boolean().optional()
})

const generateInvoiceSchema = z.object({
  subscriptionId: z.string().uuid(),
  dueDate: z.string().optional()
})

const bulkGenerateBillingSchema = z.object({
  month: z.number().min(1).max(12),
  year: z.number().min(2024),
  dueDate: z.string().optional()
})

const updateSubscriptionAmountSchema = z.object({
  monthlyAmount: z.number().min(0),
  effectiveDate: z.string().optional()
})

// Get all subscriptions with filtering
app.get('/subscriptions', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const { status, page = '1', limit = '20', search } = c.req.query()

    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    let baseQuery = db.select({
      id: subscriptions.id,
      clientId: subscriptions.clientId,
      studentCount: subscriptions.studentCount,
      startDate: subscriptions.startDate,
      endDate: subscriptions.endDate,
      status: subscriptions.status,
      autoRenew: subscriptions.autoRenew,
      createdAt: subscriptions.createdAt,
      updatedAt: subscriptions.updatedAt,
      // Client information
      schoolName: clients.schoolName,
      schoolCode: clients.schoolCode,
      email: clients.email,
      // Plan information
      planName: subscriptionPlans.name,
      billingCycle: subscriptionPlans.billingCycle,
      pricePerStudent: subscriptionPlans.pricePerStudent
    })
    .from(subscriptions)
    .leftJoin(clients, eq(subscriptions.clientId, clients.id))
    .leftJoin(subscriptionPlans, eq(subscriptions.planId, subscriptionPlans.id))

    // Apply filters
    const conditions = []
    if (status) {
      conditions.push(eq(subscriptions.status, status))
    }

    if (search) {
      conditions.push(
        sql`${clients.schoolName} ILIKE ${`%${search}%`} OR ${clients.schoolCode} ILIKE ${`%${search}%`}`
      )
    }

    const subscriptionList = conditions.length > 0
      ? await baseQuery.where(and(...conditions))
          .orderBy(desc(subscriptions.createdAt))
          .limit(limitNum)
          .offset(offset)
      : await baseQuery
          .orderBy(desc(subscriptions.createdAt))
          .limit(limitNum)
          .offset(offset)



    // Get total count for pagination
    const [totalCount] = await db
      .select({ count: count() })
      .from(subscriptions)
      .leftJoin(clients, eq(subscriptions.clientId, clients.id))

    return c.json({
      subscriptions: subscriptionList,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limitNum)
      }
    })

  } catch (error) {
    console.error('Admin subscriptions error:', error)
    return c.json({ error: 'Failed to fetch subscriptions' }, 500)
  }
})

// Get specific subscription details
app.get('/subscriptions/:id', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const subscriptionId = c.req.param('id')

    const [subscription] = await db.select({
      id: subscriptions.id,
      clientId: subscriptions.clientId,
      planId: subscriptions.planId,
      studentCount: subscriptions.studentCount,
      startDate: subscriptions.startDate,
      endDate: subscriptions.endDate,
      status: subscriptions.status,
      autoRenew: subscriptions.autoRenew,
      createdAt: subscriptions.createdAt,
      updatedAt: subscriptions.updatedAt,
      // Client information
      schoolName: clients.schoolName,
      schoolCode: clients.schoolCode,
      email: clients.email,
      phone: clients.phone,
      // Plan information
      planName: subscriptionPlans.name,
      billingCycle: subscriptionPlans.billingCycle,
      pricePerStudent: subscriptionPlans.pricePerStudent
    })
    .from(subscriptions)
    .leftJoin(clients, eq(subscriptions.clientId, clients.id))
    .leftJoin(subscriptionPlans, eq(subscriptions.planId, subscriptionPlans.id))
    .where(eq(subscriptions.id, subscriptionId))
    .limit(1)

    if (!subscription) {
      return c.json({ error: 'Subscription not found' }, 404)
    }

    // Get billing cycles for this subscription
    const billingCyclesList = await db.select()
      .from(billingCycles)
      .where(eq(billingCycles.subscriptionId, subscriptionId))
      .orderBy(desc(billingCycles.cycleStart))

    // Get recent invoices
    const invoicesList = subscription.clientId ? await db.select()
      .from(invoices)
      .where(eq(invoices.clientId, subscription.clientId))
      .orderBy(desc(invoices.createdAt))
      .limit(10) : []

    return c.json({
      subscription,
      billingCycles: billingCyclesList,
      invoices: invoicesList
    })

  } catch (error) {
    console.error('Admin subscription details error:', error)
    return c.json({ error: 'Failed to fetch subscription details' }, 500)
  }
})

// Create new subscription for client
app.post('/subscriptions', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', createSubscriptionSchema), async (c) => {
  try {
    const subscriptionData = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Validate client exists and get client details
    const [client] = await db.select({
      id: clients.id,
      schoolName: clients.schoolName,
      email: clients.email,
      status: clients.status
    }).from(clients).where(eq(clients.id, subscriptionData.clientId)).limit(1)

    if (!client) {
      return c.json({ error: 'Client not found' }, 404)
    }

    if (client.status !== 'active') {
      return c.json({ error: 'Cannot create subscription for inactive client' }, 400)
    }

    // Check if client already has an active subscription
    const [existingSubscription] = await db.select()
      .from(subscriptions)
      .where(and(
        eq(subscriptions.clientId, subscriptionData.clientId),
        inArray(subscriptions.status, ['active', 'pending'])
      ))
      .limit(1)

    if (existingSubscription) {
      return c.json({
        error: `Client already has a ${existingSubscription.status} subscription`,
        existingSubscription: {
          id: existingSubscription.id,
          status: existingSubscription.status,
          startDate: existingSubscription.startDate
        }
      }, 400)
    }

    // Calculate dates with proper due date handling
    const startDate = new Date(subscriptionData.startDate)
    const nextBillingDate = new Date(startDate)

    // Set the due date of the month for next billing
    if (subscriptionData.billingCycle === 'monthly') {
      nextBillingDate.setMonth(nextBillingDate.getMonth() + 1)
      nextBillingDate.setDate(subscriptionData.dueDate)
    } else if (subscriptionData.billingCycle === 'yearly') {
      nextBillingDate.setFullYear(nextBillingDate.getFullYear() + 1)
      nextBillingDate.setDate(subscriptionData.dueDate)
    }

    // Calculate discounted amount
    const baseAmount = subscriptionData.monthlyAmount
    const discountAmount = (baseAmount * subscriptionData.discountPercentage) / 100
    const finalAmount = baseAmount - discountAmount + subscriptionData.setupFee

    // Begin transaction for subscription creation
    const [newSubscription] = await db.insert(subscriptions).values({
      clientId: subscriptionData.clientId,
      planId: subscriptionData.planId,
      planName: subscriptionData.planName,
      studentCount: subscriptionData.studentCount,
      monthlyAmount: finalAmount.toString(),
      startDate: startDate.toISOString().split('T')[0],
      nextBillingDate: nextBillingDate.toISOString().split('T')[0],
      billingCycle: subscriptionData.billingCycle,
      status: 'active',
      autoRenew: subscriptionData.autoRenew,
      dueDate: subscriptionData.dueDate,
      gracePeriodDays: subscriptionData.gracePeriodDays,
      setupFee: subscriptionData.setupFee.toString(),
      discountPercentage: subscriptionData.discountPercentage.toString(),
      notes: subscriptionData.notes,
      createdBy: admin.id
    }).returning()

    // Create initial billing cycle if start date is today or in the past
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    if (startDate <= today) {
      const cycleEnd = new Date(nextBillingDate)
      const dueDate = new Date(nextBillingDate)
      dueDate.setDate(dueDate.getDate() + subscriptionData.gracePeriodDays)

      await db.insert(billingCycles).values({
        subscriptionId: newSubscription.id,
        cycleStart: startDate.toISOString().split('T')[0],
        cycleEnd: cycleEnd.toISOString().split('T')[0],
        studentCount: subscriptionData.studentCount,
        baseAmount: baseAmount.toString(),
        discountAmount: discountAmount.toString(),
        setupFee: subscriptionData.setupFee.toString(),
        totalAmount: finalAmount.toString(),
        status: 'active',
        dueDate: dueDate.toISOString().split('T')[0],
        gracePeriodDays: subscriptionData.gracePeriodDays
      })
    }

    return c.json({
      message: 'Subscription created successfully',
      subscription: {
        ...newSubscription,
        client: {
          schoolName: client.schoolName,
          email: client.email
        },
        pricing: {
          baseAmount,
          discountAmount,
          setupFee: subscriptionData.setupFee,
          finalAmount
        }
      },
      createdBy: admin.name
    })

  } catch (error) {
    console.error('Create subscription error:', error)
    if (error instanceof Error) {
      return c.json({ error: `Failed to create subscription: ${error.message}` }, 500)
    }
    return c.json({ error: 'Failed to create subscription' }, 500)
  }
})

// Update subscription
app.put('/subscriptions/:id', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', updateSubscriptionSchema), async (c) => {
  try {
    const subscriptionId = c.req.param('id')
    const updateData = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Check if subscription exists
    const [existingSubscription] = await db.select()
      .from(subscriptions)
      .where(eq(subscriptions.id, subscriptionId))
      .limit(1)

    if (!existingSubscription) {
      return c.json({ error: 'Subscription not found' }, 404)
    }

    // Update subscription
    const [updatedSubscription] = await db.update(subscriptions)
      .set({
        studentCount: updateData.studentCount,
        monthlyAmount: updateData.monthlyAmount?.toString(),
        status: updateData.status,
        autoRenew: updateData.autoRenew,
        updatedAt: new Date()
      })
      .where(eq(subscriptions.id, subscriptionId))
      .returning()

    return c.json({
      message: 'Subscription updated successfully',
      subscription: updatedSubscription,
      updatedBy: admin.name
    })

  } catch (error) {
    console.error('Update subscription error:', error)
    return c.json({ error: 'Failed to update subscription' }, 500)
  }
})

// Get client subscription details
app.get('/clients/:clientId/subscription', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const clientId = c.req.param('clientId')

    const [subscription] = await db.select({
      id: subscriptions.id,
      studentCount: subscriptions.studentCount,
      startDate: subscriptions.startDate,
      endDate: subscriptions.endDate,
      status: subscriptions.status,
      autoRenew: subscriptions.autoRenew,
      // Plan information
      planName: subscriptionPlans.name,
      billingCycle: subscriptionPlans.billingCycle,
      pricePerStudent: subscriptionPlans.pricePerStudent
    })
    .from(subscriptions)
    .leftJoin(subscriptionPlans, eq(subscriptions.planId, subscriptionPlans.id))
    .where(eq(subscriptions.clientId, clientId))
    .limit(1)

    if (!subscription) {
      return c.json({ error: 'No subscription found for this client' }, 404)
    }

    // Get recent billing cycles
    const recentBillingCycles = await db.select()
      .from(billingCycles)
      .where(eq(billingCycles.subscriptionId, subscription.id))
      .orderBy(desc(billingCycles.cycleStart))
      .limit(5)

    return c.json({
      subscription,
      recentBillingCycles
    })

  } catch (error) {
    console.error('Get client subscription error:', error)
    return c.json({ error: 'Failed to fetch client subscription' }, 500)
  }
})

// ===== INVOICE MANAGEMENT =====

// Get all invoices with filtering
app.get('/invoices', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const { status, page = '1', limit = '20', search, clientId } = c.req.query()

    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    let baseQuery = db.select({
      id: invoices.id,
      invoiceNumber: invoices.invoiceNumber,
      clientId: invoices.clientId,
      amount: invoices.amount,
      taxAmount: invoices.taxAmount,
      totalAmount: invoices.totalAmount,
      status: invoices.status,
      issuedDate: invoices.issuedDate,
      dueDate: invoices.dueDate,
      paidDate: invoices.paidDate,
      createdAt: invoices.createdAt,
      // Client information
      schoolName: clients.schoolName,
      schoolCode: clients.schoolCode,
      email: clients.email
    })
    .from(invoices)
    .leftJoin(clients, eq(invoices.clientId, clients.id))

    // Apply filters
    const conditions = []
    if (status) {
      conditions.push(eq(invoices.status, status))
    }
    if (clientId) {
      conditions.push(eq(invoices.clientId, clientId))
    }
    if (search) {
      conditions.push(
        sql`${clients.schoolName} ILIKE ${`%${search}%`} OR ${invoices.invoiceNumber} ILIKE ${`%${search}%`}`
      )
    }

    const invoicesList = conditions.length > 0
      ? await baseQuery.where(and(...conditions))
          .orderBy(desc(invoices.createdAt))
          .limit(limitNum)
          .offset(offset)
      : await baseQuery
          .orderBy(desc(invoices.createdAt))
          .limit(limitNum)
          .offset(offset)

    // Get total count for pagination
    const [totalCount] = await db
      .select({ count: count() })
      .from(invoices)
      .leftJoin(clients, eq(invoices.clientId, clients.id))

    return c.json({
      invoices: invoicesList,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limitNum)
      }
    })

  } catch (error) {
    console.error('Admin invoices error:', error)
    return c.json({ error: 'Failed to fetch invoices' }, 500)
  }
})

// Generate invoice for subscription
app.post('/invoices/generate', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', generateInvoiceSchema), async (c) => {
  try {
    const { subscriptionId, dueDate } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Get subscription details
    const [subscription] = await db.select({
      id: subscriptions.id,
      clientId: subscriptions.clientId,
      studentCount: subscriptions.studentCount,
      // Plan information
      pricePerStudent: subscriptionPlans.pricePerStudent,
      billingCycle: subscriptionPlans.billingCycle
    })
    .from(subscriptions)
    .leftJoin(subscriptionPlans, eq(subscriptions.planId, subscriptionPlans.id))
    .where(eq(subscriptions.id, subscriptionId))
    .limit(1)

    if (!subscription) {
      return c.json({ error: 'Subscription not found' }, 404)
    }

    // Calculate amounts
    const baseAmount = Number(subscription.pricePerStudent) * subscription.studentCount
    const taxAmount = baseAmount * 0.18 // 18% GST
    const totalAmount = baseAmount + taxAmount

    // Generate invoice number
    const invoiceNumber = `INV-${Date.now()}-${subscription.clientId?.slice(-6).toUpperCase() || 'UNKNOWN'}`

    // Create billing cycle
    const cycleStart = new Date()
    const cycleEnd = new Date()
    if (subscription.billingCycle === 'monthly') {
      cycleEnd.setMonth(cycleEnd.getMonth() + 1)
    } else {
      cycleEnd.setFullYear(cycleEnd.getFullYear() + 1)
    }

    const [billingCycle] = await db.insert(billingCycles).values({
      subscriptionId: subscription.id,
      cycleStart: cycleStart.toISOString().split('T')[0],
      cycleEnd: cycleEnd.toISOString().split('T')[0],
      studentCount: subscription.studentCount,
      baseAmount: baseAmount.toString(),
      taxAmount: taxAmount.toString(),
      totalAmount: totalAmount.toString(),
      status: 'pending',
      dueDate: dueDate ? new Date(dueDate).toISOString().split('T')[0] : cycleEnd.toISOString().split('T')[0]
    }).returning()

    // Create invoice
    const [invoice] = await db.insert(invoices).values({
      billingCycleId: billingCycle.id,
      clientId: subscription.clientId,
      invoiceNumber,
      amount: baseAmount.toString(),
      taxAmount: taxAmount.toString(),
      totalAmount: totalAmount.toString(),
      status: 'sent',
      issuedDate: new Date().toISOString().split('T')[0],
      dueDate: dueDate ? new Date(dueDate).toISOString().split('T')[0] : cycleEnd.toISOString().split('T')[0]
    }).returning()

    return c.json({
      message: 'Invoice generated successfully',
      invoice,
      billingCycle,
      generatedBy: admin.name
    })

  } catch (error) {
    console.error('Generate invoice error:', error)
    return c.json({ error: 'Failed to generate invoice' }, 500)
  }
})

// ===== PAYMENT MANAGEMENT =====

// Get all payments with filtering
app.get('/payments', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const { status, page = '1', limit = '20', search, clientId } = c.req.query()

    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    let baseQuery = db.select({
      id: payments.id,
      invoiceId: payments.invoiceId,
      clientId: payments.clientId,
      razorpayPaymentId: payments.razorpayPaymentId,
      razorpayOrderId: payments.razorpayOrderId,
      amount: payments.amount,
      currency: payments.currency,
      status: payments.status,
      paymentMethod: payments.paymentMethod,
      processedAt: payments.processedAt,
      createdAt: payments.createdAt,
      // Client information
      schoolName: clients.schoolName,
      schoolCode: clients.schoolCode,
      // Invoice information
      invoiceNumber: invoices.invoiceNumber,
      invoiceStatus: invoices.status
    })
    .from(payments)
    .leftJoin(clients, eq(payments.clientId, clients.id))
    .leftJoin(invoices, eq(payments.invoiceId, invoices.id))

    // Apply filters
    const conditions = []
    if (status) {
      conditions.push(eq(payments.status, status))
    }
    if (clientId) {
      conditions.push(eq(payments.clientId, clientId))
    }
    if (search) {
      conditions.push(
        sql`${clients.schoolName} ILIKE ${`%${search}%`} OR ${invoices.invoiceNumber} ILIKE ${`%${search}%`}`
      )
    }

    const paymentsList = conditions.length > 0
      ? await baseQuery.where(and(...conditions))
          .orderBy(desc(payments.createdAt))
          .limit(limitNum)
          .offset(offset)
      : await baseQuery
          .orderBy(desc(payments.createdAt))
          .limit(limitNum)
          .offset(offset)

    // Get total count for pagination
    const [totalCount] = await db
      .select({ count: count() })
      .from(payments)
      .leftJoin(clients, eq(payments.clientId, clients.id))

    return c.json({
      payments: paymentsList,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limitNum)
      }
    })

  } catch (error) {
    console.error('Admin payments error:', error)
    return c.json({ error: 'Failed to fetch payments' }, 500)
  }
})

// Get payment details
app.get('/payments/:id', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const paymentId = c.req.param('id')

    const [payment] = await db.select({
      id: payments.id,
      invoiceId: payments.invoiceId,
      clientId: payments.clientId,
      razorpayPaymentId: payments.razorpayPaymentId,
      razorpayOrderId: payments.razorpayOrderId,
      amount: payments.amount,
      currency: payments.currency,
      status: payments.status,
      paymentMethod: payments.paymentMethod,
      failureReason: payments.failureReason,
      processedAt: payments.processedAt,
      createdAt: payments.createdAt,
      // Client information
      schoolName: clients.schoolName,
      schoolCode: clients.schoolCode,
      email: clients.email,
      // Invoice information
      invoiceNumber: invoices.invoiceNumber,
      invoiceAmount: invoices.totalAmount,
      invoiceStatus: invoices.status
    })
    .from(payments)
    .leftJoin(clients, eq(payments.clientId, clients.id))
    .leftJoin(invoices, eq(payments.invoiceId, invoices.id))
    .where(eq(payments.id, paymentId))
    .limit(1)

    if (!payment) {
      return c.json({ error: 'Payment not found' }, 404)
    }

    return c.json({ payment })

  } catch (error) {
    console.error('Get payment details error:', error)
    return c.json({ error: 'Failed to fetch payment details' }, 500)
  }
})

// Mark payment as verified (manual verification)
app.put('/payments/:id/verify', adminAuthMiddleware, requirePermission('billing:write'), async (c) => {
  try {
    const paymentId = c.req.param('id')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Get payment details
    const [payment] = await db.select()
      .from(payments)
      .where(eq(payments.id, paymentId))
      .limit(1)

    if (!payment) {
      return c.json({ error: 'Payment not found' }, 404)
    }

    // Update payment status
    const [updatedPayment] = await db.update(payments)
      .set({
        status: 'success',
        processedAt: new Date()
      })
      .where(eq(payments.id, paymentId))
      .returning()

    // Update invoice status
    if (payment.invoiceId) {
      await db.update(invoices)
        .set({
          status: 'paid',
          paidDate: new Date().toISOString().split('T')[0]
        })
        .where(eq(invoices.id, payment.invoiceId))
    }

    return c.json({
      message: 'Payment verified successfully',
      payment: updatedPayment,
      verifiedBy: admin.name
    })

  } catch (error) {
    console.error('Verify payment error:', error)
    return c.json({ error: 'Failed to verify payment' }, 500)
  }
})

// ===== SOFTWARE REQUEST MANAGEMENT =====

// Get all software requests with pagination and filters
app.get('/software-requests', adminAuthMiddleware, requirePermission('requests:read'), async (c) => {
  try {
    const { status, type, page = '1', limit = '20' } = c.req.query()

    const pageNum = parseInt(page)
    const limitNum = parseInt(limit)
    const offset = (pageNum - 1) * limitNum

    // Build where conditions
    const whereConditions = []

    if (status) {
      whereConditions.push(eq(softwareRequests.status, status))
    }

    if (type) {
      whereConditions.push(eq(softwareRequests.requestType, type))
    }

    // Execute query with conditions
    const baseQuery = db.select({
      id: softwareRequests.id,
      clientId: softwareRequests.clientId,
      requestType: softwareRequests.requestType,
      status: softwareRequests.status,
      studentCount: softwareRequests.studentCount,
      facultyCount: softwareRequests.facultyCount,
      calculatedAverageFee: softwareRequests.calculatedAverageFee,
      termsAccepted: softwareRequests.termsAccepted,
      createdAt: softwareRequests.createdAt,
      schoolName: clients.schoolName,
      schoolCode: clients.schoolCode,
      email: clients.email
    }).from(softwareRequests)
    .leftJoin(clients, eq(softwareRequests.clientId, clients.id))

    const requestsData = whereConditions.length > 0
      ? await baseQuery.where(and(...whereConditions)).orderBy(desc(softwareRequests.createdAt)).limit(limitNum).offset(offset)
      : await baseQuery.orderBy(desc(softwareRequests.createdAt)).limit(limitNum).offset(offset)

    // Get total count for pagination
    const [totalCount] = whereConditions.length > 0
      ? await db.select({ count: count() }).from(softwareRequests).where(and(...whereConditions))
      : await db.select({ count: count() }).from(softwareRequests)

    return c.json({
      requests: requestsData,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limitNum)
      }
    })

  } catch (error) {
    console.error('Admin software requests error:', error)
    return c.json({ error: 'Failed to fetch software requests' }, 500)
  }
})

// Update software request status
app.put('/software-requests/:id', adminAuthMiddleware, requirePermission('requests:write'), async (c) => {
  try {
    const requestId = c.req.param('id')
    const { status, reviewNotes, rejectionReason } = await c.req.json()

    const admin = getCurrentAdmin(c)

    const updateData: any = {
      status,
      reviewedBy: admin?.id,
      updatedAt: new Date()
    }

    if (reviewNotes) updateData.reviewNotes = reviewNotes
    if (rejectionReason) updateData.rejectionReason = rejectionReason

    await db.update(softwareRequests)
      .set(updateData)
      .where(eq(softwareRequests.id, requestId))

    return c.json({
      message: 'Software request updated successfully',
      updatedBy: admin?.name
    })

  } catch (error) {
    console.error('Admin update software request error:', error)
    return c.json({ error: 'Failed to update software request' }, 500)
  }
})

// ===== ADMIN USER MANAGEMENT =====

// Get all admin users (super admin only)
app.get('/admin-users', adminAuthMiddleware, requireAdminRole(['super_admin']), async (c) => {
  try {
    const adminUsersData = await db.select({
      id: adminUsers.id,
      email: adminUsers.email,
      name: adminUsers.name,
      role: adminUsers.role,
      permissions: adminUsers.permissions,
      isActive: adminUsers.isActive,
      lastLogin: adminUsers.lastLogin,
      createdAt: adminUsers.createdAt
    }).from(adminUsers).orderBy(desc(adminUsers.createdAt))

    return c.json({
      adminUsers: adminUsersData,
      roles: ADMIN_ROLES
    })

  } catch (error) {
    console.error('Admin users fetch error:', error)
    return c.json({ error: 'Failed to fetch admin users' }, 500)
  }
})

// Create new admin user (super admin only)
app.post('/admin-users', adminAuthMiddleware, requireAdminRole(['super_admin']), zValidator('json', z.object({
  email: z.string().email(),
  name: z.string().min(2),
  role: z.enum(['super_admin', 'sales', 'support', 'billing']),
  password: z.string().min(8)
})), async (c) => {
  try {
    const { email, name, role, password } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    // Check if email already exists
    const [existingAdmin] = await db.select().from(adminUsers).where(eq(adminUsers.email, email)).limit(1)
    if (existingAdmin) {
      return c.json({ error: 'Email already exists' }, 400)
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, 12)

    // Get default permissions for role
    const permissions = ADMIN_ROLES[role as keyof typeof ADMIN_ROLES]?.permissions || []

    // Create admin user
    const [newAdmin] = await db.insert(adminUsers).values({
      email,
      name,
      role,
      passwordHash,
      permissions,
      isActive: true
    }).returning()

    return c.json({
      message: 'Admin user created successfully',
      adminUser: {
        id: newAdmin.id,
        email: newAdmin.email,
        name: newAdmin.name,
        role: newAdmin.role,
        permissions: newAdmin.permissions
      },
      createdBy: admin?.name
    })

  } catch (error) {
    console.error('Admin user creation error:', error)
    return c.json({ error: 'Failed to create admin user' }, 500)
  }
})

// Update admin user (super admin only)
app.put('/admin-users/:id', adminAuthMiddleware, requireAdminRole(['super_admin']), async (c) => {
  try {
    const adminUserId = c.req.param('id')
    const { name, role, isActive, permissions } = await c.req.json()
    const admin = getCurrentAdmin(c)

    const updateData: any = {}
    if (name) updateData.name = name
    if (role) {
      updateData.role = role
      // Update permissions based on role if not explicitly provided
      if (!permissions) {
        updateData.permissions = ADMIN_ROLES[role as keyof typeof ADMIN_ROLES]?.permissions || []
      }
    }
    if (typeof isActive === 'boolean') updateData.isActive = isActive
    if (permissions) updateData.permissions = permissions

    await db.update(adminUsers)
      .set(updateData)
      .where(eq(adminUsers.id, adminUserId))

    return c.json({
      message: 'Admin user updated successfully',
      updatedBy: admin?.name
    })

  } catch (error) {
    console.error('Admin user update error:', error)
    return c.json({ error: 'Failed to update admin user' }, 500)
  }
})

// Change admin password
app.put('/change-password', adminAuthMiddleware, zValidator('json', z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8, 'New password must be at least 8 characters'),
  confirmPassword: z.string().min(1, 'Password confirmation is required')
})), async (c) => {
  try {
    const { currentPassword, newPassword, confirmPassword } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin authentication required' }, 401)
    }

    // Validate password confirmation
    if (newPassword !== confirmPassword) {
      return c.json({ error: 'New password and confirmation do not match' }, 400)
    }

    // Get current admin data from database
    const [currentAdmin] = await db.select().from(adminUsers).where(eq(adminUsers.id, admin.id)).limit(1)

    if (!currentAdmin) {
      return c.json({ error: 'Admin user not found' }, 404)
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, currentAdmin.passwordHash)
    if (!isCurrentPasswordValid) {
      return c.json({ error: 'Current password is incorrect' }, 400)
    }

    // Hash new password
    const newPasswordHash = await bcrypt.hash(newPassword, 12)

    // Update password in database
    await db.update(adminUsers)
      .set({ passwordHash: newPasswordHash })
      .where(eq(adminUsers.id, admin.id))

    return c.json({
      message: 'Password changed successfully',
      changedBy: admin.name
    })

  } catch (error) {
    console.error('Admin password change error:', error)
    return c.json({ error: 'Failed to change password' }, 500)
  }
})

// Deactivate admin user (super admin only)
app.put('/admin-users/:id/deactivate', adminAuthMiddleware, requireAdminRole(['super_admin']), async (c) => {
  try {
    const adminUserId = c.req.param('id')
    const admin = getCurrentAdmin(c)

    // Prevent self-deactivation
    if (adminUserId === admin?.id) {
      return c.json({ error: 'Cannot deactivate your own account' }, 400)
    }

    await db.update(adminUsers)
      .set({ isActive: false })
      .where(eq(adminUsers.id, adminUserId))

    return c.json({
      message: 'Admin user deactivated successfully',
      deactivatedBy: admin?.name
    })

  } catch (error) {
    console.error('Admin user deactivation error:', error)
    return c.json({ error: 'Failed to deactivate admin user' }, 500)
  }
})

// Reactivate admin user (super admin only)
app.put('/admin-users/:id/activate', adminAuthMiddleware, requireAdminRole(['super_admin']), async (c) => {
  try {
    const adminUserId = c.req.param('id')
    const admin = getCurrentAdmin(c)

    await db.update(adminUsers)
      .set({ isActive: true })
      .where(eq(adminUsers.id, adminUserId))

    return c.json({
      message: 'Admin user activated successfully',
      activatedBy: admin?.name
    })

  } catch (error) {
    console.error('Admin user activation error:', error)
    return c.json({ error: 'Failed to activate admin user' }, 500)
  }
})

// Reset admin user password (super admin only)
app.put('/admin-users/:id/reset-password', adminAuthMiddleware, requireAdminRole(['super_admin']), zValidator('json', z.object({
  newPassword: z.string().min(8, 'New password must be at least 8 characters')
})), async (c) => {
  try {
    const adminUserId = c.req.param('id')
    const { newPassword } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    // Hash new password
    const passwordHash = await bcrypt.hash(newPassword, 12)

    // Update password in database
    await db.update(adminUsers)
      .set({ passwordHash })
      .where(eq(adminUsers.id, adminUserId))

    return c.json({
      message: 'Admin user password reset successfully',
      resetBy: admin?.name
    })

  } catch (error) {
    console.error('Admin password reset error:', error)
    return c.json({ error: 'Failed to reset admin user password' }, 500)
  }
})

// ===== MONTHLY BILLING AUTOMATION =====

// Update subscription monthly amount
app.put('/subscriptions/:id/amount', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', updateSubscriptionAmountSchema), async (c) => {
  try {
    const subscriptionId = c.req.param('id')
    const { monthlyAmount, effectiveDate } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Check if subscription exists
    const [subscription] = await db.select().from(subscriptions).where(eq(subscriptions.id, subscriptionId)).limit(1)
    if (!subscription) {
      return c.json({ error: 'Subscription not found' }, 404)
    }

    // Update subscription amount
    await db.update(subscriptions)
      .set({
        monthlyAmount: monthlyAmount.toString(),
        updatedAt: new Date()
      })
      .where(eq(subscriptions.id, subscriptionId))

    return c.json({
      message: 'Subscription amount updated successfully',
      subscriptionId,
      newAmount: monthlyAmount,
      updatedBy: admin.name
    })

  } catch (error) {
    console.error('Update subscription amount error:', error)
    return c.json({ error: 'Failed to update subscription amount' }, 500)
  }
})

// Generate billing cycles for all active subscriptions for a specific month
app.post('/billing/generate-monthly', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', bulkGenerateBillingSchema), async (c) => {
  try {
    const { month, year, dueDate } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Get all active subscriptions
    const activeSubscriptions = await db.select({
      id: subscriptions.id,
      clientId: subscriptions.clientId,
      studentCount: subscriptions.studentCount,
      monthlyAmount: subscriptions.monthlyAmount,
      client: {
        schoolName: clients.schoolName,
        email: clients.email
      }
    })
    .from(subscriptions)
    .leftJoin(clients, eq(subscriptions.clientId, clients.id))
    .where(eq(subscriptions.status, 'active'))

    if (activeSubscriptions.length === 0) {
      return c.json({ message: 'No active subscriptions found' })
    }

    // Calculate cycle dates
    const cycleStart = new Date(year, month - 1, 1).toISOString().split('T')[0]
    const cycleEnd = new Date(year, month, 0).toISOString().split('T')[0] // Last day of month
    const defaultDueDate = dueDate || new Date(year, month, 5).toISOString().split('T')[0] // 5th of next month

    const generatedCycles = []
    const generatedInvoices = []

    for (const subscription of activeSubscriptions) {
      // Check if billing cycle already exists for this month
      const [existingCycle] = await db.select()
        .from(billingCycles)
        .where(and(
          eq(billingCycles.subscriptionId, subscription.id),
          eq(billingCycles.cycleStart, cycleStart)
        ))
        .limit(1)

      if (existingCycle) {
        continue // Skip if already exists
      }

      const baseAmount = parseFloat(subscription.monthlyAmount)
      const taxAmount = baseAmount * 0.18 // 18% GST
      const totalAmount = baseAmount + taxAmount

      // Create billing cycle
      const [newCycle] = await db.insert(billingCycles).values({
        subscriptionId: subscription.id,
        cycleStart,
        cycleEnd,
        studentCount: subscription.studentCount,
        baseAmount: baseAmount.toString(),
        discountAmount: '0',
        taxAmount: taxAmount.toString(),
        totalAmount: totalAmount.toString(),
        isProrated: false,
        status: 'pending',
        dueDate: defaultDueDate
      }).returning()

      generatedCycles.push(newCycle)

      // Generate invoice number
      const invoiceNumber = `INV-${year}${month.toString().padStart(2, '0')}-${subscription.clientId!.slice(-6).toUpperCase()}`

      // Create invoice
      const [newInvoice] = await db.insert(invoices).values({
        billingCycleId: newCycle.id,
        clientId: subscription.clientId,
        invoiceNumber,
        amount: baseAmount.toString(),
        taxAmount: taxAmount.toString(),
        totalAmount: totalAmount.toString(),
        status: 'sent',
        issuedDate: new Date().toISOString().split('T')[0],
        dueDate: defaultDueDate
      }).returning()

      generatedInvoices.push({
        ...newInvoice,
        clientName: subscription.client?.schoolName
      })
    }

    return c.json({
      message: `Generated ${generatedCycles.length} billing cycles and ${generatedInvoices.length} invoices for ${month}/${year}`,
      cycles: generatedCycles.length,
      invoices: generatedInvoices.length,
      generatedBy: admin.name,
      details: generatedInvoices
    })

  } catch (error) {
    console.error('Generate monthly billing error:', error)
    return c.json({ error: 'Failed to generate monthly billing' }, 500)
  }
})

// Get client subscription and billing details
app.get('/clients/:id/billing', adminAuthMiddleware, requirePermission('clients:read'), async (c) => {
  try {
    const clientId = c.req.param('id')

    // Get client with subscription details
    const [clientData] = await db.select({
      client: clients,
      subscription: subscriptions
    })
    .from(clients)
    .leftJoin(subscriptions, and(
      eq(subscriptions.clientId, clients.id),
      eq(subscriptions.status, 'active')
    ))
    .where(eq(clients.id, clientId))
    .limit(1)

    if (!clientData) {
      return c.json({ error: 'Client not found' }, 404)
    }

    // Get recent billing cycles
    const recentCycles = clientData.subscription ? await db.select()
      .from(billingCycles)
      .where(eq(billingCycles.subscriptionId, clientData.subscription.id))
      .orderBy(desc(billingCycles.createdAt))
      .limit(6) : []

    // Get recent invoices
    const recentInvoices = await db.select()
      .from(invoices)
      .where(eq(invoices.clientId, clientId))
      .orderBy(desc(invoices.createdAt))
      .limit(10)

    // Get recent payments
    const recentPayments = await db.select()
      .from(payments)
      .where(eq(payments.clientId, clientId))
      .orderBy(desc(payments.createdAt))
      .limit(10)

    // Calculate financial summary
    const totalPaid = recentPayments
      .filter(p => p.status === 'success')
      .reduce((sum, p) => sum + parseFloat(p.amount), 0)

    const totalOutstanding = recentInvoices
      .filter(i => i.status && ['sent', 'overdue'].includes(i.status))
      .reduce((sum, i) => sum + parseFloat(i.totalAmount), 0)

    return c.json({
      client: clientData.client,
      subscription: clientData.subscription,
      billingCycles: recentCycles,
      invoices: recentInvoices,
      payments: recentPayments,
      summary: {
        monthlyAmount: clientData.subscription?.monthlyAmount || '0',
        totalPaid: totalPaid.toString(),
        totalOutstanding: totalOutstanding.toString(),
        lastPaymentDate: recentPayments.find(p => p.status === 'success')?.processedAt || null
      }
    })

  } catch (error) {
    console.error('Get client billing error:', error)
    return c.json({ error: 'Failed to fetch client billing details' }, 500)
  }
})

// Mark invoice as paid and create payment record
app.post('/invoices/:id/mark-paid', adminAuthMiddleware, requirePermission('billing:write'), async (c) => {
  try {
    const invoiceId = c.req.param('id')
    const { paymentMethod, transactionId, notes } = await c.req.json()
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Get invoice details
    const [invoice] = await db.select().from(invoices).where(eq(invoices.id, invoiceId)).limit(1)
    if (!invoice) {
      return c.json({ error: 'Invoice not found' }, 404)
    }

    if (invoice.status === 'paid') {
      return c.json({ error: 'Invoice is already paid' }, 400)
    }

    // Create payment record
    const [payment] = await db.insert(payments).values({
      invoiceId: invoice.id,
      clientId: invoice.clientId,
      amount: invoice.totalAmount,
      currency: 'INR',
      status: 'success',
      paymentMethod: paymentMethod || 'manual',
      razorpayPaymentId: transactionId,
      processedAt: new Date()
    }).returning()

    // Update invoice status
    await db.update(invoices)
      .set({
        status: 'paid',
        paidDate: new Date().toISOString().split('T')[0]
      })
      .where(eq(invoices.id, invoiceId))

    return c.json({
      message: 'Invoice marked as paid successfully',
      payment,
      markedBy: admin.name
    })

  } catch (error) {
    console.error('Mark invoice paid error:', error)
    return c.json({ error: 'Failed to mark invoice as paid' }, 500)
  }
})

// Get billing dashboard analytics
app.get('/billing/dashboard', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    // Get current month stats
    const currentDate = new Date()
    const currentMonth = currentDate.getMonth() + 1
    const currentYear = currentDate.getFullYear()

    // Total active subscriptions
    const [activeSubscriptionsCount] = await db
      .select({ count: count() })
      .from(subscriptions)
      .where(eq(subscriptions.status, 'active'))

    // Monthly revenue (current month)
    const monthlyRevenue = await db
      .select({
        totalAmount: sql<string>`SUM(${payments.amount})`
      })
      .from(payments)
      .where(and(
        eq(payments.status, 'success'),
        sql`EXTRACT(MONTH FROM ${payments.processedAt}) = ${currentMonth}`,
        sql`EXTRACT(YEAR FROM ${payments.processedAt}) = ${currentYear}`
      ))

    // Outstanding invoices
    const outstandingInvoices = await db
      .select({
        count: count(),
        totalAmount: sql<string>`SUM(${invoices.totalAmount})`
      })
      .from(invoices)
      .where(sql`${invoices.status} IN ('sent', 'overdue')`)

    // Overdue invoices
    const overdueInvoices = await db
      .select({
        count: count(),
        totalAmount: sql<string>`SUM(${invoices.totalAmount})`
      })
      .from(invoices)
      .where(and(
        eq(invoices.status, 'overdue'),
        sql`${invoices.dueDate} < CURRENT_DATE`
      ))

    // Recent payments
    const recentPayments = await db
      .select({
        payment: payments,
        client: {
          schoolName: clients.schoolName
        },
        invoice: {
          invoiceNumber: invoices.invoiceNumber
        }
      })
      .from(payments)
      .leftJoin(clients, eq(payments.clientId, clients.id))
      .leftJoin(invoices, eq(payments.invoiceId, invoices.id))
      .where(eq(payments.status, 'success'))
      .orderBy(desc(payments.processedAt))
      .limit(10)

    return c.json({
      activeSubscriptions: activeSubscriptionsCount.count,
      monthlyRevenue: monthlyRevenue[0]?.totalAmount || '0',
      outstanding: {
        count: outstandingInvoices[0]?.count || 0,
        amount: outstandingInvoices[0]?.totalAmount || '0'
      },
      overdue: {
        count: overdueInvoices[0]?.count || 0,
        amount: overdueInvoices[0]?.totalAmount || '0'
      },
      recentPayments
    })

  } catch (error) {
    console.error('Billing dashboard error:', error)
    return c.json({ error: 'Failed to fetch billing dashboard' }, 500)
  }
})

// Get all subscriptions with client details
app.get('/subscriptions', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const { status, search, limit = '50', page = '1' } = c.req.query()
    const limitNum = parseInt(limit)
    const offset = (parseInt(page) - 1) * limitNum

    let query = db
      .select({
        subscription: subscriptions,
        client: {
          id: clients.id,
          schoolName: clients.schoolName,
          email: clients.email,
          phone: clients.phone,
          status: clients.status
        }
      })
      .from(subscriptions)
      .leftJoin(clients, eq(subscriptions.clientId, clients.id))

    // Apply filters
    const conditions = []
    if (status) {
      conditions.push(eq(subscriptions.status, status))
    }
    if (search) {
      conditions.push(
        sql`${clients.schoolName} ILIKE ${`%${search}%`} OR ${clients.email} ILIKE ${`%${search}%`}`
      )
    }

    const subscriptionsList = conditions.length > 0
      ? await query.where(and(...conditions))
          .orderBy(desc(subscriptions.createdAt))
          .limit(limitNum)
          .offset(offset)
      : await query
          .orderBy(desc(subscriptions.createdAt))
          .limit(limitNum)
          .offset(offset)

    // Get total count
    const [totalCount] = await db
      .select({ count: count() })
      .from(subscriptions)
      .leftJoin(clients, eq(subscriptions.clientId, clients.id))

    return c.json({
      subscriptions: subscriptionsList,
      pagination: {
        total: totalCount.count,
        page: parseInt(page),
        limit: limitNum,
        totalPages: Math.ceil(totalCount.count / limitNum)
      }
    })

  } catch (error) {
    console.error('Get subscriptions error:', error)
    return c.json({ error: 'Failed to fetch subscriptions' }, 500)
  }
})

// Update subscription status (suspend/reactivate)
app.put('/subscriptions/:id/status', adminAuthMiddleware, requirePermission('billing:write'), async (c) => {
  try {
    const subscriptionId = c.req.param('id')
    const { status, reason } = await c.req.json()
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    if (!['active', 'suspended', 'cancelled'].includes(status)) {
      return c.json({ error: 'Invalid status' }, 400)
    }

    // Update subscription status
    await db.update(subscriptions)
      .set({
        status,
        updatedAt: new Date()
      })
      .where(eq(subscriptions.id, subscriptionId))

    return c.json({
      message: `Subscription ${status} successfully`,
      reason,
      updatedBy: admin.name
    })

  } catch (error) {
    console.error('Update subscription status error:', error)
    return c.json({ error: 'Failed to update subscription status' }, 500)
  }
})

// ===== PARTNER MANAGEMENT =====

// Validation schemas for partner management
const createPartnerSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  companyName: z.string().optional(),
  phone: z.string().min(10, 'Phone number is required'),
  address: z.string().min(1, 'Address is required'),
  bankAccountNumber: z.string().optional(),
  bankIfscCode: z.string().optional(),
  bankAccountHolderName: z.string().optional(),
  profitSharePercentage: z.number().min(35).max(50).optional()
})

const updatePartnerSchema = z.object({
  name: z.string().min(1).optional(),
  companyName: z.string().optional(),
  phone: z.string().min(10).optional(),
  address: z.string().optional(),
  bankAccountNumber: z.string().optional(),
  bankIfscCode: z.string().optional(),
  bankAccountHolderName: z.string().optional(),
  profitSharePercentage: z.number().min(35).max(50).optional(),
  isActive: z.boolean().optional()
})

const createExpenseSchema = z.object({
  categoryName: z.string().min(1, 'Category name is required'),
  description: z.string().optional(),
  amountPerSchool: z.number().positive().optional(),
  isPercentage: z.boolean().default(false),
  percentageValue: z.number().min(0).max(100).optional(),
  appliesTo: z.enum(['all', 'specific_partners', 'specific_schools']).default('all')
})

// Generate unique partner code
function generatePartnerCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// Generate unique referral code
function generateReferralCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// Create new partner (Admin only)
app.post('/partners', adminAuthMiddleware, requireAdminRole(['super_admin', 'sales']), zValidator('json', createPartnerSchema), async (c) => {
  try {
    const partnerData = c.req.valid('json')
    const currentAdmin = getCurrentAdmin(c)

    // Hash password
    const passwordHash = await bcrypt.hash(partnerData.password, 12)

    // Generate unique partner code
    let partnerCode = generatePartnerCode()
    let codeExists = true
    while (codeExists) {
      const [existing] = await db.select().from(partners).where(eq(partners.partnerCode, partnerCode)).limit(1)
      if (!existing) {
        codeExists = false
      } else {
        partnerCode = generatePartnerCode()
      }
    }

    // Create partner
    const [newPartner] = await db.insert(partners).values({
      partnerCode,
      email: partnerData.email,
      passwordHash,
      name: partnerData.name,
      companyName: partnerData.companyName,
      phone: partnerData.phone,
      address: partnerData.address,
      bankAccountNumber: partnerData.bankAccountNumber,
      bankIfscCode: partnerData.bankIfscCode,
      bankAccountHolderName: partnerData.bankAccountHolderName,
      profitSharePercentage: partnerData.profitSharePercentage ? partnerData.profitSharePercentage.toString() : null,
      createdBy: currentAdmin!.id
    }).returning()

    // Generate referral code
    let referralCode = generateReferralCode()
    let refCodeExists = true
    while (refCodeExists) {
      const [existing] = await db.select().from(referralCodes).where(eq(referralCodes.code, referralCode)).limit(1)
      if (!existing) {
        refCodeExists = false
      } else {
        referralCode = generateReferralCode()
      }
    }

    // Create referral code for partner
    await db.insert(referralCodes).values({
      partnerId: newPartner.id,
      code: referralCode
    })

    return c.json({
      message: 'Partner created successfully',
      partner: {
        id: newPartner.id,
        partnerCode: newPartner.partnerCode,
        name: newPartner.name,
        email: newPartner.email,
        referralCode
      }
    }, 201)

  } catch (error) {
    console.error('Create partner error:', error)
    if (error && typeof error === 'object' && 'code' in error && error.code === '23505') { // Unique constraint violation
      return c.json({ error: 'Email already exists' }, 409)
    }
    return c.json({ error: 'Failed to create partner' }, 500)
  }
})

// Get all partners with filtering and pagination
app.get('/partners', adminAuthMiddleware, requireAdminRole(['super_admin', 'sales', 'support']), async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '10')
    const search = c.req.query('search') || ''
    const status = c.req.query('status') // 'active', 'inactive'
    const sortBy = c.req.query('sortBy') || 'createdAt'
    const sortOrder = c.req.query('sortOrder') || 'desc'

    const offset = (page - 1) * limit

    // Build query conditions
    let whereConditions = []
    if (search) {
      whereConditions.push(
        sql`(${partners.name} ILIKE ${`%${search}%`} OR ${partners.email} ILIKE ${`%${search}%`} OR ${partners.companyName} ILIKE ${`%${search}%`})`
      )
    }
    if (status === 'active') {
      whereConditions.push(eq(partners.isActive, true))
    } else if (status === 'inactive') {
      whereConditions.push(eq(partners.isActive, false))
    }

    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined

    // Get partners with referral codes
    const partnersData = await db
      .select({
        id: partners.id,
        partnerCode: partners.partnerCode,
        name: partners.name,
        email: partners.email,
        companyName: partners.companyName,
        phone: partners.phone,
        profitSharePercentage: partners.profitSharePercentage,
        isActive: partners.isActive,
        emailVerified: partners.emailVerified,
        lastLogin: partners.lastLogin,
        createdAt: partners.createdAt,
        referralCode: referralCodes.code
      })
      .from(partners)
      .leftJoin(referralCodes, and(eq(referralCodes.partnerId, partners.id), eq(referralCodes.isActive, true)))
      .where(whereClause)
      .orderBy(sortOrder === 'desc' ? desc(partners.createdAt) : partners.createdAt)
      .limit(limit)
      .offset(offset)

    // Get total count
    const [totalCount] = await db
      .select({ count: count() })
      .from(partners)
      .where(whereClause)

    return c.json({
      partners: partnersData,
      pagination: {
        page,
        limit,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limit)
      }
    })

  } catch (error) {
    console.error('Get partners error:', error)
    return c.json({ error: 'Failed to fetch partners' }, 500)
  }
})

// Get partner by ID with detailed information
app.get('/partners/:id', adminAuthMiddleware, requireAdminRole(['super_admin', 'sales', 'support']), async (c) => {
  try {
    const partnerId = c.req.param('id')

    const [partner] = await db
      .select({
        id: partners.id,
        partnerCode: partners.partnerCode,
        name: partners.name,
        email: partners.email,
        companyName: partners.companyName,
        phone: partners.phone,
        address: partners.address,
        bankAccountNumber: partners.bankAccountNumber,
        bankIfscCode: partners.bankIfscCode,
        bankAccountHolderName: partners.bankAccountHolderName,
        profitSharePercentage: partners.profitSharePercentage,
        isActive: partners.isActive,
        emailVerified: partners.emailVerified,
        lastLogin: partners.lastLogin,
        createdAt: partners.createdAt,
        referralCode: referralCodes.code
      })
      .from(partners)
      .leftJoin(referralCodes, and(eq(referralCodes.partnerId, partners.id), eq(referralCodes.isActive, true)))
      .where(eq(partners.id, partnerId))
      .limit(1)

    if (!partner) {
      return c.json({ error: 'Partner not found' }, 404)
    }

    // Get partner statistics
    const [totalReferrals] = await db
      .select({ count: count() })
      .from(schoolReferrals)
      .where(eq(schoolReferrals.partnerId, partnerId))

    const [totalEarnings] = await db
      .select({
        total: sql<number>`COALESCE(SUM(${partnerEarnings.partnerEarning}), 0)`,
        available: sql<number>`COALESCE(SUM(CASE WHEN ${partnerEarnings.status} = 'available' THEN ${partnerEarnings.partnerEarning} ELSE 0 END), 0)`
      })
      .from(partnerEarnings)
      .where(eq(partnerEarnings.partnerId, partnerId))

    return c.json({
      partner,
      statistics: {
        totalReferrals: totalReferrals.count,
        totalEarnings: totalEarnings.total || 0,
        availableBalance: totalEarnings.available || 0
      }
    })

  } catch (error) {
    console.error('Get partner error:', error)
    return c.json({ error: 'Failed to fetch partner' }, 500)
  }
})

// Update partner
app.put('/partners/:id', adminAuthMiddleware, requireAdminRole(['super_admin', 'sales']), zValidator('json', updatePartnerSchema), async (c) => {
  try {
    const partnerId = c.req.param('id')
    const updateData = c.req.valid('json')

    // Convert numeric fields to strings for decimal columns
    const processedUpdateData = {
      ...updateData,
      profitSharePercentage: updateData.profitSharePercentage !== undefined
        ? (updateData.profitSharePercentage ? updateData.profitSharePercentage.toString() : null)
        : undefined
    }

    const [updatedPartner] = await db
      .update(partners)
      .set({
        ...processedUpdateData,
        updatedAt: new Date()
      })
      .where(eq(partners.id, partnerId))
      .returning()

    if (!updatedPartner) {
      return c.json({ error: 'Partner not found' }, 404)
    }

    return c.json({
      message: 'Partner updated successfully',
      partner: updatedPartner
    })

  } catch (error) {
    console.error('Update partner error:', error)
    return c.json({ error: 'Failed to update partner' }, 500)
  }
})

// Get schools referred by a partner
app.get('/partners/:id/schools', adminAuthMiddleware, requireAdminRole(['super_admin', 'sales', 'support']), async (c) => {
  try {
    const partnerId = c.req.param('id')
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '10')
    const offset = (page - 1) * limit

    const schools = await db
      .select({
        id: clients.id,
        schoolName: clients.schoolName,
        schoolCode: clients.schoolCode,
        email: clients.email,
        phone: clients.phone,
        actualStudentCount: clients.actualStudentCount,
        status: clients.status,
        onboardingStatus: clients.onboardingStatus,
        referredAt: schoolReferrals.referredAt,
        referralCode: referralCodes.code
      })
      .from(schoolReferrals)
      .innerJoin(clients, eq(clients.id, schoolReferrals.clientId))
      .innerJoin(referralCodes, eq(referralCodes.id, schoolReferrals.referralCodeId))
      .where(eq(schoolReferrals.partnerId, partnerId))
      .orderBy(desc(schoolReferrals.referredAt))
      .limit(limit)
      .offset(offset)

    const [totalCount] = await db
      .select({ count: count() })
      .from(schoolReferrals)
      .where(eq(schoolReferrals.partnerId, partnerId))

    return c.json({
      schools,
      pagination: {
        page,
        limit,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limit)
      }
    })

  } catch (error) {
    console.error('Get partner schools error:', error)
    return c.json({ error: 'Failed to fetch partner schools' }, 500)
  }
})

// Create operational expense
app.post('/expenses', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), zValidator('json', createExpenseSchema), async (c) => {
  try {
    const expenseData = c.req.valid('json')
    const currentAdmin = getCurrentAdmin(c)

    // Convert numeric fields to strings for decimal columns
    const processedExpenseData = {
      ...expenseData,
      amountPerSchool: expenseData.amountPerSchool ? expenseData.amountPerSchool.toString() : null,
      percentageValue: expenseData.percentageValue ? expenseData.percentageValue.toString() : null
    }

    const [newExpense] = await db.insert(operationalExpenses).values({
      ...processedExpenseData,
      createdBy: currentAdmin!.id
    }).returning()

    return c.json({
      message: 'Operational expense created successfully',
      expense: newExpense
    }, 201)

  } catch (error) {
    console.error('Create expense error:', error)
    return c.json({ error: 'Failed to create expense' }, 500)
  }
})

// Get all operational expenses
app.get('/expenses', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing', 'support']), async (c) => {
  try {
    const expenses = await db
      .select({
        id: operationalExpenses.id,
        categoryName: operationalExpenses.categoryName,
        description: operationalExpenses.description,
        amountPerSchool: operationalExpenses.amountPerSchool,
        isPercentage: operationalExpenses.isPercentage,
        percentageValue: operationalExpenses.percentageValue,
        appliesTo: operationalExpenses.appliesTo,
        isActive: operationalExpenses.isActive,
        createdAt: operationalExpenses.createdAt
      })
      .from(operationalExpenses)
      .where(eq(operationalExpenses.isActive, true))
      .orderBy(desc(operationalExpenses.createdAt))

    return c.json({ expenses })

  } catch (error) {
    console.error('Get expenses error:', error)
    return c.json({ error: 'Failed to fetch expenses' }, 500)
  }
})

// ===== WITHDRAWAL MANAGEMENT =====

const processWithdrawalSchema = z.object({
  status: z.enum(['approved', 'rejected']),
  transactionReference: z.string().optional(),
  rejectionReason: z.string().optional(),
  processingFee: z.number().min(0).optional()
})

// Get all withdrawal requests
app.get('/withdrawals', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '10')
    const status = c.req.query('status') // 'pending', 'approved', 'processed', 'rejected'
    const offset = (page - 1) * limit

    let whereConditions = []
    if (status) {
      whereConditions.push(eq(withdrawalRequests.status, status))
    }

    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined

    const withdrawals = await db
      .select({
        id: withdrawalRequests.id,
        partnerId: withdrawalRequests.partnerId,
        partnerName: partners.name,
        partnerEmail: partners.email,
        requestedAmount: withdrawalRequests.requestedAmount,
        availableBalance: withdrawalRequests.availableBalance,
        status: withdrawalRequests.status,
        requestMonth: withdrawalRequests.requestMonth,
        requestedAt: withdrawalRequests.requestedAt,
        reviewedAt: withdrawalRequests.reviewedAt,
        processedAt: withdrawalRequests.processedAt,
        transactionReference: withdrawalRequests.transactionReference,
        rejectionReason: withdrawalRequests.rejectionReason,
        processingFee: withdrawalRequests.processingFee,
        netAmount: withdrawalRequests.netAmount
      })
      .from(withdrawalRequests)
      .innerJoin(partners, eq(partners.id, withdrawalRequests.partnerId))
      .where(whereClause)
      .orderBy(desc(withdrawalRequests.requestedAt))
      .limit(limit)
      .offset(offset)

    const [totalCount] = await db
      .select({ count: count() })
      .from(withdrawalRequests)
      .where(whereClause)

    return c.json({
      withdrawals,
      pagination: {
        page,
        limit,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limit)
      }
    })

  } catch (error) {
    console.error('Get withdrawals error:', error)
    return c.json({ error: 'Failed to fetch withdrawals' }, 500)
  }
})

// Process withdrawal request (approve/reject)
app.put('/withdrawals/:id/process', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), zValidator('json', processWithdrawalSchema), async (c) => {
  try {
    const withdrawalId = c.req.param('id')
    const { status, transactionReference, rejectionReason, processingFee } = c.req.valid('json')
    const currentAdmin = getCurrentAdmin(c)

    // Get withdrawal request
    const [withdrawal] = await db
      .select()
      .from(withdrawalRequests)
      .where(eq(withdrawalRequests.id, withdrawalId))
      .limit(1)

    if (!withdrawal) {
      return c.json({ error: 'Withdrawal request not found' }, 404)
    }

    if (withdrawal.status !== 'pending') {
      return c.json({ error: 'Withdrawal request already processed' }, 400)
    }

    const updateData: any = {
      status,
      reviewedBy: currentAdmin!.id,
      reviewedAt: new Date()
    }

    if (status === 'approved') {
      if (!transactionReference) {
        return c.json({ error: 'Transaction reference is required for approval' }, 400)
      }
      updateData.transactionReference = transactionReference
      updateData.processingFee = (processingFee || 0).toString()
      updateData.netAmount = (parseFloat(withdrawal.requestedAmount) - (processingFee || 0)).toString()
      updateData.processedAt = new Date()
      updateData.processedBy = currentAdmin!.id
      updateData.status = 'processed'
    } else if (status === 'rejected') {
      if (!rejectionReason) {
        return c.json({ error: 'Rejection reason is required' }, 400)
      }
      updateData.rejectionReason = rejectionReason
    }

    // Update withdrawal request
    const [updatedWithdrawal] = await db
      .update(withdrawalRequests)
      .set(updateData)
      .where(eq(withdrawalRequests.id, withdrawalId))
      .returning()

    // If approved, create transaction record
    if (status === 'approved') {
      // Get current partner balance
      const [balanceResult] = await db
        .select({
          balance: sql<number>`COALESCE(SUM(CASE
            WHEN ${partnerTransactions.transactionType} IN ('EARNING', 'BONUS') THEN ${partnerTransactions.amount}
            WHEN ${partnerTransactions.transactionType} IN ('WITHDRAWAL', 'PENALTY') THEN -${partnerTransactions.amount}
            ELSE 0
          END), 0)`
        })
        .from(partnerTransactions)
        .where(eq(partnerTransactions.partnerId, withdrawal.partnerId))

      const currentBalance = balanceResult?.balance || 0
      const newBalance = currentBalance - parseFloat(withdrawal.requestedAmount)

      // Create withdrawal transaction
      await db.insert(partnerTransactions).values({
        partnerId: withdrawal.partnerId!,
        transactionType: 'WITHDRAWAL',
        amount: withdrawal.requestedAmount,
        description: `Withdrawal processed - ${transactionReference}`,
        referenceId: withdrawalId,
        referenceType: 'withdrawal_request',
        balanceBefore: currentBalance.toString(),
        balanceAfter: newBalance.toString(),
        createdBy: currentAdmin!.id
      })
    }

    return c.json({
      message: `Withdrawal request ${status} successfully`,
      withdrawal: updatedWithdrawal
    })

  } catch (error) {
    console.error('Process withdrawal error:', error)
    return c.json({ error: 'Failed to process withdrawal request' }, 500)
  }
})

// Get partner earnings and transactions
app.get('/partners/:id/earnings', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing', 'support']), async (c) => {
  try {
    const partnerId = c.req.param('id')
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '10')
    const offset = (page - 1) * limit

    // Get earnings
    const earnings = await db
      .select({
        id: partnerEarnings.id,
        clientId: partnerEarnings.clientId,
        schoolName: clients.schoolName,
        grossAmount: partnerEarnings.grossAmount,
        totalExpenses: partnerEarnings.totalExpenses,
        netProfit: partnerEarnings.netProfit,
        partnerSharePercentage: partnerEarnings.partnerSharePercentage,
        partnerEarning: partnerEarnings.partnerEarning,
        status: partnerEarnings.status,
        calculatedAt: partnerEarnings.calculatedAt,
        availableAt: partnerEarnings.availableAt
      })
      .from(partnerEarnings)
      .innerJoin(clients, eq(clients.id, partnerEarnings.clientId))
      .where(eq(partnerEarnings.partnerId, partnerId))
      .orderBy(desc(partnerEarnings.calculatedAt))
      .limit(limit)
      .offset(offset)

    // Get transactions
    const transactions = await db
      .select()
      .from(partnerTransactions)
      .where(eq(partnerTransactions.partnerId, partnerId))
      .orderBy(desc(partnerTransactions.createdAt))
      .limit(10)

    // Get summary
    const [summary] = await db
      .select({
        totalEarnings: sql<number>`COALESCE(SUM(${partnerEarnings.partnerEarning}), 0)`,
        availableBalance: sql<number>`COALESCE(SUM(CASE WHEN ${partnerEarnings.status} = 'available' THEN ${partnerEarnings.partnerEarning} ELSE 0 END), 0)`,
        pendingEarnings: sql<number>`COALESCE(SUM(CASE WHEN ${partnerEarnings.status} = 'pending' THEN ${partnerEarnings.partnerEarning} ELSE 0 END), 0)`
      })
      .from(partnerEarnings)
      .where(eq(partnerEarnings.partnerId, partnerId))

    const [totalCount] = await db
      .select({ count: count() })
      .from(partnerEarnings)
      .where(eq(partnerEarnings.partnerId, partnerId))

    return c.json({
      earnings,
      transactions,
      summary,
      pagination: {
        page,
        limit,
        total: totalCount.count,
        totalPages: Math.ceil(totalCount.count / limit)
      }
    })

  } catch (error) {
    console.error('Get partner earnings error:', error)
    return c.json({ error: 'Failed to fetch partner earnings' }, 500)
  }
})

// ===== FINANCIAL MANAGEMENT & CALCULATIONS =====

// Calculate partner earnings for a specific payment
app.post('/calculate-earnings/:paymentId', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), async (c) => {
  try {
    const paymentId = c.req.param('paymentId')
    const currentAdmin = getCurrentAdmin(c)

    // Get payment details with client and partner information
    const paymentData = await db
      .select({
        paymentId: payments.id,
        amount: payments.amount,
        clientId: clients.id,
        schoolName: clients.schoolName,
        partnerId: schoolReferrals.partnerId,
        partnerSharePercentage: partners.profitSharePercentage,
        invoiceId: payments.invoiceId
      })
      .from(payments)
      .innerJoin(clients, eq(clients.id, payments.clientId))
      .leftJoin(schoolReferrals, eq(schoolReferrals.clientId, clients.id))
      .leftJoin(partners, eq(partners.id, schoolReferrals.partnerId))
      .where(eq(payments.id, paymentId))
      .limit(1)

    const [payment] = paymentData

    if (!payment) {
      return c.json({ error: 'Payment not found' }, 404)
    }

    if (!payment.partnerId) {
      return c.json({ error: 'No partner associated with this payment' }, 400)
    }

    // Check if earnings already calculated
    const [existingEarning] = await db
      .select()
      .from(partnerEarnings)
      .where(and(
        eq(partnerEarnings.paymentId, paymentId),
        eq(partnerEarnings.partnerId, payment.partnerId)
      ))
      .limit(1)

    if (existingEarning) {
      return c.json({ error: 'Earnings already calculated for this payment' }, 400)
    }

    // Get operational expenses
    const expenses = await db
      .select()
      .from(operationalExpenses)
      .where(eq(operationalExpenses.isActive, true))

    // Calculate total expenses
    let totalExpenses = 0
    const expenseBreakdown = []

    for (const expense of expenses) {
      let expenseAmount = 0

      if (expense.isPercentage && expense.percentageValue) {
        expenseAmount = (parseFloat(payment.amount) * parseFloat(expense.percentageValue)) / 100
      } else if (expense.amountPerSchool) {
        expenseAmount = parseFloat(expense.amountPerSchool)
      }

      if (expenseAmount > 0) {
        totalExpenses += expenseAmount
        expenseBreakdown.push({
          category: expense.categoryName,
          amount: expenseAmount,
          type: expense.isPercentage ? 'percentage' : 'fixed'
        })
      }
    }

    // Calculate net profit and partner earning
    const grossAmount = parseFloat(payment.amount)
    const netProfit = grossAmount - totalExpenses
    const partnerSharePercentage = payment.partnerSharePercentage ? parseFloat(payment.partnerSharePercentage) : 40 // Default 40%
    const partnerEarning = (netProfit * partnerSharePercentage) / 100

    // Create partner earning record
    const [newEarning] = await db.insert(partnerEarnings).values({
      partnerId: payment.partnerId!,
      clientId: payment.clientId!,
      invoiceId: payment.invoiceId!,
      paymentId: payment.paymentId || null,
      grossAmount: grossAmount.toString(),
      totalExpenses: totalExpenses.toString(),
      netProfit: netProfit.toString(),
      partnerSharePercentage: partnerSharePercentage.toString(),
      partnerEarning: partnerEarning.toString(),
      status: 'available', // Available immediately after payment confirmation
      calculatedAt: new Date(),
      availableAt: new Date(),
      calculatedBy: currentAdmin!.id,
      expenseBreakdown: expenseBreakdown
    }).returning()

    // Create transaction record
    const [balanceResult] = await db
      .select({
        balance: sql<number>`COALESCE(SUM(CASE
          WHEN ${partnerTransactions.transactionType} IN ('EARNING', 'BONUS') THEN ${partnerTransactions.amount}
          WHEN ${partnerTransactions.transactionType} IN ('WITHDRAWAL', 'PENALTY') THEN -${partnerTransactions.amount}
          ELSE 0
        END), 0)`
      })
      .from(partnerTransactions)
      .where(eq(partnerTransactions.partnerId, payment.partnerId!))

    const currentBalance = balanceResult?.balance || 0
    const newBalance = currentBalance + partnerEarning

    await db.insert(partnerTransactions).values({
      partnerId: payment.partnerId!,
      transactionType: 'EARNING',
      amount: partnerEarning.toString(),
      description: `Earning from ${payment.schoolName} - Payment ${paymentId}`,
      referenceId: newEarning.id,
      referenceType: 'partner_earning',
      balanceBefore: currentBalance.toString(),
      balanceAfter: newBalance.toString(),
      createdBy: currentAdmin!.id
    })

    return c.json({
      message: 'Partner earnings calculated successfully',
      earning: {
        id: newEarning.id,
        grossAmount,
        totalExpenses,
        netProfit,
        partnerSharePercentage,
        partnerEarning,
        expenseBreakdown
      }
    })

  } catch (error) {
    console.error('Calculate earnings error:', error)
    return c.json({ error: 'Failed to calculate partner earnings' }, 500)
  }
})

// Bulk calculate earnings for all pending payments
app.post('/calculate-all-earnings', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), async (c) => {
  try {
    const currentAdmin = getCurrentAdmin(c)

    // Get all successful payments that don't have earnings calculated
    const pendingPayments = await db
      .select({
        paymentId: payments.id,
        amount: payments.amount,
        clientId: clients.id,
        schoolName: clients.schoolName,
        partnerId: schoolReferrals.partnerId,
        partnerSharePercentage: partners.profitSharePercentage,
        invoiceId: payments.invoiceId
      })
      .from(payments)
      .innerJoin(clients, eq(clients.id, payments.clientId))
      .leftJoin(schoolReferrals, eq(schoolReferrals.clientId, clients.id))
      .leftJoin(partners, eq(partners.id, schoolReferrals.partnerId))
      .leftJoin(partnerEarnings, and(
        eq(partnerEarnings.paymentId, payments.id),
        eq(partnerEarnings.partnerId, schoolReferrals.partnerId)
      ))
      .where(and(
        eq(payments.status, 'success'),
        sql`${schoolReferrals.partnerId} IS NOT NULL`,
        sql`${partnerEarnings.id} IS NULL`
      ))

    if (pendingPayments.length === 0) {
      return c.json({ message: 'No pending payments to calculate earnings for' })
    }

    // Get operational expenses once
    const expenses = await db
      .select()
      .from(operationalExpenses)
      .where(eq(operationalExpenses.isActive, true))

    let calculatedCount = 0
    const results = []

    for (const payment of pendingPayments) {
      try {
        // Calculate total expenses
        let totalExpenses = 0
        const expenseBreakdown = []

        for (const expense of expenses) {
          let expenseAmount = 0

          if (expense.isPercentage && expense.percentageValue) {
            expenseAmount = (parseFloat(payment.amount) * parseFloat(expense.percentageValue)) / 100
          } else if (expense.amountPerSchool) {
            expenseAmount = parseFloat(expense.amountPerSchool)
          }

          if (expenseAmount > 0) {
            totalExpenses += expenseAmount
            expenseBreakdown.push({
              category: expense.categoryName,
              amount: expenseAmount,
              type: expense.isPercentage ? 'percentage' : 'fixed'
            })
          }
        }

        // Calculate earnings
        const grossAmount = parseFloat(payment.amount)
        const netProfit = grossAmount - totalExpenses
        const partnerSharePercentage = payment.partnerSharePercentage ? parseFloat(payment.partnerSharePercentage) : 40
        const partnerEarning = (netProfit * partnerSharePercentage) / 100

        // Create earning record
        const [newEarning] = await db.insert(partnerEarnings).values({
          partnerId: payment.partnerId!,
          clientId: payment.clientId!,
          invoiceId: payment.invoiceId!,
          paymentId: payment.paymentId || null,
          grossAmount: grossAmount.toString(),
          totalExpenses: totalExpenses.toString(),
          netProfit: netProfit.toString(),
          partnerSharePercentage: partnerSharePercentage.toString(),
          partnerEarning: partnerEarning.toString(),
          status: 'available',
          calculatedAt: new Date(),
          availableAt: new Date(),
          calculatedBy: currentAdmin!.id,
          expenseBreakdown: expenseBreakdown
        }).returning()

        // Create transaction record
        const [balanceResult] = await db
          .select({
            balance: sql<number>`COALESCE(SUM(CASE
              WHEN ${partnerTransactions.transactionType} IN ('EARNING', 'BONUS') THEN ${partnerTransactions.amount}
              WHEN ${partnerTransactions.transactionType} IN ('WITHDRAWAL', 'PENALTY') THEN -${partnerTransactions.amount}
              ELSE 0
            END), 0)`
          })
          .from(partnerTransactions)
          .where(eq(partnerTransactions.partnerId, payment.partnerId!))

        const currentBalance = balanceResult?.balance || 0
        const newBalance = currentBalance + partnerEarning

        await db.insert(partnerTransactions).values({
          partnerId: payment.partnerId!,
          transactionType: 'EARNING',
          amount: partnerEarning.toString(),
          description: `Earning from ${payment.schoolName} - Payment ${payment.paymentId}`,
          referenceId: newEarning.id,
          referenceType: 'partner_earning',
          balanceBefore: currentBalance.toString(),
          balanceAfter: newBalance.toString(),
          createdBy: currentAdmin!.id
        })

        calculatedCount++
        results.push({
          paymentId: payment.paymentId,
          schoolName: payment.schoolName,
          partnerEarning,
          status: 'success'
        })

      } catch (error) {
        console.error(`Error calculating earnings for payment ${payment.paymentId}:`, error)
        results.push({
          paymentId: payment.paymentId,
          schoolName: payment.schoolName,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return c.json({
      message: `Calculated earnings for ${calculatedCount} out of ${pendingPayments.length} payments`,
      calculatedCount,
      totalPayments: pendingPayments.length,
      results
    })

  } catch (error) {
    console.error('Bulk calculate earnings error:', error)
    return c.json({ error: 'Failed to calculate bulk earnings' }, 500)
  }
})

// Update operational expense
app.put('/expenses/:id', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), async (c) => {
  try {
    const expenseId = c.req.param('id')
    const updateData = c.req.json()

    const [updatedExpense] = await db
      .update(operationalExpenses)
      .set({
        ...updateData,
        updatedAt: new Date()
      })
      .where(eq(operationalExpenses.id, expenseId))
      .returning()

    if (!updatedExpense) {
      return c.json({ error: 'Expense not found' }, 404)
    }

    return c.json({
      message: 'Expense updated successfully',
      expense: updatedExpense
    })

  } catch (error) {
    console.error('Update expense error:', error)
    return c.json({ error: 'Failed to update expense' }, 500)
  }
})

// ===== SYSTEM INFORMATION =====

// Get system information and statistics
app.get('/system-info', adminAuthMiddleware, requireAdminRole(['super_admin']), async (c) => {
  try {
    // Get various system statistics
    const [totalLeads] = await db.select({ count: count() }).from(leads)
    const [totalClients] = await db.select({ count: count() }).from(clients)
    const [totalAdminUsers] = await db.select({ count: count() }).from(adminUsers)
    const [totalClientUsers] = await db.select({ count: count() }).from(clientUsers)
    const [pendingRequests] = await db.select({ count: count() }).from(softwareRequests).where(eq(softwareRequests.status, 'pending'))
    const [activeClients] = await db.select({ count: count() }).from(clients).where(eq(clients.onboardingStatus, 'completed'))

    // Partner system statistics
    const [totalPartners] = await db.select({ count: count() }).from(partners)
    const [activePartners] = await db.select({ count: count() }).from(partners).where(eq(partners.isActive, true))
    const [totalReferrals] = await db.select({ count: count() }).from(schoolReferrals)
    const [pendingWithdrawals] = await db.select({ count: count() }).from(withdrawalRequests).where(eq(withdrawalRequests.status, 'pending'))

    const [partnerEarningsStats] = await db
      .select({
        totalEarnings: sql<number>`COALESCE(SUM(${partnerEarnings.partnerEarning}), 0)`,
        availableBalance: sql<number>`COALESCE(SUM(CASE WHEN ${partnerEarnings.status} = 'available' THEN ${partnerEarnings.partnerEarning} ELSE 0 END), 0)`
      })
      .from(partnerEarnings)

    return c.json({
      systemStats: {
        totalLeads: totalLeads.count,
        totalClients: totalClients.count,
        activeClients: activeClients.count,
        totalAdminUsers: totalAdminUsers.count,
        totalClientUsers: totalClientUsers.count,
        pendingRequests: pendingRequests.count
      },
      partnerStats: {
        totalPartners: totalPartners.count,
        activePartners: activePartners.count,
        totalReferrals: totalReferrals.count,
        pendingWithdrawals: pendingWithdrawals.count,
        totalEarnings: partnerEarningsStats?.totalEarnings || 0,
        availableBalance: partnerEarningsStats?.availableBalance || 0
      },
      serverInfo: {
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development',
        version: '1.0.0'
      }
    })

  } catch (error) {
    console.error('System info error:', error)
    return c.json({ error: 'Failed to fetch system information' }, 500)
  }
})

// ===== DUE DATE MANAGEMENT =====

// Process overdue billing cycles and update statuses
app.post('/billing/process-due-dates', adminAuthMiddleware, requirePermission('billing:write'), async (c) => {
  try {
    const admin = getCurrentAdmin(c)
    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    const result = await DueDateManager.processAllDueDateUpdates()

    return c.json({
      message: 'Due date processing completed successfully',
      result,
      processedBy: admin.name,
      processedAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Process due dates error:', error)
    return c.json({ error: 'Failed to process due dates' }, 500)
  }
})

// Get due date information for a specific subscription
app.get('/subscriptions/:id/due-date-info', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const subscriptionId = c.req.param('id')

    const dueDateInfo = await DueDateManager.getSubscriptionDueDateInfo(subscriptionId)

    if (!dueDateInfo) {
      return c.json({ error: 'Subscription not found' }, 404)
    }

    return c.json({
      subscriptionId,
      dueDateInfo
    })

  } catch (error) {
    console.error('Get due date info error:', error)
    return c.json({ error: 'Failed to get due date information' }, 500)
  }
})

// Update subscription due date settings
app.put('/subscriptions/:id/due-date-settings', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', z.object({
  dueDate: z.number().min(1).max(31),
  gracePeriodDays: z.number().min(0).max(30)
})), async (c) => {
  try {
    const subscriptionId = c.req.param('id')
    const { dueDate, gracePeriodDays } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Update subscription due date settings
    const [updatedSubscription] = await db.update(subscriptions)
      .set({
        dueDate,
        gracePeriodDays,
        updatedAt: new Date()
      })
      .where(eq(subscriptions.id, subscriptionId))
      .returning()

    if (!updatedSubscription) {
      return c.json({ error: 'Subscription not found' }, 404)
    }

    return c.json({
      message: 'Due date settings updated successfully',
      subscription: updatedSubscription,
      updatedBy: admin.name
    })

  } catch (error) {
    console.error('Update due date settings error:', error)
    return c.json({ error: 'Failed to update due date settings' }, 500)
  }
})

// Get overdue billing cycles summary
app.get('/billing/overdue-summary', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    // Get overdue billing cycles with client information
    const overdueCycles = await db.select({
      id: billingCycles.id,
      subscriptionId: billingCycles.subscriptionId,
      dueDate: billingCycles.dueDate,
      totalAmount: billingCycles.totalAmount,
      penaltyAmount: billingCycles.penaltyAmount,
      status: billingCycles.status,
      gracePeriodDays: billingCycles.gracePeriodDays,
      // Client information
      clientId: subscriptions.clientId,
      schoolName: clients.schoolName,
      email: clients.email
    })
    .from(billingCycles)
    .leftJoin(subscriptions, eq(billingCycles.subscriptionId, subscriptions.id))
    .leftJoin(clients, eq(subscriptions.clientId, clients.id))
    .where(and(
      inArray(billingCycles.status, ['overdue', 'suspended']),
      lte(billingCycles.dueDate, today.toISOString().split('T')[0])
    ))
    .orderBy(desc(billingCycles.dueDate))

    // Calculate summary statistics
    const totalOverdueAmount = overdueCycles.reduce((sum, cycle) => sum + parseFloat(cycle.totalAmount), 0)
    const totalPenaltyAmount = overdueCycles.reduce((sum, cycle) => sum + parseFloat(cycle.penaltyAmount || '0'), 0)
    const overdueCount = overdueCycles.filter(c => c.status === 'overdue').length
    const suspendedCount = overdueCycles.filter(c => c.status === 'suspended').length

    return c.json({
      summary: {
        totalOverdueAmount,
        totalPenaltyAmount,
        overdueCount,
        suspendedCount,
        totalCycles: overdueCycles.length
      },
      overdueCycles
    })

  } catch (error) {
    console.error('Get overdue summary error:', error)
    return c.json({ error: 'Failed to get overdue summary' }, 500)
  }
})

// ===== SUBSCRIPTION STATUS MANAGEMENT =====

// Get subscription status information
app.get('/subscriptions/:id/status-info', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const subscriptionId = c.req.param('id')

    const statusInfo = await SubscriptionStatusManager.getSubscriptionStatusInfo(subscriptionId)

    if (!statusInfo) {
      return c.json({ error: 'Subscription not found' }, 404)
    }

    return c.json({
      subscriptionId,
      statusInfo
    })

  } catch (error) {
    console.error('Get subscription status info error:', error)
    return c.json({ error: 'Failed to get subscription status information' }, 500)
  }
})

// Suspend subscription
app.post('/subscriptions/:id/suspend', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', z.object({
  reason: z.string().min(1, 'Reason is required')
})), async (c) => {
  try {
    const subscriptionId = c.req.param('id')
    const { reason } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    const result = await SubscriptionStatusManager.suspendSubscription(
      subscriptionId,
      reason,
      admin.id
    )

    if (!result.success) {
      return c.json({ error: result.error }, 400)
    }

    return c.json({
      message: 'Subscription suspended successfully',
      subscriptionId,
      reason,
      suspendedBy: admin.name,
      suspendedAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Suspend subscription error:', error)
    return c.json({ error: 'Failed to suspend subscription' }, 500)
  }
})

// Reactivate subscription
app.post('/subscriptions/:id/reactivate', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', z.object({
  reason: z.string().min(1, 'Reason is required')
})), async (c) => {
  try {
    const subscriptionId = c.req.param('id')
    const { reason } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    const result = await SubscriptionStatusManager.reactivateSubscription(
      subscriptionId,
      reason,
      admin.id
    )

    if (!result.success) {
      return c.json({ error: result.error }, 400)
    }

    return c.json({
      message: 'Subscription reactivated successfully',
      subscriptionId,
      reason,
      reactivatedBy: admin.name,
      reactivatedAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Reactivate subscription error:', error)
    return c.json({ error: 'Failed to reactivate subscription' }, 500)
  }
})

// Cancel subscription
app.post('/subscriptions/:id/cancel', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', z.object({
  reason: z.string().min(1, 'Reason is required'),
  effectiveDate: z.string().optional()
})), async (c) => {
  try {
    const subscriptionId = c.req.param('id')
    const { reason, effectiveDate } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    const effectiveDateObj = effectiveDate ? new Date(effectiveDate) : undefined
    const result = await SubscriptionStatusManager.cancelSubscription(
      subscriptionId,
      reason,
      admin.id,
      effectiveDateObj
    )

    if (!result.success) {
      return c.json({ error: result.error }, 400)
    }

    return c.json({
      message: 'Subscription cancelled successfully',
      subscriptionId,
      reason,
      effectiveDate: effectiveDateObj?.toISOString(),
      cancelledBy: admin.name,
      cancelledAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Cancel subscription error:', error)
    return c.json({ error: 'Failed to cancel subscription' }, 500)
  }
})

// Get subscription renewal information
app.get('/subscriptions/:id/renewal-info', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const subscriptionId = c.req.param('id')

    const renewalInfo = await SubscriptionStatusManager.getRenewalInfo(subscriptionId)

    if (!renewalInfo) {
      return c.json({ error: 'Subscription not found' }, 404)
    }

    return c.json({
      subscriptionId,
      renewalInfo
    })

  } catch (error) {
    console.error('Get renewal info error:', error)
    return c.json({ error: 'Failed to get renewal information' }, 500)
  }
})

// Transition subscription status (general endpoint)
app.post('/subscriptions/:id/transition-status', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', z.object({
  newStatus: z.enum(['active', 'suspended', 'cancelled', 'expired', 'pending', 'overdue']),
  reason: z.string().min(1, 'Reason is required'),
  metadata: z.record(z.any()).optional()
})), async (c) => {
  try {
    const subscriptionId = c.req.param('id')
    const { newStatus, reason, metadata } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    const result = await SubscriptionStatusManager.transitionStatus(
      subscriptionId,
      newStatus,
      reason,
      admin.id,
      metadata
    )

    if (!result.success) {
      return c.json({ error: result.error }, 400)
    }

    return c.json({
      message: 'Subscription status updated successfully',
      transition: result.transition,
      updatedBy: admin.name
    })

  } catch (error) {
    console.error('Transition subscription status error:', error)
    return c.json({ error: 'Failed to transition subscription status' }, 500)
  }
})

// ===== BILLING SYSTEM MONITORING =====

// Get billing system health status
app.get('/billing/system-health', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const healthStatus = await billingScheduler.getSystemHealth()

    return c.json({
      message: 'Billing system health retrieved successfully',
      health: healthStatus,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Get system health error:', error)
    return c.json({ error: 'Failed to get system health status' }, 500)
  }
})

// Get billing scheduler task status
app.get('/billing/scheduler-status', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const taskStatus = billingScheduler.getStatus()

    return c.json({
      message: 'Scheduler status retrieved successfully',
      tasks: taskStatus,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Get scheduler status error:', error)
    return c.json({ error: 'Failed to get scheduler status' }, 500)
  }
})

// Manually trigger billing generation
app.post('/billing/trigger-manual-billing', adminAuthMiddleware, requirePermission('billing:write'), async (c) => {
  try {
    const admin = getCurrentAdmin(c)
    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    console.log(`🔄 Manual billing triggered by admin: ${admin.name} (${admin.email})`)

    const result = await billingScheduler.triggerManualBilling(admin.id)

    return c.json({
      message: 'Manual billing completed successfully',
      result,
      triggeredBy: admin.name,
      triggeredAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Manual billing trigger error:', error)
    return c.json({ error: 'Failed to trigger manual billing' }, 500)
  }
})

// Manually trigger overdue check
app.post('/billing/trigger-overdue-check', adminAuthMiddleware, requirePermission('billing:write'), async (c) => {
  try {
    const admin = getCurrentAdmin(c)
    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    console.log(`🔄 Manual overdue check triggered by admin: ${admin.name} (${admin.email})`)

    const result = await billingScheduler.checkOverdueInvoices()

    return c.json({
      message: 'Overdue check completed successfully',
      result,
      triggeredBy: admin.name,
      triggeredAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Manual overdue check error:', error)
    return c.json({ error: 'Failed to trigger overdue check' }, 500)
  }
})

// Manually trigger payment reminders
app.post('/billing/trigger-payment-reminders', adminAuthMiddleware, requirePermission('billing:write'), async (c) => {
  try {
    const admin = getCurrentAdmin(c)
    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    console.log(`🔄 Manual payment reminders triggered by admin: ${admin.name} (${admin.email})`)

    const result = await billingScheduler.sendPaymentReminders()

    return c.json({
      message: 'Payment reminders completed successfully',
      result,
      triggeredBy: admin.name,
      triggeredAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Manual payment reminders error:', error)
    return c.json({ error: 'Failed to trigger payment reminders' }, 500)
  }
})

// Get billing analytics and metrics
app.get('/billing/analytics', adminAuthMiddleware, requirePermission('billing:read'), async (c) => {
  try {
    const { period = '30' } = c.req.query()
    const days = parseInt(period)
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    // Get billing metrics
    const [totalRevenue] = await db.select({
      total: sql<number>`COALESCE(SUM(CAST(${invoices.totalAmount} AS DECIMAL)), 0)`
    })
    .from(invoices)
    .where(and(
      eq(invoices.status, 'paid'),
      gte(invoices.paidDate, startDate.toISOString().split('T')[0])
    ))

    const [pendingRevenue] = await db.select({
      total: sql<number>`COALESCE(SUM(CAST(${invoices.totalAmount} AS DECIMAL)), 0)`
    })
    .from(invoices)
    .where(eq(invoices.status, 'sent'))

    const [overdueRevenue] = await db.select({
      total: sql<number>`COALESCE(SUM(CAST(${invoices.totalAmount} AS DECIMAL)), 0)`
    })
    .from(invoices)
    .where(eq(invoices.status, 'overdue'))

    const [activeSubscriptionsCount] = await db.select({ count: count() })
      .from(subscriptions)
      .where(eq(subscriptions.status, 'active'))

    const [totalInvoicesCount] = await db.select({ count: count() })
      .from(invoices)
      .where(gte(invoices.issuedDate, startDate.toISOString().split('T')[0]))

    return c.json({
      period: `${days} days`,
      metrics: {
        totalRevenue: totalRevenue.total || 0,
        pendingRevenue: pendingRevenue.total || 0,
        overdueRevenue: overdueRevenue.total || 0,
        activeSubscriptions: activeSubscriptionsCount.count,
        totalInvoices: totalInvoicesCount.count
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Get billing analytics error:', error)
    return c.json({ error: 'Failed to get billing analytics' }, 500)
  }
})

// ===== ANALYTICS REPORTS EXPORT =====

// Export comprehensive analytics reports
app.get('/reports/export', adminAuthMiddleware, requirePermission('analytics:read'), async (c) => {
  try {
    const { type = 'comprehensive', period = '30' } = c.req.query()
    const days = parseInt(period)
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    let csvData = ''
    let filename = `${type}-report-${new Date().toISOString().split('T')[0]}.csv`

    switch (type) {
      case 'revenue':
        // Revenue report
        const revenueData = await db.select({
          date: invoices.createdAt,
          clientName: clients.schoolName,
          amount: invoices.totalAmount,
          status: invoices.status,
          paidDate: invoices.paidDate
        })
        .from(invoices)
        .leftJoin(clients, eq(invoices.clientId, clients.id))
        .where(gte(invoices.createdAt, startDate))
        .orderBy(desc(invoices.createdAt))

        csvData = 'Date,Client Name,Amount,Status,Paid Date\n'
        revenueData.forEach(row => {
          csvData += `${row.date},${row.clientName || 'Unknown'},₹${row.amount},${row.status},${row.paidDate || 'N/A'}\n`
        })
        break

      case 'clients':
        // Client report with partner referral info
        const clientData = await db.select({
          schoolName: clients.schoolName,
          email: clients.email,
          phone: clients.phone,
          classFee: clients.classFee,
          studentCount: clients.actualStudentCount,
          createdAt: clients.createdAt,
          partnerCode: referralCodes.code
        })
        .from(clients)
        .leftJoin(schoolReferrals, eq(clients.id, schoolReferrals.clientId))
        .leftJoin(referralCodes, eq(schoolReferrals.referralCodeId, referralCodes.id))
        .where(gte(clients.createdAt, startDate))
        .orderBy(desc(clients.createdAt))

        csvData = 'School Name,Email,Phone,Class Fee,Student Count,Created Date,Partner Code\n'
        clientData.forEach(row => {
          csvData += `${row.schoolName},${row.email},${row.phone || 'N/A'},₹${row.classFee || 0},${row.studentCount || 0},${row.createdAt},${row.partnerCode || 'Direct'}\n`
        })
        break

      case 'partners':
        // Partner report with earnings from partner_earnings table
        const partnerData = await db.select({
          name: partners.name,
          email: partners.email,
          phone: partners.phone,
          partnerCode: partners.partnerCode,
          totalEarnings: sql<number>`COALESCE(SUM(CAST(${partnerEarnings.partnerEarning} AS DECIMAL)), 0)`,
          createdAt: partners.createdAt
        })
        .from(partners)
        .leftJoin(partnerEarnings, eq(partners.id, partnerEarnings.partnerId))
        .where(gte(partners.createdAt, startDate))
        .groupBy(partners.id, partners.name, partners.email, partners.phone, partners.partnerCode, partners.createdAt)
        .orderBy(desc(partners.createdAt))

        csvData = 'Name,Email,Phone,Partner Code,Total Earnings,Created Date\n'
        partnerData.forEach(row => {
          csvData += `${row.name},${row.email},${row.phone || 'N/A'},${row.partnerCode},₹${row.totalEarnings || 0},${row.createdAt}\n`
        })
        break

      case 'payments':
        // Payment report
        const paymentData = await db.select({
          date: payments.createdAt,
          clientName: clients.schoolName,
          amount: payments.amount,
          status: payments.status,
          paymentMethod: payments.paymentMethod,
          razorpayPaymentId: payments.razorpayPaymentId,
          paidDate: payments.processedAt
        })
        .from(payments)
        .leftJoin(clients, eq(payments.clientId, clients.id))
        .where(gte(payments.createdAt, startDate))
        .orderBy(desc(payments.createdAt))

        csvData = 'Date,Client Name,Amount,Status,Payment Method,Payment ID,Paid Date\n'
        paymentData.forEach(row => {
          csvData += `${row.date},${row.clientName || 'Unknown'},₹${row.amount},${row.status},${row.paymentMethod || 'N/A'},${row.razorpayPaymentId || 'N/A'},${row.paidDate || 'N/A'}\n`
        })
        break

      case 'comprehensive':
      default:
        // Comprehensive report with all key metrics
        const [totalRevenue] = await db.select({
          total: sql<number>`COALESCE(SUM(CAST(${invoices.totalAmount} AS DECIMAL)), 0)`
        })
        .from(invoices)
        .where(and(
          eq(invoices.status, 'paid'),
          gte(invoices.createdAt, startDate)
        ))

        const [totalClients] = await db.select({ count: count() })
          .from(clients)
          .where(gte(clients.createdAt, startDate))

        const [totalLeads] = await db.select({ count: count() })
          .from(leads)
          .where(gte(leads.createdAt, startDate))

        const [totalPartners] = await db.select({ count: count() })
          .from(partners)
          .where(gte(partners.createdAt, startDate))

        csvData = 'Metric,Value,Period\n'
        csvData += `Total Revenue,₹${totalRevenue.total?.toLocaleString() || 0},${period} days\n`
        csvData += `New Clients,${totalClients.count},${period} days\n`
        csvData += `New Leads,${totalLeads.count},${period} days\n`
        csvData += `New Partners,${totalPartners.count},${period} days\n`
        csvData += `Conversion Rate,${totalLeads.count > 0 ? Math.round((totalClients.count / totalLeads.count) * 100) : 0}%,${period} days\n`
        break
    }

    return new Response(csvData, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${filename}"`
      }
    })

  } catch (error) {
    console.error('Export report error:', error)
    return c.json({ error: 'Failed to export report' }, 500)
  }
})

// ===== RAZORPAY PAYMENT MANAGEMENT =====

// Create Razorpay order for invoice payment
app.post('/payments/create-order', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), zValidator('json', z.object({
  invoiceId: z.string().uuid(),
  amount: z.number().min(1),
  currency: z.string().default('INR'),
  receipt: z.string().optional()
})), async (c) => {
  try {
    const { invoiceId, amount, currency, receipt } = c.req.valid('json')

    // Verify invoice exists
    const invoice = await db.select().from(invoices).where(eq(invoices.id, invoiceId)).limit(1)
    if (invoice.length === 0) {
      return c.json({ success: false, error: 'Invoice not found' }, 404)
    }

    // Create Razorpay order
    const orderResult = await razorpayService.createOrder({
      amount: amount * 100, // Convert to paise
      currency,
      receipt: receipt || `INV-${invoiceId.slice(-8)}`,
      notes: {
        invoice_id: invoiceId,
        client_id: invoice[0].clientId || ''
      }
    })

    if (!orderResult.success) {
      return c.json({ success: false, error: orderResult.error }, 500)
    }

    return c.json({
      success: true,
      order: orderResult.order
    })

  } catch (error) {
    console.error('Create Razorpay order error:', error)
    return c.json({ success: false, error: 'Failed to create payment order' }, 500)
  }
})

// Record manual payment
app.post('/payments/record', adminAuthMiddleware, requirePermission('billing:write'), zValidator('json', z.object({
  invoiceId: z.string().uuid(),
  amount: z.number().min(0.01),
  paymentMethod: z.string().min(1),
  transactionId: z.string().optional(),
  notes: z.string().optional()
})), async (c) => {
  try {
    const { invoiceId, amount, paymentMethod, transactionId, notes } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Verify invoice exists and is not already paid
    const [invoice] = await db.select().from(invoices).where(eq(invoices.id, invoiceId)).limit(1)
    if (!invoice) {
      return c.json({ error: 'Invoice not found' }, 404)
    }

    if (invoice.status === 'paid') {
      return c.json({ error: 'Invoice is already paid' }, 400)
    }

    // Create payment record
    const [newPayment] = await db.insert(payments).values({
      invoiceId,
      clientId: invoice.clientId,
      amount: amount.toString(),
      currency: 'INR',
      status: 'success',
      paymentMethod,
      razorpayPaymentId: transactionId || `MANUAL-${Date.now()}`,
      processedAt: new Date()
    }).returning()

    // Update invoice status
    await db.update(invoices)
      .set({
        status: 'paid',
        paidDate: new Date().toISOString().split('T')[0]
      })
      .where(eq(invoices.id, invoiceId))

    // Update billing cycle status if applicable
    if (invoice.billingCycleId) {
      await db.update(billingCycles)
        .set({ status: 'paid' })
        .where(eq(billingCycles.id, invoice.billingCycleId))
    }

    return c.json({
      message: 'Payment recorded successfully',
      payment: newPayment,
      recordedBy: admin.name,
      notes: notes || ''
    })

  } catch (error) {
    console.error('Record payment error:', error)
    return c.json({ error: 'Failed to record payment' }, 500)
  }
})

// Verify Razorpay payment
app.post('/payments/verify', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), zValidator('json', z.object({
  razorpayOrderId: z.string(),
  razorpayPaymentId: z.string(),
  razorpaySignature: z.string(),
  invoiceId: z.string().uuid()
})), async (c) => {
  try {
    const { razorpayOrderId, razorpayPaymentId, razorpaySignature, invoiceId } = c.req.valid('json')

    // Verify payment signature
    const isValid = razorpayService.verifyPaymentSignature({
      razorpayOrderId,
      razorpayPaymentId,
      razorpaySignature
    })

    if (!isValid) {
      return c.json({ success: false, error: 'Invalid payment signature' }, 400)
    }

    // Get payment details from Razorpay
    const paymentResult = await razorpayService.getPayment(razorpayPaymentId)
    if (!paymentResult.success) {
      return c.json({ success: false, error: 'Failed to fetch payment details' }, 500)
    }

    const payment = paymentResult.payment

    // Update invoice status
    await db.update(invoices)
      .set({
        status: 'paid',
        paidDate: new Date().toISOString().split('T')[0]
      })
      .where(eq(invoices.id, invoiceId))

    // Create payment record
    const [newPayment] = await db.insert(payments).values({
      invoiceId,
      clientId: payment.notes?.client_id || '',
      razorpayPaymentId,
      razorpayOrderId,
      amount: (payment.amount / 100).toString(), // Convert from paise
      currency: payment.currency,
      status: 'success',
      paymentMethod: payment.method,
      processedAt: new Date(payment.created_at * 1000)
    }).returning()

    return c.json({
      success: true,
      payment: newPayment,
      message: 'Payment verified and recorded successfully'
    })

  } catch (error) {
    console.error('Verify payment error:', error)
    return c.json({ success: false, error: 'Failed to verify payment' }, 500)
  }
})

// Get payment history for a client
app.get('/payments/client/:clientId', adminAuthMiddleware, requireAdminRole(['super_admin', 'billing']), async (c) => {
  try {
    const clientId = c.req.param('clientId')

    const clientPayments = await db.select({
      id: payments.id,
      invoiceId: payments.invoiceId,
      razorpayPaymentId: payments.razorpayPaymentId,
      amount: payments.amount,
      currency: payments.currency,
      status: payments.status,
      paymentMethod: payments.paymentMethod,
      processedAt: payments.processedAt,
      createdAt: payments.createdAt,
      invoice: {
        invoiceNumber: invoices.invoiceNumber,
        issuedDate: invoices.issuedDate,
        dueDate: invoices.dueDate
      }
    })
    .from(payments)
    .leftJoin(invoices, eq(payments.invoiceId, invoices.id))
    .where(eq(payments.clientId, clientId))
    .orderBy(desc(payments.createdAt))

    return c.json({
      success: true,
      payments: clientPayments
    })

  } catch (error) {
    console.error('Get client payments error:', error)
    return c.json({ success: false, error: 'Failed to fetch payment history' }, 500)
  }
})

// Health check for admin routes
// ===== SUPPORT TICKET MANAGEMENT =====

// Validation schemas for support tickets
const createTicketMessageSchema = z.object({
  message: z.string().min(1, 'Message is required'),
  attachments: z.array(z.string()).optional()
})

const updateTicketSchema = z.object({
  status: z.enum(['open', 'in_progress', 'resolved', 'closed']).optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  assignedTo: z.string().uuid().optional(),
  category: z.string().optional()
})

// Get all support tickets with filtering and pagination
app.get('/support/tickets', adminAuthMiddleware, requirePermission('support:read'), async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '20')
    const status = c.req.query('status')
    const priority = c.req.query('priority')
    const assignedTo = c.req.query('assignedTo')
    const clientId = c.req.query('clientId')
    const search = c.req.query('search') || ''
    const sortBy = c.req.query('sortBy') || 'createdAt'
    const sortOrder = c.req.query('sortOrder') || 'desc'

    const offset = (page - 1) * limit

    // Build where conditions
    let whereConditions = []

    if (status) {
      whereConditions.push(eq(supportTickets.status, status))
    }

    if (priority) {
      whereConditions.push(eq(supportTickets.priority, priority))
    }

    if (assignedTo) {
      whereConditions.push(eq(supportTickets.assignedTo, assignedTo))
    }

    if (clientId) {
      whereConditions.push(eq(supportTickets.clientId, clientId))
    }

    if (search) {
      whereConditions.push(
        sql`(${supportTickets.title} ILIKE ${`%${search}%`} OR ${supportTickets.description} ILIKE ${`%${search}%`})`
      )
    }

    // Get tickets with client and assigned admin info
    const tickets = await db.select({
      id: supportTickets.id,
      title: supportTickets.title,
      description: supportTickets.description,
      priority: supportTickets.priority,
      status: supportTickets.status,
      category: supportTickets.category,
      assignedTo: supportTickets.assignedTo,
      resolvedAt: supportTickets.resolvedAt,
      createdAt: supportTickets.createdAt,
      updatedAt: supportTickets.updatedAt,
      // Client information
      clientId: clients.id,
      clientName: clients.schoolName,
      clientEmail: clients.email,
      // Created by information
      createdById: clientUsers.id,
      createdByName: clientUsers.name,
      createdByEmail: clientUsers.email
    })
    .from(supportTickets)
    .leftJoin(clients, eq(supportTickets.clientId, clients.id))
    .leftJoin(clientUsers, eq(supportTickets.createdBy, clientUsers.id))
    .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
    .orderBy(sortOrder === 'desc' ? desc(supportTickets.createdAt) : supportTickets.createdAt)
    .limit(limit)
    .offset(offset)

    // Get total count for pagination
    const [{ count: totalCount }] = await db.select({ count: count() })
      .from(supportTickets)
      .leftJoin(clients, eq(supportTickets.clientId, clients.id))
      .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)

    // Get assigned admin names for tickets
    const ticketsWithAdmins = await Promise.all(
      tickets.map(async (ticket) => {
        let assignedAdminName = null
        if (ticket.assignedTo) {
          const [admin] = await db.select({ name: adminUsers.name })
            .from(adminUsers)
            .where(eq(adminUsers.id, ticket.assignedTo))
            .limit(1)
          assignedAdminName = admin?.name || null
        }

        return {
          ...ticket,
          assignedAdminName
        }
      })
    )

    return c.json({
      tickets: ticketsWithAdmins,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    })

  } catch (error) {
    console.error('Get support tickets error:', error)
    return c.json({ error: 'Failed to fetch support tickets' }, 500)
  }
})

// Get single support ticket with messages
app.get('/support/tickets/:id', adminAuthMiddleware, requirePermission('support:read'), async (c) => {
  try {
    const ticketId = c.req.param('id')

    // Get ticket details
    const [ticket] = await db.select({
      id: supportTickets.id,
      title: supportTickets.title,
      description: supportTickets.description,
      priority: supportTickets.priority,
      status: supportTickets.status,
      category: supportTickets.category,
      assignedTo: supportTickets.assignedTo,
      resolvedAt: supportTickets.resolvedAt,
      createdAt: supportTickets.createdAt,
      updatedAt: supportTickets.updatedAt,
      // Client information
      clientId: clients.id,
      clientName: clients.schoolName,
      clientEmail: clients.email,
      clientPhone: clients.phone,
      // Created by information
      createdById: clientUsers.id,
      createdByName: clientUsers.name,
      createdByEmail: clientUsers.email
    })
    .from(supportTickets)
    .leftJoin(clients, eq(supportTickets.clientId, clients.id))
    .leftJoin(clientUsers, eq(supportTickets.createdBy, clientUsers.id))
    .where(eq(supportTickets.id, ticketId))
    .limit(1)

    if (!ticket) {
      return c.json({ error: 'Ticket not found' }, 404)
    }

    // Get assigned admin name
    let assignedAdminName = null
    if (ticket.assignedTo) {
      const [admin] = await db.select({ name: adminUsers.name, email: adminUsers.email })
        .from(adminUsers)
        .where(eq(adminUsers.id, ticket.assignedTo))
        .limit(1)
      assignedAdminName = admin?.name || null
    }

    // Get ticket messages
    const messages = await db.select({
      id: ticketMessages.id,
      message: ticketMessages.message,
      senderType: ticketMessages.senderType,
      senderId: ticketMessages.senderId,
      attachments: ticketMessages.attachments,
      createdAt: ticketMessages.createdAt
    })
    .from(ticketMessages)
    .where(eq(ticketMessages.ticketId, ticketId))
    .orderBy(ticketMessages.createdAt)

    // Get sender names for messages
    const messagesWithSenders = await Promise.all(
      messages.map(async (message) => {
        let senderName = 'Unknown'
        let senderEmail = ''

        if (message.senderType === 'admin') {
          const [admin] = await db.select({ name: adminUsers.name, email: adminUsers.email })
            .from(adminUsers)
            .where(eq(adminUsers.id, message.senderId))
            .limit(1)
          senderName = admin?.name || 'Admin'
          senderEmail = admin?.email || ''
        } else if (message.senderType === 'client') {
          const [client] = await db.select({ name: clientUsers.name, email: clientUsers.email })
            .from(clientUsers)
            .where(eq(clientUsers.id, message.senderId))
            .limit(1)
          senderName = client?.name || 'Client'
          senderEmail = client?.email || ''
        }

        return {
          ...message,
          senderName,
          senderEmail
        }
      })
    )

    return c.json({
      ticket: {
        ...ticket,
        assignedAdminName
      },
      messages: messagesWithSenders
    })

  } catch (error) {
    console.error('Get support ticket error:', error)
    return c.json({ error: 'Failed to fetch support ticket' }, 500)
  }
})

// Update support ticket (status, priority, assignment, etc.)
app.put('/support/tickets/:id', adminAuthMiddleware, requirePermission('support:write'), zValidator('json', updateTicketSchema), async (c) => {
  try {
    const ticketId = c.req.param('id')
    const updateData = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Verify ticket exists
    const [existingTicket] = await db.select({ id: supportTickets.id, status: supportTickets.status })
      .from(supportTickets)
      .where(eq(supportTickets.id, ticketId))
      .limit(1)

    if (!existingTicket) {
      return c.json({ error: 'Ticket not found' }, 404)
    }

    // Prepare update data
    const updateFields: any = {
      updatedAt: new Date()
    }

    if (updateData.status) {
      updateFields.status = updateData.status
      if (updateData.status === 'resolved' || updateData.status === 'closed') {
        updateFields.resolvedAt = new Date()
      }
    }

    if (updateData.priority) {
      updateFields.priority = updateData.priority
    }

    if (updateData.assignedTo !== undefined) {
      updateFields.assignedTo = updateData.assignedTo
    }

    if (updateData.category) {
      updateFields.category = updateData.category
    }

    // Update ticket
    await db.update(supportTickets)
      .set(updateFields)
      .where(eq(supportTickets.id, ticketId))

    // Add system message for status changes
    if (updateData.status && updateData.status !== existingTicket.status) {
      await db.insert(ticketMessages).values({
        ticketId,
        senderType: 'admin',
        senderId: admin.id,
        message: `Ticket status changed from "${existingTicket.status}" to "${updateData.status}" by ${admin.name}`
      })
    }

    return c.json({
      message: 'Ticket updated successfully',
      ticketId,
      updatedBy: admin.name,
      updatedAt: new Date().toISOString()
    })

  } catch (error) {
    console.error('Update support ticket error:', error)
    return c.json({ error: 'Failed to update support ticket' }, 500)
  }
})

// Add message to support ticket
app.post('/support/tickets/:id/messages', adminAuthMiddleware, requirePermission('support:write'), zValidator('json', createTicketMessageSchema), async (c) => {
  try {
    const ticketId = c.req.param('id')
    const { message, attachments } = c.req.valid('json')
    const admin = getCurrentAdmin(c)

    if (!admin) {
      return c.json({ error: 'Admin not found' }, 401)
    }

    // Verify ticket exists
    const [ticket] = await db.select({ id: supportTickets.id })
      .from(supportTickets)
      .where(eq(supportTickets.id, ticketId))
      .limit(1)

    if (!ticket) {
      return c.json({ error: 'Ticket not found' }, 404)
    }

    // Insert message
    const [newMessage] = await db.insert(ticketMessages).values({
      ticketId,
      senderType: 'admin',
      senderId: admin.id,
      message,
      attachments: attachments || null
    }).returning()

    // Update ticket's updatedAt timestamp
    await db.update(supportTickets)
      .set({ updatedAt: new Date() })
      .where(eq(supportTickets.id, ticketId))

    return c.json({
      message: 'Message added successfully',
      messageId: newMessage.id,
      ticketId,
      sentBy: admin.name,
      sentAt: newMessage.createdAt
    })

  } catch (error) {
    console.error('Add ticket message error:', error)
    return c.json({ error: 'Failed to add message to ticket' }, 500)
  }
})

// Get support ticket analytics
app.get('/support/analytics', adminAuthMiddleware, requirePermission('support:read'), async (c) => {
  try {
    const period = c.req.query('period') || '30' // days

    const startDate = new Date()
    startDate.setDate(startDate.getDate() - parseInt(period))

    // Get ticket counts by status
    const statusCounts = await db.select({
      status: supportTickets.status,
      count: count()
    })
    .from(supportTickets)
    .where(gte(supportTickets.createdAt, startDate))
    .groupBy(supportTickets.status)

    // Get ticket counts by priority
    const priorityCounts = await db.select({
      priority: supportTickets.priority,
      count: count()
    })
    .from(supportTickets)
    .where(gte(supportTickets.createdAt, startDate))
    .groupBy(supportTickets.priority)

    // Get ticket counts by category
    const categoryCounts = await db.select({
      category: supportTickets.category,
      count: count()
    })
    .from(supportTickets)
    .where(gte(supportTickets.createdAt, startDate))
    .groupBy(supportTickets.category)

    // Get assigned admin performance
    const adminPerformance = await db.select({
      adminId: supportTickets.assignedTo,
      totalAssigned: count(),
      resolved: sql<number>`COUNT(CASE WHEN ${supportTickets.status} IN ('resolved', 'closed') THEN 1 END)`
    })
    .from(supportTickets)
    .where(and(
      gte(supportTickets.createdAt, startDate),
      isNotNull(supportTickets.assignedTo)
    ))
    .groupBy(supportTickets.assignedTo)

    // Get admin names for performance data
    const adminPerformanceWithNames = await Promise.all(
      adminPerformance.map(async (perf) => {
        const [admin] = await db.select({ name: adminUsers.name })
          .from(adminUsers)
          .where(eq(adminUsers.id, perf.adminId!))
          .limit(1)

        return {
          adminName: admin?.name || 'Unknown',
          totalAssigned: perf.totalAssigned,
          resolved: perf.resolved,
          resolutionRate: perf.totalAssigned > 0 ? Math.round((perf.resolved / perf.totalAssigned) * 100) : 0
        }
      })
    )

    // Get total metrics
    const [totalMetrics] = await db.select({
      totalTickets: count(),
      openTickets: sql<number>`COUNT(CASE WHEN ${supportTickets.status} = 'open' THEN 1 END)`,
      inProgressTickets: sql<number>`COUNT(CASE WHEN ${supportTickets.status} = 'in_progress' THEN 1 END)`,
      resolvedTickets: sql<number>`COUNT(CASE WHEN ${supportTickets.status} IN ('resolved', 'closed') THEN 1 END)`,
      urgentTickets: sql<number>`COUNT(CASE WHEN ${supportTickets.priority} = 'urgent' THEN 1 END)`,
      unassignedTickets: sql<number>`COUNT(CASE WHEN ${supportTickets.assignedTo} IS NULL THEN 1 END)`
    })
    .from(supportTickets)
    .where(gte(supportTickets.createdAt, startDate))

    return c.json({
      period: `${period} days`,
      totalMetrics,
      statusBreakdown: statusCounts,
      priorityBreakdown: priorityCounts,
      categoryBreakdown: categoryCounts,
      adminPerformance: adminPerformanceWithNames
    })

  } catch (error) {
    console.error('Support analytics error:', error)
    return c.json({ error: 'Failed to fetch support analytics' }, 500)
  }
})

// Get available admins for ticket assignment
app.get('/support/available-admins', adminAuthMiddleware, requirePermission('support:read'), async (c) => {
  try {
    const admins = await db.select({
      id: adminUsers.id,
      name: adminUsers.name,
      email: adminUsers.email,
      role: adminUsers.role
    })
    .from(adminUsers)
    .where(and(
      eq(adminUsers.isActive, true),
      sql`${adminUsers.permissions} ? 'support:write'` // Has support write permission
    ))
    .orderBy(adminUsers.name)

    return c.json({ admins })

  } catch (error) {
    console.error('Get available admins error:', error)
    return c.json({ error: 'Failed to fetch available admins' }, 500)
  }
})

// ===== SECURITY ENDPOINTS =====

// Get security dashboard data
app.get('/security/dashboard', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const admin = getCurrentAdmin(c)
    if (!admin) {
      return c.json({ error: 'Admin authentication required' }, 401)
    }

    const timeRange = c.req.query('timeRange') as 'hour' | 'day' | 'week' | 'month' || 'day'

    // Get security dashboard data
    const dashboardData = await securityMonitor.getSecurityDashboard(timeRange)

    // Log admin access to security dashboard
    await auditLogger.logAdmin('security_dashboard_access', {
      adminId: admin.id,
      resource: 'security_dashboard',
      details: {
        timeRange,
        accessTime: new Date().toISOString()
      },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    return c.json({
      success: true,
      data: dashboardData
    })

  } catch (error) {
    console.error('Security dashboard error:', error)
    return c.json({ error: 'Failed to fetch security dashboard data' }, 500)
  }
})

// Get audit logs
app.get('/security/audit-logs', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const admin = getCurrentAdmin(c)
    if (!admin) {
      return c.json({ error: 'Admin authentication required' }, 401)
    }

    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '50')
    const category = c.req.query('category') as string
    const severity = c.req.query('severity') as string
    const startDate = c.req.query('startDate') as string
    const endDate = c.req.query('endDate') as string

    const offset = (page - 1) * limit

    // Build query conditions
    const conditions = []
    if (category) {
      conditions.push(eq(auditLogs.category, category))
    }
    if (severity) {
      conditions.push(eq(auditLogs.severity, severity))
    }
    if (startDate) {
      conditions.push(gte(auditLogs.timestamp, new Date(startDate)))
    }
    if (endDate) {
      conditions.push(lte(auditLogs.timestamp, new Date(endDate)))
    }

    // Get audit logs
    const logs = await db
      .select()
      .from(auditLogs)
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .orderBy(desc(auditLogs.timestamp))
      .limit(limit)
      .offset(offset)

    // Get total count
    const [{ count: totalCount }] = await db
      .select({ count: count() })
      .from(auditLogs)
      .where(conditions.length > 0 ? and(...conditions) : undefined)

    // Log admin access to audit logs
    await auditLogger.logAdmin('audit_logs_access', {
      adminId: admin.id,
      resource: 'audit_logs',
      details: {
        filters: { category, severity, startDate, endDate },
        page,
        limit,
        accessTime: new Date().toISOString()
      },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    return c.json({
      success: true,
      data: {
        logs,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit)
        }
      }
    })

  } catch (error) {
    console.error('Audit logs error:', error)
    return c.json({ error: 'Failed to fetch audit logs' }, 500)
  }
})

// Get security events
app.get('/security/events', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const admin = getCurrentAdmin(c)
    if (!admin) {
      return c.json({ error: 'Admin authentication required' }, 401)
    }

    const page = parseInt(c.req.query('page') || '1')
    const limit = parseInt(c.req.query('limit') || '50')
    const eventType = c.req.query('eventType') as string
    const severity = c.req.query('severity') as string
    const resolved = c.req.query('resolved') as string

    const offset = (page - 1) * limit

    // Build query conditions
    const conditions = []
    if (eventType) {
      conditions.push(eq(securityEvents.eventType, eventType))
    }
    if (severity) {
      conditions.push(eq(securityEvents.severity, severity))
    }
    if (resolved !== undefined) {
      conditions.push(eq(securityEvents.resolved, resolved === 'true'))
    }

    // Get security events
    const events = await db
      .select()
      .from(securityEvents)
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .orderBy(desc(securityEvents.createdAt))
      .limit(limit)
      .offset(offset)

    // Get total count
    const [{ count: totalCount }] = await db
      .select({ count: count() })
      .from(securityEvents)
      .where(conditions.length > 0 ? and(...conditions) : undefined)

    return c.json({
      success: true,
      data: {
        events,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit)
        }
      }
    })

  } catch (error) {
    console.error('Security events error:', error)
    return c.json({ error: 'Failed to fetch security events' }, 500)
  }
})

// ===== BILLING MONITORING & AUTOMATION ENDPOINTS =====

app.get('/billing/health', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const healthReport = await billingMonitor.monitorBillingHealth()

    await auditLogger.logAdmin('billing_health_access', {
      adminId: (c.get('adminUser') as any)?.id || 'unknown',
      resource: 'billing_health',
      details: {
        status: healthReport.status,
        alertCount: healthReport.alerts.length
      },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    return c.json(healthReport)

  } catch (error) {
    console.error('Billing health error:', error)
    return c.json({ error: 'Failed to get billing health' }, 500)
  }
})

app.get('/billing/dunning/stats', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const stats = await dunningManager.getDunningStats()

    await auditLogger.logAdmin('dunning_stats_access', {
      adminId: (c.get('adminUser') as any)?.id || 'unknown',
      resource: 'dunning_stats',
      details: { totalCases: stats.totalCases },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    return c.json(stats)

  } catch (error) {
    console.error('Dunning stats error:', error)
    return c.json({ error: 'Failed to get dunning stats' }, 500)
  }
})

app.get('/billing/dunning/cases', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const cases = await dunningManager.getActiveDunningCases()

    await auditLogger.logAdmin('dunning_cases_access', {
      adminId: (c.get('adminUser') as any)?.id || 'unknown',
      resource: 'dunning_cases',
      details: { caseCount: cases.length },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    return c.json({ cases })

  } catch (error) {
    console.error('Dunning cases error:', error)
    return c.json({ error: 'Failed to get dunning cases' }, 500)
  }
})

app.get('/billing/payment-failures/stats', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const days = parseInt(c.req.query('days') || '30')
    const stats = await paymentFailureHandler.getPaymentFailureStats(days)

    await auditLogger.logAdmin('payment_failure_stats_access', {
      adminId: (c.get('adminUser') as any)?.id || 'unknown',
      resource: 'payment_failure_stats',
      details: { days, totalFailures: stats.totalFailures },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    return c.json(stats)

  } catch (error) {
    console.error('Payment failure stats error:', error)
    return c.json({ error: 'Failed to get payment failure stats' }, 500)
  }
})

app.get('/billing/payment-failures', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const failures = await paymentFailureHandler.getActivePaymentFailures()

    await auditLogger.logAdmin('payment_failures_access', {
      adminId: (c.get('adminUser') as any)?.id || 'unknown',
      resource: 'payment_failures',
      details: { failureCount: failures.length },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    return c.json({ failures })

  } catch (error) {
    console.error('Payment failures error:', error)
    return c.json({ error: 'Failed to get payment failures' }, 500)
  }
})

app.post('/billing/payment-failures/:paymentId/retry', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const paymentId = c.req.param('paymentId')
    const adminId = (c.get('adminUser') as any)?.id || 'unknown'

    const result = await paymentFailureHandler.manualRetryPayment(paymentId, adminId)

    return c.json(result)

  } catch (error) {
    console.error('Manual payment retry error:', error)
    return c.json({ error: 'Failed to retry payment' }, 500)
  }
})

app.post('/billing/dunning/process', adminAuthMiddleware, requirePermission('admin:all'), async (c) => {
  try {
    const adminId = (c.get('adminUser') as any)?.id || 'unknown'

    const result = await dunningManager.processDunningActions()

    await auditLogger.logAdmin('manual_dunning_process', {
      adminId,
      resource: 'dunning_manager',
      details: {
        processed: result.processed,
        actionsExecuted: result.actionsExecuted,
        errors: result.errors
      },
      ipAddress: c.req.header('x-forwarded-for') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
      success: true
    })

    return c.json(result)

  } catch (error) {
    console.error('Manual dunning process error:', error)
    return c.json({ error: 'Failed to process dunning actions' }, 500)
  }
})

app.get("/health", (c) => {
  return c.json({
    status: "ok",
    service: "Admin API",
    timestamp: new Date().toISOString()
  })
})

export default app
