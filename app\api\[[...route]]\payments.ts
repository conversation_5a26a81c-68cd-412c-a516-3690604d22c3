import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import { db } from '@/src/db'
import { 
  invoices, 
  payments, 
  clients, 
  subscriptions,
  billingCycles 
} from '@/src/db/schema'
import { eq, and, desc } from 'drizzle-orm'
import { razorpayService } from '@/src/services/razorpayService'

const app = new Hono()

// Create payment order for client invoice
app.post('/create-order', zValidator('json', z.object({
  invoiceId: z.string().uuid(),
  clientEmail: z.string().email()
})), async (c) => {
  try {
    const { invoiceId, clientEmail } = c.req.valid('json')

    // Verify invoice and client
    const invoiceData = await db.select({
      id: invoices.id,
      invoiceNumber: invoices.invoiceNumber,
      totalAmount: invoices.totalAmount,
      status: invoices.status,
      clientId: invoices.clientId,
      client: {
        schoolName: clients.schoolName,
        email: clients.email
      }
    })
    .from(invoices)
    .leftJoin(clients, eq(invoices.clientId, clients.id))
    .where(and(
      eq(invoices.id, invoiceId),
      eq(clients.email, clientEmail)
    ))
    .limit(1)

    if (invoiceData.length === 0) {
      return c.json({ 
        success: false, 
        error: 'Invoice not found or unauthorized access' 
      }, 404)
    }

    const invoice = invoiceData[0]

    // Check if invoice is already paid
    if (invoice.status === 'paid') {
      return c.json({ 
        success: false, 
        error: 'Invoice is already paid' 
      }, 400)
    }

    // Create Razorpay order
    const amount = Math.round(parseFloat(invoice.totalAmount) * 100) // Convert to paise
    const orderResult = await razorpayService.createOrder({
      amount,
      currency: 'INR',
      receipt: `PAY-${invoice.invoiceNumber}`,
      notes: {
        invoice_id: invoiceId,
        client_id: invoice.clientId || '',
        school_name: invoice.client?.schoolName || '',
        invoice_number: invoice.invoiceNumber
      }
    })

    if (!orderResult.success) {
      return c.json({ 
        success: false, 
        error: 'Failed to create payment order' 
      }, 500)
    }

    return c.json({
      success: true,
      order: {
        id: orderResult.order.id,
        amount: orderResult.order.amount,
        currency: orderResult.order.currency,
        receipt: orderResult.order.receipt
      },
      invoice: {
        id: invoice.id,
        number: invoice.invoiceNumber,
        amount: invoice.totalAmount,
        schoolName: invoice.client?.schoolName
      }
    })

  } catch (error) {
    console.error('Create payment order error:', error)
    return c.json({ 
      success: false, 
      error: 'Failed to create payment order' 
    }, 500)
  }
})

// Verify and complete payment
app.post('/verify', zValidator('json', z.object({
  razorpayOrderId: z.string(),
  razorpayPaymentId: z.string(),
  razorpaySignature: z.string(),
  invoiceId: z.string().uuid(),
  clientEmail: z.string().email()
})), async (c) => {
  try {
    const { 
      razorpayOrderId, 
      razorpayPaymentId, 
      razorpaySignature, 
      invoiceId, 
      clientEmail 
    } = c.req.valid('json')

    // Verify invoice and client authorization
    const invoiceData = await db.select({
      id: invoices.id,
      clientId: invoices.clientId,
      totalAmount: invoices.totalAmount,
      status: invoices.status,
      client: {
        email: clients.email,
        schoolName: clients.schoolName
      }
    })
    .from(invoices)
    .leftJoin(clients, eq(invoices.clientId, clients.id))
    .where(and(
      eq(invoices.id, invoiceId),
      eq(clients.email, clientEmail)
    ))
    .limit(1)

    if (invoiceData.length === 0) {
      return c.json({ 
        success: false, 
        error: 'Invoice not found or unauthorized access' 
      }, 404)
    }

    const invoice = invoiceData[0]

    // Check if already paid
    if (invoice.status === 'paid') {
      return c.json({ 
        success: false, 
        error: 'Invoice is already paid' 
      }, 400)
    }

    // Verify payment signature
    const isValid = razorpayService.verifyPaymentSignature({
      razorpayOrderId,
      razorpayPaymentId,
      razorpaySignature
    })

    if (!isValid) {
      return c.json({ 
        success: false, 
        error: 'Invalid payment signature' 
      }, 400)
    }

    // Get payment details from Razorpay
    const paymentResult = await razorpayService.getPayment(razorpayPaymentId)
    if (!paymentResult.success) {
      return c.json({ 
        success: false, 
        error: 'Failed to verify payment with gateway' 
      }, 500)
    }

    const payment = paymentResult.payment

    // Update invoice status
    await db.update(invoices)
      .set({
        status: 'paid',
        paidDate: new Date().toISOString().split('T')[0]
      })
      .where(eq(invoices.id, invoiceId))

    // Create payment record
    const [newPayment] = await db.insert(payments).values({
      invoiceId,
      clientId: invoice.clientId || '',
      razorpayPaymentId,
      razorpayOrderId,
      amount: (payment.amount / 100).toString(), // Convert from paise
      currency: payment.currency,
      status: 'success',
      paymentMethod: payment.method,
      processedAt: new Date(payment.created_at * 1000)
    }).returning()

    // Update billing cycle status if applicable
    const billingCycle = await db.select()
      .from(billingCycles)
      .where(eq(billingCycles.id, invoice.id))
      .limit(1)

    if (billingCycle.length > 0) {
      await db.update(billingCycles)
        .set({ status: 'paid' })
        .where(eq(billingCycles.id, billingCycle[0].id))
    }

    return c.json({
      success: true,
      message: 'Payment completed successfully',
      payment: {
        id: newPayment.id,
        amount: newPayment.amount,
        currency: newPayment.currency,
        status: newPayment.status,
        processedAt: newPayment.processedAt
      }
    })

  } catch (error) {
    console.error('Verify payment error:', error)
    return c.json({ 
      success: false, 
      error: 'Failed to verify payment' 
    }, 500)
  }
})

// Get client's pending invoices
app.get('/invoices/:clientEmail', async (c) => {
  try {
    const clientEmail = c.req.param('clientEmail')

    const pendingInvoices = await db.select({
      id: invoices.id,
      invoiceNumber: invoices.invoiceNumber,
      amount: invoices.amount,
      taxAmount: invoices.taxAmount,
      totalAmount: invoices.totalAmount,
      status: invoices.status,
      issuedDate: invoices.issuedDate,
      dueDate: invoices.dueDate,
      billingCycle: {
        cycleStart: billingCycles.cycleStart,
        cycleEnd: billingCycles.cycleEnd,
        studentCount: billingCycles.studentCount
      }
    })
    .from(invoices)
    .leftJoin(clients, eq(invoices.clientId, clients.id))
    .leftJoin(billingCycles, eq(invoices.billingCycleId, billingCycles.id))
    .where(and(
      eq(clients.email, clientEmail),
      eq(invoices.status, 'sent')
    ))
    .orderBy(desc(invoices.issuedDate))

    return c.json({
      success: true,
      invoices: pendingInvoices
    })

  } catch (error) {
    console.error('Get client invoices error:', error)
    return c.json({ 
      success: false, 
      error: 'Failed to fetch invoices' 
    }, 500)
  }
})

// Get client's payment history
app.get('/history/:clientEmail', async (c) => {
  try {
    const clientEmail = c.req.param('clientEmail')

    const paymentHistory = await db.select({
      id: payments.id,
      amount: payments.amount,
      currency: payments.currency,
      status: payments.status,
      paymentMethod: payments.paymentMethod,
      processedAt: payments.processedAt,
      invoice: {
        invoiceNumber: invoices.invoiceNumber,
        issuedDate: invoices.issuedDate,
        totalAmount: invoices.totalAmount
      }
    })
    .from(payments)
    .leftJoin(invoices, eq(payments.invoiceId, invoices.id))
    .leftJoin(clients, eq(payments.clientId, clients.id))
    .where(eq(clients.email, clientEmail))
    .orderBy(desc(payments.processedAt))

    return c.json({
      success: true,
      payments: paymentHistory
    })

  } catch (error) {
    console.error('Get payment history error:', error)
    return c.json({ 
      success: false, 
      error: 'Failed to fetch payment history' 
    }, 500)
  }
})

export default app
