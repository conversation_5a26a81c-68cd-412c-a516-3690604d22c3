'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  UserCheck,
  FileText,
  TrendingUp,
  Shield,
  LogOut,
  Eye,
  Edit,
  Trash2,
  Plus,
  Search,
  Filter,
  Download,
  Handshake,
  DollarSign,
  Building2,
  CreditCard,
  X,
  CheckCircle,
  ArrowUp,
  AlertCircle,
  Clock,
  BarChart3,
  UserCog,
  MessageSquare
} from 'lucide-react'

interface AdminUser {
  id: string
  email: string
  name: string
  role: string
  permissions: string[]
}

interface DashboardStats {
  totalLeads: number
  totalClients: number
  totalUsers: number
  totalRequests: number
  recentLeads: any[]
  recentClients: any[]
  recentRequests: any[]
}

interface PartnerStats {
  totalPartners: number
  activePartners: number
  totalReferrals: number
  pendingWithdrawals: number
  totalEarnings: number
  availableBalance: number
}

interface ClientAnalytics {
  totalClients: number
  clientsWithFees: number
  averageFee: number
  totalStudents: number
  feeRange: {
    min: number
    max: number
  }
  partnerAttributed: number
}

export default function AdminDashboardPage() {
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null)
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [partnerStats, setPartnerStats] = useState<PartnerStats | null>(null)
  const [clientAnalytics, setClientAnalytics] = useState<ClientAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    // Check admin authentication
    const token = localStorage.getItem('adminToken')
    const userData = localStorage.getItem('adminUser')

    if (!token || !userData) {
      window.location.href = '/admin/login'
      return
    }

    try {
      const parsedUser = JSON.parse(userData)
      setAdminUser(parsedUser)
      fetchDashboardData(token)
      fetchClientAnalytics(token)
    } catch (error) {
      console.error('Error parsing admin user data:', error)
      handleLogout()
    }
  }, [])

  const fetchDashboardData = async (token: string) => {
    try {
      const response = await fetch('/api/admin/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        if (response.status === 401) {
          handleLogout()
          return
        }
        throw new Error('Failed to fetch dashboard data')
      }

      const data = await response.json()
      setStats(data)
      setPartnerStats(data.partnerStats)
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchClientAnalytics = async (token: string) => {
    try {
      const response = await fetch('/api/admin/clients/analytics', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setClientAnalytics(data.analytics)
      }
    } catch (error) {
      console.error('Error fetching client analytics:', error)
    }
  }

  const handleLogout = () => {
    localStorage.removeItem('adminToken')
    localStorage.removeItem('adminUser')
    window.location.href = '/admin/login'
  }

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'super_admin': return 'bg-red-500'
      case 'sales': return 'bg-green-500'
      case 'support': return 'bg-blue-500'
      case 'billing': return 'bg-yellow-500'
      default: return 'bg-gray-500'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-50 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-slate-600">Loading admin dashboard...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Shield className="w-8 h-8 text-blue-600" />
                <h1 className="text-xl font-bold text-slate-900">Schopio Admin</h1>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="text-right">
                  <p className="text-sm font-medium text-slate-900">{adminUser?.name}</p>
                  <p className="text-xs text-slate-500">{adminUser?.email}</p>
                </div>
                <Badge className={`${getRoleBadgeColor(adminUser?.role || '')} text-white`}>
                  {adminUser?.role?.replace('_', ' ').toUpperCase()}
                </Badge>
              </div>
              <Button
                onClick={handleLogout}
                variant="outline"
                size="sm"
                className="flex items-center space-x-1"
              >
                <LogOut className="w-4 h-4" />
                <span>Logout</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white border-b border-slate-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview', icon: TrendingUp },
              { id: 'leads', label: 'Leads', icon: Users },
              { id: 'clients', label: 'Clients', icon: UserCheck },
              { id: 'requests', label: 'Software Requests', icon: FileText },
              { id: 'partners', label: 'Partners', icon: Handshake },
              { id: 'financial', label: 'Financial', icon: DollarSign },
              { id: 'support', label: 'Support Tickets', icon: MessageSquare },
              { id: 'analytics', label: 'Analytics', icon: BarChart3 },
              { id: 'admin-users', label: 'Admin Users', icon: UserCog }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-slate-600">Total Leads</p>
                        <p className="text-3xl font-bold text-slate-900">{stats?.totalLeads || 0}</p>
                      </div>
                      <Users className="w-8 h-8 text-blue-500" />
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-slate-600">Total Clients</p>
                        <p className="text-3xl font-bold text-slate-900">{stats?.totalClients || 0}</p>
                      </div>
                      <UserCheck className="w-8 h-8 text-green-500" />
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-slate-600">Total Users</p>
                        <p className="text-3xl font-bold text-slate-900">{stats?.totalUsers || 0}</p>
                      </div>
                      <Users className="w-8 h-8 text-purple-500" />
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-slate-600">Software Requests</p>
                        <p className="text-3xl font-bold text-slate-900">{stats?.totalRequests || 0}</p>
                      </div>
                      <FileText className="w-8 h-8 text-orange-500" />
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Client Analytics Cards */}
            {clientAnalytics && (
              <div>
                <h3 className="text-lg font-semibold text-slate-900 mb-4">School Analytics Overview</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                  >
                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-slate-600">Average Class Fee</p>
                            <p className="text-3xl font-bold text-slate-900">₹{clientAnalytics.averageFee.toLocaleString()}</p>
                            <p className="text-sm text-blue-600">{clientAnalytics.clientsWithFees} schools set fees</p>
                          </div>
                          <DollarSign className="w-8 h-8 text-green-500" />
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                  >
                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-slate-600">Total Students</p>
                            <p className="text-3xl font-bold text-slate-900">{clientAnalytics.totalStudents.toLocaleString()}</p>
                            <p className="text-sm text-green-600">Across all schools</p>
                          </div>
                          <Users className="w-8 h-8 text-blue-500" />
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                  >
                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-slate-600">Fee Range</p>
                            <p className="text-2xl font-bold text-slate-900">₹{clientAnalytics.feeRange.min.toLocaleString()}</p>
                            <p className="text-sm text-slate-600">to ₹{clientAnalytics.feeRange.max.toLocaleString()}</p>
                          </div>
                          <TrendingUp className="w-8 h-8 text-purple-500" />
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7 }}
                  >
                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-slate-600">Fee Coverage</p>
                            <p className="text-3xl font-bold text-slate-900">{Math.round((clientAnalytics.clientsWithFees / clientAnalytics.totalClients) * 100)}%</p>
                            <p className="text-sm text-orange-600">Schools with fees set</p>
                          </div>
                          <BarChart3 className="w-8 h-8 text-orange-500" />
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 }}
                  >
                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-slate-600">Partner Attribution</p>
                            <p className="text-3xl font-bold text-slate-900">
                              {clientAnalytics.partnerAttributed || 0}
                            </p>
                            <p className="text-sm text-purple-600">
                              {clientAnalytics.totalClients > 0
                                ? Math.round(((clientAnalytics.partnerAttributed || 0) / clientAnalytics.totalClients) * 100)
                                : 0}% via partners
                            </p>
                          </div>
                          <Handshake className="w-8 h-8 text-purple-500" />
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                </div>
              </div>
            )}

            {/* Partner Stats Cards */}
            {partnerStats && (
              <div>
                <h3 className="text-lg font-semibold text-slate-900 mb-4">Partner System Overview</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                  >
                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-slate-600">Total Partners</p>
                            <p className="text-3xl font-bold text-slate-900">{partnerStats.totalPartners}</p>
                            <p className="text-sm text-green-600">{partnerStats.activePartners} active</p>
                          </div>
                          <Handshake className="w-8 h-8 text-blue-500" />
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                  >
                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-slate-600">Total Referrals</p>
                            <p className="text-3xl font-bold text-slate-900">{partnerStats.totalReferrals}</p>
                            <p className="text-sm text-slate-500">Schools referred</p>
                          </div>
                          <Building2 className="w-8 h-8 text-green-500" />
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7 }}
                  >
                    <Card>
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-slate-600">Total Earnings</p>
                            <p className="text-3xl font-bold text-slate-900">₹{partnerStats.totalEarnings.toLocaleString()}</p>
                            <p className="text-sm text-orange-600">₹{partnerStats.availableBalance.toLocaleString()} available</p>
                          </div>
                          <DollarSign className="w-8 h-8 text-orange-500" />
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>

                  {partnerStats.pendingWithdrawals > 0 && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.8 }}
                    >
                      <Card>
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-slate-600">Pending Withdrawals</p>
                              <p className="text-3xl font-bold text-red-600">{partnerStats.pendingWithdrawals}</p>
                              <p className="text-sm text-slate-500">Require attention</p>
                            </div>
                            <CreditCard className="w-8 h-8 text-red-500" />
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  )}
                </div>
              </div>
            )}

            {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Users className="w-5 h-5" />
                    <span>Recent Leads</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {stats?.recentLeads?.slice(0, 5).map((lead, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                        <div>
                          <p className="font-medium text-slate-900">{lead.schoolName}</p>
                          <p className="text-sm text-slate-500">{lead.email}</p>
                        </div>
                        <Badge variant="outline">{lead.status}</Badge>
                      </div>
                    )) || (
                      <p className="text-slate-500 text-center py-4">No recent leads</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="w-5 h-5" />
                    <span>Recent Requests</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {stats?.recentRequests?.slice(0, 5).map((request, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                        <div>
                          <p className="font-medium text-slate-900">{request.schoolName}</p>
                          <p className="text-sm text-slate-500">{request.requestType}</p>
                        </div>
                        <Badge variant="outline">{request.status}</Badge>
                      </div>
                    )) || (
                      <p className="text-slate-500 text-center py-4">No recent requests</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Leads Management */}
        {activeTab === 'leads' && (
          <LeadsManagement adminToken={localStorage.getItem('adminToken') || ''} />
        )}

        {/* Clients Management */}
        {activeTab === 'clients' && (
          <ClientsManagement adminToken={localStorage.getItem('adminToken') || ''} />
        )}

        {/* Software Requests Management */}
        {activeTab === 'requests' && (
          <RequestsManagement adminToken={localStorage.getItem('adminToken') || ''} />
        )}

        {/* Partners Management */}
        {activeTab === 'partners' && (
          <PartnersManagement adminToken={localStorage.getItem('adminToken') || ''} />
        )}

        {/* Financial Management */}
        {activeTab === 'financial' && (
          <FinancialManagement adminToken={localStorage.getItem('adminToken') || ''} />
        )}

        {/* Support Tickets Management */}
        {activeTab === 'support' && (
          <SupportTicketsManagement adminToken={localStorage.getItem('adminToken') || ''} />
        )}

        {/* Analytics & Reporting Dashboard */}
        {activeTab === 'analytics' && (
          <AnalyticsReportingDashboard adminToken={localStorage.getItem('adminToken') || ''} />
        )}

        {/* Admin Users Management */}
        {activeTab === 'admin-users' && (
          <AdminUsersManagement adminToken={localStorage.getItem('adminToken') || ''} />
        )}
      </main>
    </div>
  )
}

// Leads Management Component
function LeadsManagement({ adminToken }: { adminToken: string }) {
  const [leads, setLeads] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [selectedLead, setSelectedLead] = useState<any>(null)
  const [showViewModal, setShowViewModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showConvertModal, setShowConvertModal] = useState(false)
  const [editFormData, setEditFormData] = useState<any>({})
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    fetchLeads()
  }, [searchTerm, statusFilter])

  const fetchLeads = async () => {
    try {
      const params = new URLSearchParams()
      if (searchTerm) params.append('search', searchTerm)
      if (statusFilter) params.append('status', statusFilter)

      const response = await fetch(`/api/admin/leads?${params}`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (response.ok) {
        const data = await response.json()
        setLeads(data.leads || [])
      }
    } catch (error) {
      console.error('Error fetching leads:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleViewLead = async (leadId: string) => {
    try {
      const response = await fetch(`/api/admin/leads/${leadId}`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (response.ok) {
        const data = await response.json()
        setSelectedLead(data.lead)
        setShowViewModal(true)
      }
    } catch (error) {
      console.error('Error fetching lead details:', error)
    }
  }

  const handleEditLead = async (leadId: string) => {
    try {
      const response = await fetch(`/api/admin/leads/${leadId}`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (response.ok) {
        const data = await response.json()
        setSelectedLead(data.lead)
        setEditFormData({
          schoolName: data.lead.schoolName,
          contactPerson: data.lead.contactPerson,
          email: data.lead.email,
          phone: data.lead.phone,
          location: data.lead.location,
          status: data.lead.status,
          source: data.lead.source,
          notes: data.lead.notes || ''
        })
        setShowEditModal(true)
      }
    } catch (error) {
      console.error('Error fetching lead details:', error)
    }
  }

  const handleSaveLead = async () => {
    if (!selectedLead) return

    setSaving(true)
    try {
      const response = await fetch(`/api/admin/leads/${selectedLead.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(editFormData)
      })

      if (response.ok) {
        setShowEditModal(false)
        fetchLeads() // Refresh the list
      }
    } catch (error) {
      console.error('Error updating lead:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleConvertToClient = async (leadId: string) => {
    try {
      const response = await fetch(`/api/admin/leads/${leadId}`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (response.ok) {
        const data = await response.json()
        setSelectedLead(data.lead)
        setShowConvertModal(true)
      }
    } catch (error) {
      console.error('Error fetching lead details:', error)
    }
  }

  const handleConfirmConvert = async () => {
    if (!selectedLead) return

    setSaving(true)
    try {
      const response = await fetch(`/api/admin/leads/${selectedLead.id}/convert`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        setShowConvertModal(false)
        fetchLeads() // Refresh the list
      }
    } catch (error) {
      console.error('Error converting lead to client:', error)
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-slate-900">Leads Management</h2>
        <Button className="flex items-center space-x-2">
          <Download className="w-4 h-4" />
          <span>Export</span>
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search leads..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Status</option>
              <option value="new">New</option>
              <option value="contacted">Contacted</option>
              <option value="qualified">Qualified</option>
              <option value="converted">Converted</option>
              <option value="lost">Lost</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Leads Table */}
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="p-8 text-center">
              <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
              <p className="mt-2 text-slate-600">Loading leads...</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-slate-50 border-b border-slate-200">
                  <tr>
                    <th className="text-left p-4 font-medium text-slate-900">School Name</th>
                    <th className="text-left p-4 font-medium text-slate-900">Contact</th>
                    <th className="text-left p-4 font-medium text-slate-900">Status</th>
                    <th className="text-left p-4 font-medium text-slate-900">Source</th>
                    <th className="text-left p-4 font-medium text-slate-900">Date</th>
                    <th className="text-left p-4 font-medium text-slate-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {leads.map((lead: any, index) => (
                    <tr key={index} className="border-b border-slate-100 hover:bg-slate-50">
                      <td className="p-4">
                        <div>
                          <p className="font-medium text-slate-900">{lead.schoolName}</p>
                          <p className="text-sm text-slate-500">{lead.location}</p>
                        </div>
                      </td>
                      <td className="p-4">
                        <div>
                          <p className="text-slate-900">{lead.contactPerson}</p>
                          <p className="text-sm text-slate-500">{lead.email}</p>
                          <p className="text-sm text-slate-500">{lead.phone}</p>
                        </div>
                      </td>
                      <td className="p-4">
                        <Badge variant="outline">{lead.status}</Badge>
                      </td>
                      <td className="p-4 text-slate-600">{lead.source}</td>
                      <td className="p-4 text-slate-600">
                        {new Date(lead.createdAt).toLocaleDateString()}
                      </td>
                      <td className="p-4">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewLead(lead.id)}
                            title="View Lead Details"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEditLead(lead.id)}
                            title="Edit Lead Information"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          {lead.status !== 'converted' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleConvertToClient(lead.id)}
                              title="Convert to Client"
                              className="text-green-600 hover:text-green-700"
                            >
                              <UserCheck className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {leads.length === 0 && (
                <div className="p-8 text-center text-slate-500">
                  No leads found
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* View Lead Modal */}
      {showViewModal && selectedLead && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-slate-900">Lead Details</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowViewModal(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h4 className="font-semibold text-slate-900 border-b pb-2">Lead Information</h4>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-slate-600">School Name</label>
                    <p className="text-slate-900">{selectedLead.schoolName}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Contact Person</label>
                    <p className="text-slate-900">{selectedLead.contactPerson}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Email</label>
                    <p className="text-slate-900">{selectedLead.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Phone</label>
                    <p className="text-slate-900">{selectedLead.phone}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Location</label>
                    <p className="text-slate-900">{selectedLead.location}</p>
                  </div>
                </div>
              </div>

              {/* Status & Source Information */}
              <div className="space-y-4">
                <h4 className="font-semibold text-slate-900 border-b pb-2">Status & Source</h4>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-slate-600">Status</label>
                    <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                      selectedLead.status === 'converted' ? 'bg-green-100 text-green-800' :
                      selectedLead.status === 'qualified' ? 'bg-blue-100 text-blue-800' :
                      selectedLead.status === 'contacted' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {selectedLead.status?.toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Source</label>
                    <p className="text-slate-900">{selectedLead.source}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Created At</label>
                    <p className="text-slate-900">{new Date(selectedLead.createdAt).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Last Updated</label>
                    <p className="text-slate-900">{new Date(selectedLead.updatedAt).toLocaleDateString()}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Notes Section */}
            {selectedLead.notes && (
              <div className="mt-6 space-y-4">
                <h4 className="font-semibold text-slate-900 border-b pb-2">Notes</h4>
                <div className="bg-slate-50 p-4 rounded-lg">
                  <p className="text-slate-700 whitespace-pre-wrap">{selectedLead.notes}</p>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 mt-6">
              {selectedLead.status !== 'converted' && (
                <Button
                  onClick={() => {
                    setShowViewModal(false)
                    handleConvertToClient(selectedLead.id)
                  }}
                  className="flex items-center space-x-2 bg-green-600 hover:bg-green-700"
                >
                  <UserCheck className="w-4 h-4" />
                  <span>Convert to Client</span>
                </Button>
              )}
              <Button
                onClick={() => {
                  setShowViewModal(false)
                  handleEditLead(selectedLead.id)
                }}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <Edit className="w-4 h-4" />
                <span>Edit Lead</span>
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Lead Modal */}
      {showEditModal && selectedLead && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-slate-900">Edit Lead Information</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowEditModal(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">School Name</label>
                  <input
                    type="text"
                    value={editFormData.schoolName || ''}
                    onChange={(e) => setEditFormData({...editFormData, schoolName: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Contact Person</label>
                  <input
                    type="text"
                    value={editFormData.contactPerson || ''}
                    onChange={(e) => setEditFormData({...editFormData, contactPerson: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Email</label>
                  <input
                    type="email"
                    value={editFormData.email || ''}
                    onChange={(e) => setEditFormData({...editFormData, email: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Phone</label>
                  <input
                    type="tel"
                    value={editFormData.phone || ''}
                    onChange={(e) => setEditFormData({...editFormData, phone: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Location</label>
                  <input
                    type="text"
                    value={editFormData.location || ''}
                    onChange={(e) => setEditFormData({...editFormData, location: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Status</label>
                  <select
                    value={editFormData.status || ''}
                    onChange={(e) => setEditFormData({...editFormData, status: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="new">New</option>
                    <option value="contacted">Contacted</option>
                    <option value="qualified">Qualified</option>
                    <option value="proposal_sent">Proposal Sent</option>
                    <option value="negotiation">Negotiation</option>
                    <option value="converted">Converted</option>
                    <option value="lost">Lost</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">Source</label>
                <select
                  value={editFormData.source || ''}
                  onChange={(e) => setEditFormData({...editFormData, source: e.target.value})}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="website">Website</option>
                  <option value="referral">Referral</option>
                  <option value="social_media">Social Media</option>
                  <option value="cold_call">Cold Call</option>
                  <option value="email_campaign">Email Campaign</option>
                  <option value="trade_show">Trade Show</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">Notes</label>
                <textarea
                  value={editFormData.notes || ''}
                  onChange={(e) => setEditFormData({...editFormData, notes: e.target.value})}
                  rows={4}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Add any additional notes about this lead..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowEditModal(false)}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSaveLead}
                disabled={saving}
                className="flex items-center space-x-2"
              >
                {saving && <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>}
                <span>{saving ? 'Saving...' : 'Save Changes'}</span>
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Convert to Client Modal */}
      {showConvertModal && selectedLead && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-slate-900">Convert to Client</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowConvertModal(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center space-x-3">
                  <UserCheck className="w-8 h-8 text-blue-600" />
                  <div>
                    <h4 className="font-medium text-blue-900">Convert Lead to Client</h4>
                    <p className="text-sm text-blue-700">
                      This will create a new client record and mark the lead as converted.
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <p className="text-sm text-slate-600"><strong>School:</strong> {selectedLead.schoolName}</p>
                <p className="text-sm text-slate-600"><strong>Contact:</strong> {selectedLead.contactPerson}</p>
                <p className="text-sm text-slate-600"><strong>Email:</strong> {selectedLead.email}</p>
                <p className="text-sm text-slate-600"><strong>Phone:</strong> {selectedLead.phone}</p>
              </div>

              <div className="bg-yellow-50 p-3 rounded-lg">
                <p className="text-sm text-yellow-800">
                  <strong>Note:</strong> This action cannot be undone. The lead will be converted to a client and moved to the clients section.
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowConvertModal(false)}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button
                onClick={handleConfirmConvert}
                disabled={saving}
                className="flex items-center space-x-2 bg-green-600 hover:bg-green-700"
              >
                {saving && <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>}
                <UserCheck className="w-4 h-4" />
                <span>{saving ? 'Converting...' : 'Convert to Client'}</span>
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Clients Management Component
function ClientsManagement({ adminToken }: { adminToken: string }) {
  const [clients, setClients] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [selectedClient, setSelectedClient] = useState<any>(null)
  const [showViewModal, setShowViewModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showBillingModal, setShowBillingModal] = useState(false)
  const [showCreateSubscriptionModal, setShowCreateSubscriptionModal] = useState(false)
  const [editFormData, setEditFormData] = useState<any>({})
  const [billingData, setBillingData] = useState<any>(null)
  const [subscriptionFormData, setSubscriptionFormData] = useState<any>({
    studentCount: '',
    monthlyAmount: '',
    dueDate: 15,
    gracePeriodDays: 3,
    setupFee: 0,
    discountPercentage: 0,
    notes: ''
  })

  // Invoice and Payment Management States
  const [showGenerateInvoiceModal, setShowGenerateInvoiceModal] = useState(false)
  const [showRecordPaymentModal, setShowRecordPaymentModal] = useState(false)
  const [invoiceFormData, setInvoiceFormData] = useState({
    subscriptionId: '',
    dueDate: ''
  })
  const [paymentFormData, setPaymentFormData] = useState({
    invoiceId: '',
    amount: '',
    paymentMethod: '',
    transactionId: '',
    notes: ''
  })
  const [saving, setSaving] = useState(false)
  const [loadingBilling, setLoadingBilling] = useState(false)

  useEffect(() => {
    fetchClients()
  }, [searchTerm, statusFilter])

  const fetchClients = async () => {
    try {
      const params = new URLSearchParams()
      if (searchTerm) params.append('search', searchTerm)
      if (statusFilter) params.append('status', statusFilter)
      params.append('include_subscription', 'true') // Include subscription data

      const response = await fetch(`/api/admin/clients?${params}`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (response.ok) {
        const data = await response.json()
        setClients(data.clients || [])
      }
    } catch (error) {
      console.error('Error fetching clients:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleViewClient = async (clientId: string) => {
    try {
      const response = await fetch(`/api/admin/clients/${clientId}`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (response.ok) {
        const data = await response.json()
        setSelectedClient(data.client)
        setShowViewModal(true)
      }
    } catch (error) {
      console.error('Error fetching client details:', error)
    }
  }

  const handleEditClient = async (clientId: string) => {
    try {
      const response = await fetch(`/api/admin/clients/${clientId}`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (response.ok) {
        const data = await response.json()
        setSelectedClient(data.client)
        setEditFormData({
          schoolName: data.client.schoolName,
          email: data.client.email,
          phone: data.client.phone,
          address: data.client.address,
          city: data.client.city,
          state: data.client.state,
          pincode: data.client.pincode,
          onboardingStatus: data.client.onboardingStatus
        })
        setShowEditModal(true)
      }
    } catch (error) {
      console.error('Error fetching client details:', error)
    }
  }

  const handleBillingManagement = async (clientId: string) => {
    try {
      setLoadingBilling(true)
      const response = await fetch(`/api/admin/clients/${clientId}/billing`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (response.ok) {
        const data = await response.json()
        setBillingData(data)
        setSelectedClient(data.client)
        setShowBillingModal(true)
      }
    } catch (error) {
      console.error('Error fetching billing details:', error)
    } finally {
      setLoadingBilling(false)
    }
  }

  // Submit Invoice Generation
  const handleSubmitInvoiceGeneration = async () => {
    try {
      const response = await fetch('/api/admin/invoices/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${adminToken}`
        },
        body: JSON.stringify(invoiceFormData)
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Invoice generated successfully:', result)
        setShowGenerateInvoiceModal(false)

        // Refresh billing data
        if (selectedClient?.id) {
          handleBillingManagement(selectedClient.id)
        }

        alert('Invoice generated successfully!')
      } else {
        const error = await response.json()
        console.error('Failed to generate invoice:', error)
        alert(`Failed to generate invoice: ${error.error || 'Unknown error'}`)
      }
    } catch (error) {
      console.error('Error generating invoice:', error)
      alert('Error generating invoice. Please try again.')
    }
  }

  // Submit Payment Recording
  const handleSubmitPaymentRecording = async () => {
    try {
      const response = await fetch('/api/admin/payments/record', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${adminToken}`
        },
        body: JSON.stringify({
          ...paymentFormData,
          amount: parseFloat(paymentFormData.amount)
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Payment recorded successfully:', result)
        setShowRecordPaymentModal(false)

        // Refresh billing data
        if (selectedClient?.id) {
          handleBillingManagement(selectedClient.id)
        }

        alert('Payment recorded successfully!')
      } else {
        const error = await response.json()
        console.error('Failed to record payment:', error)
        alert(`Failed to record payment: ${error.error || 'Unknown error'}`)
      }
    } catch (error) {
      console.error('Error recording payment:', error)
      alert('Error recording payment. Please try again.')
    }
  }

  const handleCreateSubscription = (clientId: string) => {
    const client: any = clients.find((c: any) => c.id === clientId)
    if (!client) return

    setSelectedClient(client)

    // Auto-close billing management modal if open
    setShowBillingModal(false)

    // Auto-populate form with school's entered data - prioritize averageMonthlyFee
    const feePerStudent = client.averageMonthlyFee ? parseFloat(client.averageMonthlyFee) :
                         (client.classFee ? parseFloat(client.classFee) : 0)
    const totalMonthlyAmount = feePerStudent * (client.actualStudentCount || 0)

    const autoPopulatedData = {
      studentCount: client.actualStudentCount?.toString() || '',
      monthlyAmount: totalMonthlyAmount > 0 ? totalMonthlyAmount.toString() : '',
      dueDate: 15,
      gracePeriodDays: 3,
      setupFee: 0,
      discountPercentage: 0,
      notes: feePerStudent > 0 ?
        `Auto-populated: ${client.actualStudentCount} students × ₹${feePerStudent.toLocaleString()} per student = ₹${totalMonthlyAmount.toLocaleString()}` :
        ''
    }

    setSubscriptionFormData(autoPopulatedData)
    setShowCreateSubscriptionModal(true)
  }

  // Invoice Generation Handler
  const handleGenerateInvoice = () => {
    if (!selectedClient || !billingData?.subscription) return

    setInvoiceFormData({
      subscriptionId: billingData.subscription.id,
      dueDate: ''
    })
    setShowGenerateInvoiceModal(true)
  }

  // Record Payment Handler
  const handleRecordPayment = () => {
    if (!billingData?.invoices || billingData.invoices.length === 0) return

    // Find the most recent unpaid invoice
    const unpaidInvoice = billingData.invoices.find((inv: any) => inv.status !== 'paid')

    setPaymentFormData({
      invoiceId: unpaidInvoice?.id || '',
      amount: unpaidInvoice?.totalAmount || '',
      paymentMethod: '',
      transactionId: '',
      notes: ''
    })
    setShowRecordPaymentModal(true)
  }

  const handleSubmitSubscription = async () => {
    if (!selectedClient) return

    try {
      setSaving(true)
      const response = await fetch(`/api/admin/subscriptions/create`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          clientId: selectedClient.id,
          studentCount: parseInt(subscriptionFormData.studentCount),
          monthlyAmount: parseFloat(subscriptionFormData.monthlyAmount),
          dueDate: subscriptionFormData.dueDate,
          gracePeriodDays: subscriptionFormData.gracePeriodDays,
          setupFee: parseFloat(subscriptionFormData.setupFee) || 0,
          discountPercentage: parseFloat(subscriptionFormData.discountPercentage) || 0,
          notes: subscriptionFormData.notes
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Subscription created successfully:', result)
        setShowCreateSubscriptionModal(false)
        fetchClients() // Refresh the clients list

        // Return to billing management view with refreshed data
        if (selectedClient?.id) {
          handleBillingManagement(selectedClient.id)
        }
      } else {
        const error = await response.json()
        console.error('Failed to create subscription:', error)
        alert(`Failed to create subscription: ${error.error || 'Unknown error'}`)
      }
    } catch (error) {
      console.error('Error creating subscription:', error)
      alert('Error creating subscription. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  const handleSaveClient = async () => {
    if (!selectedClient) return

    setSaving(true)
    try {
      const response = await fetch(`/api/admin/clients/${selectedClient.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(editFormData)
      })

      if (response.ok) {
        setShowEditModal(false)
        fetchClients() // Refresh the list
      }
    } catch (error) {
      console.error('Error updating client:', error)
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-slate-900">Clients Management</h2>
        <Button className="flex items-center space-x-2">
          <Download className="w-4 h-4" />
          <span>Export</span>
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search clients..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="suspended">Suspended</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Clients Table */}
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="p-8 text-center">
              <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
              <p className="mt-2 text-slate-600">Loading clients...</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-slate-50 border-b border-slate-200">
                  <tr>
                    <th className="text-left p-4 font-medium text-slate-900">School Details</th>
                    <th className="text-left p-4 font-medium text-slate-900">Contact</th>
                    <th className="text-left p-4 font-medium text-slate-900">Students & Fees</th>
                    <th className="text-left p-4 font-medium text-slate-900">Partner</th>
                    <th className="text-left p-4 font-medium text-slate-900">Subscription</th>
                    <th className="text-left p-4 font-medium text-slate-900">Status</th>
                    <th className="text-left p-4 font-medium text-slate-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {clients.map((client: any, index) => (
                    <tr key={index} className="border-b border-slate-100 hover:bg-slate-50">
                      <td className="p-4">
                        <div>
                          <p className="font-medium text-slate-900">{client.schoolName}</p>
                          <p className="text-sm text-slate-500">Code: {client.schoolCode}</p>
                          <p className="text-sm text-slate-500">{client.location}</p>
                        </div>
                      </td>
                      <td className="p-4">
                        <div>
                          <p className="text-slate-900">{client.contactPerson || client.principalName}</p>
                          <p className="text-sm text-slate-500">{client.email}</p>
                          <p className="text-sm text-slate-500">{client.phone}</p>
                        </div>
                      </td>
                      <td className="p-4">
                        <div>
                          <p className="font-medium text-slate-900">{client.actualStudentCount} students</p>
                          <p className="text-sm text-slate-600">
                            Monthly Fee: {
                              client.averageMonthlyFee
                                ? `₹${parseFloat(client.averageMonthlyFee).toLocaleString()}`
                                : client.classFee
                                  ? `₹${parseFloat(client.classFee).toLocaleString()}`
                                  : 'Not set'
                            }
                          </p>
                        </div>
                      </td>
                      <td className="p-4">
                        <div>
                          {client.partner ? (
                            <>
                              <p className="font-medium text-slate-900">{client.partner.name}</p>
                              <p className="text-sm text-slate-500">Code: {client.partner.code}</p>
                              <p className="text-xs text-slate-400">
                                Ref: {client.partner.referralCode} ({client.partner.referralSource})
                              </p>
                            </>
                          ) : (
                            <p className="text-sm text-slate-500">Direct Registration</p>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <div>
                          {client.subscription ? (
                            <>
                              <p className="font-medium text-slate-900">Active Subscription</p>
                              <Badge variant={client.subscription.status === 'active' ? 'default' : 'secondary'} className="mt-1">
                                {client.subscription.status}
                              </Badge>
                            </>
                          ) : (
                            <p className="text-sm text-slate-500">No active subscription</p>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <Badge variant="outline">{client.status}</Badge>
                          <Badge variant="outline" className="block">{client.onboardingStatus}</Badge>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewClient(client.id)}
                            title="View Client Details"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEditClient(client.id)}
                            title="Edit Client Information"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleBillingManagement(client.id)}
                            title="Manage Billing & Subscription"
                            className="bg-green-50 hover:bg-green-100 text-green-700 border-green-200"
                          >
                            ₹
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {clients.length === 0 && (
                <div className="p-8 text-center text-slate-500">
                  No clients found
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* View Client Modal */}
      {showViewModal && selectedClient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-slate-900">Client Details</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowViewModal(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h4 className="font-semibold text-slate-900 border-b pb-2">Basic Information</h4>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-slate-600">School Name</label>
                    <p className="text-slate-900">{selectedClient.schoolName}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">School Code</label>
                    <p className="text-slate-900">{selectedClient.schoolCode}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Email</label>
                    <p className="text-slate-900">{selectedClient.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Phone</label>
                    <p className="text-slate-900">{selectedClient.phone}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Onboarding Status</label>
                    <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                      selectedClient.onboardingStatus === 'completed' ? 'bg-green-100 text-green-800' :
                      selectedClient.onboardingStatus === 'in_progress' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {selectedClient.onboardingStatus?.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                </div>
              </div>

              {/* Address Information */}
              <div className="space-y-4">
                <h4 className="font-semibold text-slate-900 border-b pb-2">Address Information</h4>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-slate-600">Address</label>
                    <p className="text-slate-900">{selectedClient.address || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">City</label>
                    <p className="text-slate-900">{selectedClient.city || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">State</label>
                    <p className="text-slate-900">{selectedClient.state || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Pincode</label>
                    <p className="text-slate-900">{selectedClient.pincode || 'Not provided'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Created At</label>
                    <p className="text-slate-900">{new Date(selectedClient.createdAt).toLocaleDateString()}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Subscription Information */}
            {selectedClient.subscription && (
              <div className="mt-6 space-y-4">
                <h4 className="font-semibold text-slate-900 border-b pb-2">Subscription Details</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-slate-600">Status</label>
                    <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                      selectedClient.subscription.status === 'active' ? 'bg-green-100 text-green-800' :
                      selectedClient.subscription.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {selectedClient.subscription.status.toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Auto Renew</label>
                    <p className="text-slate-900">{selectedClient.subscription.autoRenew ? 'Yes' : 'No'}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Users Information */}
            {selectedClient.users && selectedClient.users.length > 0 && (
              <div className="mt-6 space-y-4">
                <h4 className="font-semibold text-slate-900 border-b pb-2">Associated Users</h4>
                <div className="space-y-2">
                  {selectedClient.users.map((user: any, index: number) => (
                    <div key={index} className="flex justify-between items-center p-3 bg-slate-50 rounded-lg">
                      <div>
                        <p className="font-medium text-slate-900">{user.name}</p>
                        <p className="text-sm text-slate-600">{user.email}</p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        user.role === 'admin' ? 'bg-purple-100 text-purple-800' :
                        user.role === 'teacher' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {user.role.toUpperCase()}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Create Subscription Modal */}
      {showCreateSubscriptionModal && selectedClient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-slate-900">Create Subscription - {selectedClient.schoolName}</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCreateSubscriptionModal(false)}
              >
                ✕
              </Button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">
                    Student Count *
                    {selectedClient?.actualStudentCount && (
                      <span className="ml-2 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                        Auto-filled from school profile
                      </span>
                    )}
                  </label>
                  <input
                    type="number"
                    min="1"
                    max="10000"
                    value={subscriptionFormData.studentCount}
                    onChange={(e) => setSubscriptionFormData({
                      ...subscriptionFormData,
                      studentCount: e.target.value
                    })}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      selectedClient?.actualStudentCount ? 'border-blue-300 bg-blue-50' : 'border-slate-300'
                    }`}
                    placeholder="Enter student count"
                  />
                  {selectedClient?.actualStudentCount && (
                    <p className="text-xs text-blue-600 mt-1">
                      Pre-filled with {selectedClient.actualStudentCount} students from school profile
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">
                    Monthly Amount (₹) *
                    {(selectedClient?.averageMonthlyFee || selectedClient?.classFee) && (
                      <span className="ml-2 text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
                        Auto-calculated from {selectedClient?.averageMonthlyFee ? 'average monthly fee' : 'class fee'}
                      </span>
                    )}
                  </label>
                  <input
                    type="number"
                    min="100"
                    max="1000000"
                    step="0.01"
                    value={subscriptionFormData.monthlyAmount}
                    onChange={(e) => setSubscriptionFormData({
                      ...subscriptionFormData,
                      monthlyAmount: e.target.value
                    })}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      (selectedClient?.averageMonthlyFee || selectedClient?.classFee) ? 'border-green-300 bg-green-50' : 'border-slate-300'
                    }`}
                    placeholder="Enter monthly amount"
                  />
                  {(selectedClient?.averageMonthlyFee || selectedClient?.classFee) && (
                    <p className="text-xs text-green-600 mt-1">
                      Calculated: {selectedClient.actualStudentCount} students × ₹{
                        selectedClient?.averageMonthlyFee
                          ? parseFloat(selectedClient.averageMonthlyFee).toLocaleString()
                          : parseFloat(selectedClient.classFee).toLocaleString()
                      } = ₹{(
                        (selectedClient?.averageMonthlyFee
                          ? parseFloat(selectedClient.averageMonthlyFee)
                          : parseFloat(selectedClient.classFee)
                        ) * (selectedClient.actualStudentCount || 0)
                      ).toLocaleString()}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">
                    Due Date (Day of Month)
                  </label>
                  <select
                    value={subscriptionFormData.dueDate}
                    onChange={(e) => setSubscriptionFormData({
                      ...subscriptionFormData,
                      dueDate: parseInt(e.target.value)
                    })}
                    className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {Array.from({ length: 31 }, (_, i) => i + 1).map(day => (
                      <option key={day} value={day}>{day}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">
                    Grace Period (Days)
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="30"
                    value={subscriptionFormData.gracePeriodDays}
                    onChange={(e) => setSubscriptionFormData({
                      ...subscriptionFormData,
                      gracePeriodDays: parseInt(e.target.value)
                    })}
                    className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">
                    Setup Fee (₹)
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={subscriptionFormData.setupFee}
                    onChange={(e) => setSubscriptionFormData({
                      ...subscriptionFormData,
                      setupFee: e.target.value
                    })}
                    className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="0.00"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">
                    Discount (%)
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    step="0.01"
                    value={subscriptionFormData.discountPercentage}
                    onChange={(e) => setSubscriptionFormData({
                      ...subscriptionFormData,
                      discountPercentage: e.target.value
                    })}
                    className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="0.00"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  Notes (Optional)
                </label>
                <textarea
                  value={subscriptionFormData.notes}
                  onChange={(e) => setSubscriptionFormData({
                    ...subscriptionFormData,
                    notes: e.target.value
                  })}
                  className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows={3}
                  placeholder="Add any notes about this subscription..."
                />
              </div>

              <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Note:</strong> Creating a subscription will automatically generate the first billing cycle and set up automated monthly billing.
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowCreateSubscriptionModal(false)}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmitSubscription}
                disabled={saving || !subscriptionFormData.studentCount || !subscriptionFormData.monthlyAmount}
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700"
              >
                {saving && <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>}
                <span>{saving ? 'Creating...' : 'Create Subscription'}</span>
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Generate Invoice Modal */}
      {showGenerateInvoiceModal && selectedClient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-slate-900">Generate Invoice</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowGenerateInvoiceModal(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  Client
                </label>
                <input
                  type="text"
                  value={selectedClient.schoolName}
                  disabled
                  className="w-full px-3 py-2 border border-slate-300 rounded-md bg-slate-50"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  Subscription ID
                </label>
                <input
                  type="text"
                  value={invoiceFormData.subscriptionId}
                  disabled
                  className="w-full px-3 py-2 border border-slate-300 rounded-md bg-slate-50"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  Due Date (Optional)
                </label>
                <input
                  type="date"
                  value={invoiceFormData.dueDate}
                  onChange={(e) => setInvoiceFormData({
                    ...invoiceFormData,
                    dueDate: e.target.value
                  })}
                  className="w-full px-3 py-2 border border-slate-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <p className="text-xs text-slate-500 mt-1">
                  Leave empty to use default due date (15 days from issue)
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowGenerateInvoiceModal(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmitInvoiceGeneration}
                className="bg-green-600 hover:bg-green-700"
              >
                Generate Invoice
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Record Payment Modal */}
      {showRecordPaymentModal && selectedClient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-slate-900">Record Payment</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowRecordPaymentModal(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  Client
                </label>
                <input
                  type="text"
                  value={selectedClient.schoolName}
                  disabled
                  className="w-full px-3 py-2 border border-slate-300 rounded-md bg-slate-50"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  Invoice ID
                </label>
                <select
                  value={paymentFormData.invoiceId}
                  onChange={(e) => {
                    const selectedInvoice = billingData?.invoices?.find((inv: any) => inv.id === e.target.value)
                    setPaymentFormData({
                      ...paymentFormData,
                      invoiceId: e.target.value,
                      amount: selectedInvoice?.totalAmount || ''
                    })
                  }}
                  className="w-full px-3 py-2 border border-slate-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select Invoice</option>
                  {billingData?.invoices?.filter((inv: any) => inv.status !== 'paid').map((invoice: any) => (
                    <option key={invoice.id} value={invoice.id}>
                      {invoice.invoiceNumber} - ₹{invoice.totalAmount}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  Amount *
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={paymentFormData.amount}
                  onChange={(e) => setPaymentFormData({
                    ...paymentFormData,
                    amount: e.target.value
                  })}
                  className="w-full px-3 py-2 border border-slate-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  Payment Method *
                </label>
                <select
                  value={paymentFormData.paymentMethod}
                  onChange={(e) => setPaymentFormData({
                    ...paymentFormData,
                    paymentMethod: e.target.value
                  })}
                  className="w-full px-3 py-2 border border-slate-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="">Select Payment Method</option>
                  <option value="bank_transfer">Bank Transfer</option>
                  <option value="cash">Cash</option>
                  <option value="cheque">Cheque</option>
                  <option value="upi">UPI</option>
                  <option value="card">Card</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  Transaction ID
                </label>
                <input
                  type="text"
                  value={paymentFormData.transactionId}
                  onChange={(e) => setPaymentFormData({
                    ...paymentFormData,
                    transactionId: e.target.value
                  })}
                  className="w-full px-3 py-2 border border-slate-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Reference number or transaction ID"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">
                  Notes
                </label>
                <textarea
                  value={paymentFormData.notes}
                  onChange={(e) => setPaymentFormData({
                    ...paymentFormData,
                    notes: e.target.value
                  })}
                  className="w-full px-3 py-2 border border-slate-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                  placeholder="Additional notes about the payment"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowRecordPaymentModal(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmitPaymentRecording}
                className="bg-blue-600 hover:bg-blue-700"
                disabled={!paymentFormData.invoiceId || !paymentFormData.amount || !paymentFormData.paymentMethod}
              >
                Record Payment
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Client Modal */}
      {showEditModal && selectedClient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-slate-900">Edit Client Information</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowEditModal(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">School Name</label>
                  <input
                    type="text"
                    value={editFormData.schoolName || ''}
                    onChange={(e) => setEditFormData({...editFormData, schoolName: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Email</label>
                  <input
                    type="email"
                    value={editFormData.email || ''}
                    onChange={(e) => setEditFormData({...editFormData, email: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Phone</label>
                  <input
                    type="tel"
                    value={editFormData.phone || ''}
                    onChange={(e) => setEditFormData({...editFormData, phone: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Onboarding Status</label>
                  <select
                    value={editFormData.onboardingStatus || ''}
                    onChange={(e) => setEditFormData({...editFormData, onboardingStatus: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="pending">Pending</option>
                    <option value="in_progress">In Progress</option>
                    <option value="completed">Completed</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">Address</label>
                <textarea
                  value={editFormData.address || ''}
                  onChange={(e) => setEditFormData({...editFormData, address: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">City</label>
                  <input
                    type="text"
                    value={editFormData.city || ''}
                    onChange={(e) => setEditFormData({...editFormData, city: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">State</label>
                  <input
                    type="text"
                    value={editFormData.state || ''}
                    onChange={(e) => setEditFormData({...editFormData, state: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Pincode</label>
                  <input
                    type="text"
                    value={editFormData.pincode || ''}
                    onChange={(e) => setEditFormData({...editFormData, pincode: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowEditModal(false)}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSaveClient}
                disabled={saving}
                className="flex items-center space-x-2"
              >
                {saving && <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>}
                <span>{saving ? 'Saving...' : 'Save Changes'}</span>
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Billing Management Modal */}
      {showBillingModal && selectedClient && billingData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-slate-900">Billing Management - {selectedClient.schoolName}</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowBillingModal(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Subscription Details */}
              <div className="lg:col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Subscription Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {billingData.subscription ? (
                      <>
                        <div>
                          <label className="text-sm font-medium text-slate-600">Status</label>
                          <Badge variant={billingData.subscription.status === 'active' ? 'default' : 'secondary'}>
                            {billingData.subscription.status}
                          </Badge>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-slate-600">Start Date</label>
                          <p className="text-slate-900">{new Date(billingData.subscription.startDate).toLocaleDateString()}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-slate-600">Next Billing</label>
                          <p className="text-slate-900">{new Date(billingData.subscription.nextBillingDate).toLocaleDateString()}</p>
                        </div>
                        <div className="pt-4 border-t">
                          <Button
                            size="sm"
                            className="w-full"
                            onClick={() => {/* TODO: Implement edit subscription */}}
                          >
                            Manage Subscription
                          </Button>
                        </div>
                      </>
                    ) : (
                      <div className="text-center py-8">
                        <p className="text-slate-500 mb-4">No active subscription</p>
                        <Button
                          size="sm"
                          onClick={() => handleCreateSubscription(selectedClient.id)}
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                        >
                          Create Subscription
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Financial Summary */}
                <Card className="mt-4">
                  <CardHeader>
                    <CardTitle className="text-lg">Financial Summary</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-slate-600">Total Paid</span>
                      <span className="font-medium text-green-600">₹{billingData.summary.totalPaid}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-600">Outstanding</span>
                      <span className="font-medium text-red-600">₹{billingData.summary.totalOutstanding}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-600">Last Payment</span>
                      <span className="text-sm text-slate-500">
                        {billingData.summary.lastPaymentDate
                          ? new Date(billingData.summary.lastPaymentDate).toLocaleDateString()
                          : 'No payments'
                        }
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Recent Invoices */}
              <div className="lg:col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Recent Invoices</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3 max-h-96 overflow-y-auto">
                      {billingData.invoices.map((invoice: any, index: number) => (
                        <div key={index} className="p-3 border rounded-lg">
                          <div className="flex justify-between items-start">
                            <div>
                              <p className="font-medium text-slate-900">{invoice.invoiceNumber}</p>
                              <p className="text-sm text-slate-500">Due: {new Date(invoice.dueDate).toLocaleDateString()}</p>
                            </div>
                            <div className="text-right">
                              <p className="font-medium">₹{invoice.totalAmount}</p>
                              <Badge variant={
                                invoice.status === 'paid' ? 'default' :
                                invoice.status === 'overdue' ? 'destructive' : 'secondary'
                              }>
                                {invoice.status}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      ))}
                      {billingData.invoices.length === 0 && (
                        <p className="text-center text-slate-500 py-8">No invoices found</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Recent Payments */}
              <div className="lg:col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Recent Payments</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3 max-h-96 overflow-y-auto">
                      {billingData.payments.map((payment: any, index: number) => (
                        <div key={index} className="p-3 border rounded-lg">
                          <div className="flex justify-between items-start">
                            <div>
                              <p className="font-medium text-slate-900">₹{payment.amount}</p>
                              <p className="text-sm text-slate-500">
                                {payment.processedAt ? new Date(payment.processedAt).toLocaleDateString() : 'Pending'}
                              </p>
                            </div>
                            <Badge variant={
                              payment.status === 'success' ? 'default' :
                              payment.status === 'failed' ? 'destructive' : 'secondary'
                            }>
                              {payment.status}
                            </Badge>
                          </div>
                          {payment.paymentMethod && (
                            <p className="text-xs text-slate-400 mt-1">{payment.paymentMethod}</p>
                          )}
                        </div>
                      ))}
                      {billingData.payments.length === 0 && (
                        <p className="text-center text-slate-500 py-8">No payments found</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 mt-6 pt-6 border-t">
              <Button variant="outline" onClick={() => setShowBillingModal(false)}>
                Close
              </Button>
              <Button
                className="bg-green-600 hover:bg-green-700"
                onClick={handleGenerateInvoice}
                disabled={!billingData?.subscription}
              >
                Generate Invoice
              </Button>
              <Button
                className="bg-blue-600 hover:bg-blue-700"
                onClick={handleRecordPayment}
                disabled={!billingData?.invoices || billingData.invoices.length === 0}
              >
                Record Payment
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Software Requests Management Component
function RequestsManagement({ adminToken }: { adminToken: string }) {
  const [requests, setRequests] = useState([])
  const [loading, setLoading] = useState(true)
  const [statusFilter, setStatusFilter] = useState('')
  const [typeFilter, setTypeFilter] = useState('')
  const [selectedRequest, setSelectedRequest] = useState<any>(null)
  const [showViewModal, setShowViewModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showApprovalModal, setShowApprovalModal] = useState(false)
  const [showUpgradeModal, setShowUpgradeModal] = useState(false)
  const [editFormData, setEditFormData] = useState<any>({})
  const [approvalData, setApprovalData] = useState<any>({})
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    fetchRequests()
  }, [statusFilter, typeFilter])

  const fetchRequests = async () => {
    try {
      const params = new URLSearchParams()
      if (statusFilter) params.append('status', statusFilter)
      if (typeFilter) params.append('type', typeFilter)

      const response = await fetch(`/api/admin/software-requests?${params}`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (response.ok) {
        const data = await response.json()
        setRequests(data.requests || [])
      }
    } catch (error) {
      console.error('Error fetching requests:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateRequestStatus = async (requestId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/admin/software-requests/${requestId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
      })

      if (response.ok) {
        fetchRequests() // Refresh the list
      }
    } catch (error) {
      console.error('Error updating request status:', error)
    }
  }

  const handleViewRequest = async (requestId: string) => {
    try {
      const response = await fetch(`/api/admin/software-requests/${requestId}`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (response.ok) {
        const data = await response.json()
        setSelectedRequest(data.request)
        setShowViewModal(true)
      }
    } catch (error) {
      console.error('Error fetching request details:', error)
    }
  }

  const handleEditRequest = async (requestId: string) => {
    try {
      const response = await fetch(`/api/admin/software-requests/${requestId}`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (response.ok) {
        const data = await response.json()
        setSelectedRequest(data.request)
        setEditFormData({
          schoolName: data.request.schoolName,
          email: data.request.email,
          phone: data.request.phone,
          requestType: data.request.requestType,
          status: data.request.status,
          adminNotes: data.request.adminNotes || ''
        })
        setShowEditModal(true)
      }
    } catch (error) {
      console.error('Error fetching request details:', error)
    }
  }

  const handleSaveRequest = async () => {
    if (!selectedRequest) return

    setSaving(true)
    try {
      const response = await fetch(`/api/admin/software-requests/${selectedRequest.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(editFormData)
      })

      if (response.ok) {
        setShowEditModal(false)
        fetchRequests() // Refresh the list
      }
    } catch (error) {
      console.error('Error updating request:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleApproveRequest = async (requestId: string) => {
    try {
      const response = await fetch(`/api/admin/software-requests/${requestId}`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (response.ok) {
        const data = await response.json()
        setSelectedRequest(data.request)
        setApprovalData({
          monthlyAmount: '',
          setupFee: '',
          billingCycle: 'monthly',
          autoRenew: true,
          notes: ''
        })
        setShowApprovalModal(true)
      }
    } catch (error) {
      console.error('Error fetching request details:', error)
    }
  }

  const handleConfirmApproval = async () => {
    if (!selectedRequest) return

    setSaving(true)
    try {
      const response = await fetch(`/api/admin/software-requests/${selectedRequest.id}/approve`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(approvalData)
      })

      if (response.ok) {
        setShowApprovalModal(false)
        fetchRequests() // Refresh the list
      }
    } catch (error) {
      console.error('Error approving request:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleUpgradeRequest = async (requestId: string) => {
    try {
      const response = await fetch(`/api/admin/software-requests/${requestId}`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (response.ok) {
        const data = await response.json()
        setSelectedRequest(data.request)
        setShowUpgradeModal(true)
      }
    } catch (error) {
      console.error('Error fetching request details:', error)
    }
  }

  const handleConfirmUpgrade = async () => {
    if (!selectedRequest) return

    setSaving(true)
    try {
      const response = await fetch(`/api/admin/software-requests/${selectedRequest.id}/upgrade`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        setShowUpgradeModal(false)
        fetchRequests() // Refresh the list
      }
    } catch (error) {
      console.error('Error upgrading request:', error)
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-slate-900">Software Requests Management</h2>
        <Button className="flex items-center space-x-2">
          <Download className="w-4 h-4" />
          <span>Export</span>
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex space-x-4">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="under_review">Under Review</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
              <option value="setup_in_progress">Setup in Progress</option>
              <option value="activated">Activated</option>
            </select>
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Types</option>
              <option value="demo">Demo</option>
              <option value="production">Production</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Requests Table */}
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="p-8 text-center">
              <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
              <p className="mt-2 text-slate-600">Loading requests...</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-slate-50 border-b border-slate-200">
                  <tr>
                    <th className="text-left p-4 font-medium text-slate-900">School Details</th>
                    <th className="text-left p-4 font-medium text-slate-900">Request Type</th>
                    <th className="text-left p-4 font-medium text-slate-900">Fee Structure</th>
                    <th className="text-left p-4 font-medium text-slate-900">Status</th>
                    <th className="text-left p-4 font-medium text-slate-900">Date</th>
                    <th className="text-left p-4 font-medium text-slate-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {requests.map((request: any, index) => (
                    <tr key={index} className="border-b border-slate-100 hover:bg-slate-50">
                      <td className="p-4">
                        <div>
                          <p className="font-medium text-slate-900">{request.schoolName}</p>
                          <p className="text-sm text-slate-500">{request.email}</p>
                        </div>
                      </td>
                      <td className="p-4">
                        <Badge variant={request.requestType === 'production' ? 'default' : 'secondary'}>
                          {request.requestType}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <div>
                          {request.averageMonthlyFee ? (
                            <>
                              <p className="font-medium text-slate-900">₹{parseFloat(request.averageMonthlyFee).toLocaleString()}</p>
                              <p className="text-xs text-slate-500">per student/month</p>
                            </>
                          ) : request.requestType === 'production' ? (
                            <p className="text-sm text-amber-600">Fee pending</p>
                          ) : (
                            <p className="text-sm text-slate-400">Demo request</p>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <select
                          value={request.status}
                          onChange={(e) => updateRequestStatus(request.id, e.target.value)}
                          className="px-2 py-1 border border-slate-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="pending">Pending</option>
                          <option value="under_review">Under Review</option>
                          <option value="approved">Approved</option>
                          <option value="rejected">Rejected</option>
                          <option value="setup_in_progress">Setup in Progress</option>
                          <option value="activated">Activated</option>
                        </select>
                      </td>
                      <td className="p-4 text-slate-600">
                        {new Date(request.createdAt).toLocaleDateString()}
                      </td>
                      <td className="p-4">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewRequest(request.id)}
                            title="View Request Details"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEditRequest(request.id)}
                            title="Edit Request Information"
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          {request.status === 'pending' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleApproveRequest(request.id)}
                              title="Approve & Set Subscription"
                              className="text-green-600 hover:text-green-700"
                            >
                              <CheckCircle className="w-4 h-4" />
                            </Button>
                          )}
                          {request.requestType === 'demo' && request.status === 'activated' && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleUpgradeRequest(request.id)}
                              title="Upgrade to Production"
                              className="text-blue-600 hover:text-blue-700"
                            >
                              <ArrowUp className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {requests.length === 0 && (
                <div className="p-8 text-center text-slate-500">
                  No software requests found
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* View Request Modal */}
      {showViewModal && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-slate-900">Software Request Details</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowViewModal(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* School Information */}
              <div className="space-y-4">
                <h4 className="font-semibold text-slate-900 border-b pb-2">School Information</h4>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-slate-600">School Name</label>
                    <p className="text-slate-900">{selectedRequest.schoolName}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Email</label>
                    <p className="text-slate-900">{selectedRequest.email}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Phone</label>
                    <p className="text-slate-900">{selectedRequest.phone}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Address</label>
                    <p className="text-slate-900">{selectedRequest.address || 'Not provided'}</p>
                  </div>
                </div>
              </div>

              {/* Request Information */}
              <div className="space-y-4">
                <h4 className="font-semibold text-slate-900 border-b pb-2">Request Details</h4>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-slate-600">Request Type</label>
                    <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                      selectedRequest.requestType === 'production' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {selectedRequest.requestType?.toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Average Monthly Fee</label>
                    {selectedRequest.averageMonthlyFee ? (
                      <p className="text-slate-900 font-semibold">₹{parseFloat(selectedRequest.averageMonthlyFee).toLocaleString()} per student</p>
                    ) : selectedRequest.requestType === 'production' ? (
                      <p className="text-amber-600">Fee information pending</p>
                    ) : (
                      <p className="text-slate-500">Not applicable for demo requests</p>
                    )}
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Student Count</label>
                    <p className="text-slate-900">{selectedRequest.studentCount || 'Not specified'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Faculty Count</label>
                    <p className="text-slate-900">{selectedRequest.facultyCount || 'Not specified'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Status</label>
                    <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                      selectedRequest.status === 'approved' ? 'bg-green-100 text-green-800' :
                      selectedRequest.status === 'rejected' ? 'bg-red-100 text-red-800' :
                      selectedRequest.status === 'activated' ? 'bg-blue-100 text-blue-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {selectedRequest.status?.toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Created At</label>
                    <p className="text-slate-900">{new Date(selectedRequest.createdAt).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Last Updated</label>
                    <p className="text-slate-900">{new Date(selectedRequest.updatedAt).toLocaleDateString()}</p>
                  </div>
                  {selectedRequest.partnerId && (
                    <div>
                      <label className="text-sm font-medium text-slate-600">Referred By Partner</label>
                      <p className="text-slate-900">{selectedRequest.partnerName || selectedRequest.partnerId}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Revenue Calculation */}
            {selectedRequest.averageMonthlyFee && selectedRequest.studentCount && selectedRequest.requestType === 'production' && (
              <div className="mt-6 space-y-4">
                <h4 className="font-semibold text-slate-900 border-b pb-2">Revenue Calculation</h4>
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="text-sm font-medium text-blue-700">Fee per Student</label>
                      <p className="text-blue-900 font-semibold">₹{parseFloat(selectedRequest.averageMonthlyFee).toLocaleString()}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-blue-700">Total Students</label>
                      <p className="text-blue-900 font-semibold">{selectedRequest.studentCount}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-blue-700">Estimated Monthly Revenue</label>
                      <p className="text-blue-900 font-bold text-lg">₹{(parseFloat(selectedRequest.averageMonthlyFee) * selectedRequest.studentCount).toLocaleString()}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Subscription Information */}
            {selectedRequest.subscription && (
              <div className="mt-6 space-y-4">
                <h4 className="font-semibold text-slate-900 border-b pb-2">Subscription Details</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 bg-slate-50 p-4 rounded-lg">
                  <div>
                    <label className="text-sm font-medium text-slate-600">Monthly Amount</label>
                    <p className="text-slate-900 font-semibold">₹{selectedRequest.subscription.monthlyAmount}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Setup Fee</label>
                    <p className="text-slate-900">₹{selectedRequest.subscription.setupFee || 0}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600">Billing Cycle</label>
                    <p className="text-slate-900">{selectedRequest.subscription.billingCycle}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Admin Notes */}
            {selectedRequest.adminNotes && (
              <div className="mt-6 space-y-4">
                <h4 className="font-semibold text-slate-900 border-b pb-2">Admin Notes</h4>
                <div className="bg-slate-50 p-4 rounded-lg">
                  <p className="text-slate-700 whitespace-pre-wrap">{selectedRequest.adminNotes}</p>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 mt-6">
              {selectedRequest.status === 'pending' && (
                <Button
                  onClick={() => {
                    setShowViewModal(false)
                    handleApproveRequest(selectedRequest.id)
                  }}
                  className="flex items-center space-x-2 bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle className="w-4 h-4" />
                  <span>Approve & Set Subscription</span>
                </Button>
              )}
              {selectedRequest.requestType === 'demo' && selectedRequest.status === 'activated' && (
                <Button
                  onClick={() => {
                    setShowViewModal(false)
                    handleUpgradeRequest(selectedRequest.id)
                  }}
                  className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700"
                >
                  <ArrowUp className="w-4 h-4" />
                  <span>Upgrade to Production</span>
                </Button>
              )}
              <Button
                onClick={() => {
                  setShowViewModal(false)
                  handleEditRequest(selectedRequest.id)
                }}
                variant="outline"
                className="flex items-center space-x-2"
              >
                <Edit className="w-4 h-4" />
                <span>Edit Request</span>
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Request Modal */}
      {showEditModal && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-slate-900">Edit Software Request</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowEditModal(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">School Name</label>
                  <input
                    type="text"
                    value={editFormData.schoolName || ''}
                    onChange={(e) => setEditFormData({...editFormData, schoolName: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Email</label>
                  <input
                    type="email"
                    value={editFormData.email || ''}
                    onChange={(e) => setEditFormData({...editFormData, email: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Phone</label>
                  <input
                    type="tel"
                    value={editFormData.phone || ''}
                    onChange={(e) => setEditFormData({...editFormData, phone: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Request Type</label>
                  <select
                    value={editFormData.requestType || ''}
                    onChange={(e) => setEditFormData({...editFormData, requestType: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="demo">Demo</option>
                    <option value="production">Production</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Status</label>
                  <select
                    value={editFormData.status || ''}
                    onChange={(e) => setEditFormData({...editFormData, status: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="pending">Pending</option>
                    <option value="under_review">Under Review</option>
                    <option value="approved">Approved</option>
                    <option value="rejected">Rejected</option>
                    <option value="setup_in_progress">Setup in Progress</option>
                    <option value="activated">Activated</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">Admin Notes</label>
                <textarea
                  value={editFormData.adminNotes || ''}
                  onChange={(e) => setEditFormData({...editFormData, adminNotes: e.target.value})}
                  rows={4}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Add internal notes about this request..."
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowEditModal(false)}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSaveRequest}
                disabled={saving}
                className="flex items-center space-x-2"
              >
                {saving && <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>}
                <span>{saving ? 'Saving...' : 'Save Changes'}</span>
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Approval Modal */}
      {showApprovalModal && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-slate-900">Approve Request & Set Subscription</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowApprovalModal(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            <div className="space-y-4">
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                  <div>
                    <h4 className="font-medium text-green-900">Approve Software Request</h4>
                    <p className="text-sm text-green-700">
                      Set subscription details for {selectedRequest.schoolName}
                    </p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Monthly Amount (₹)</label>
                  <input
                    type="number"
                    value={approvalData.monthlyAmount || ''}
                    onChange={(e) => setApprovalData({...approvalData, monthlyAmount: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter monthly subscription amount"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Setup Fee (₹)</label>
                  <input
                    type="number"
                    value={approvalData.setupFee || ''}
                    onChange={(e) => setApprovalData({...approvalData, setupFee: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter one-time setup fee"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-1">Billing Cycle</label>
                  <select
                    value={approvalData.billingCycle || 'monthly'}
                    onChange={(e) => setApprovalData({...approvalData, billingCycle: e.target.value})}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="monthly">Monthly</option>
                    <option value="quarterly">Quarterly</option>
                    <option value="yearly">Yearly</option>
                  </select>
                </div>
                <div className="flex items-center space-x-2 pt-6">
                  <input
                    type="checkbox"
                    id="autoRenew"
                    checked={approvalData.autoRenew || false}
                    onChange={(e) => setApprovalData({...approvalData, autoRenew: e.target.checked})}
                    className="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="autoRenew" className="text-sm font-medium text-slate-700">
                    Auto Renew Subscription
                  </label>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-1">Approval Notes</label>
                <textarea
                  value={approvalData.notes || ''}
                  onChange={(e) => setApprovalData({...approvalData, notes: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Add notes about the approval..."
                />
              </div>

              <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Note:</strong> Approving this request will create a subscription and billing cycle for the school.
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowApprovalModal(false)}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button
                onClick={handleConfirmApproval}
                disabled={saving || !approvalData.monthlyAmount}
                className="flex items-center space-x-2 bg-green-600 hover:bg-green-700"
              >
                {saving && <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>}
                <CheckCircle className="w-4 h-4" />
                <span>{saving ? 'Approving...' : 'Approve & Create Subscription'}</span>
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Upgrade Modal */}
      {showUpgradeModal && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold text-slate-900">Upgrade to Production</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowUpgradeModal(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center space-x-3">
                  <ArrowUp className="w-8 h-8 text-blue-600" />
                  <div>
                    <h4 className="font-medium text-blue-900">Upgrade Demo to Production</h4>
                    <p className="text-sm text-blue-700">
                      Convert this demo request to a full production system.
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <p className="text-sm text-slate-600"><strong>School:</strong> {selectedRequest.schoolName}</p>
                <p className="text-sm text-slate-600"><strong>Current Type:</strong> Demo</p>
                <p className="text-sm text-slate-600"><strong>Status:</strong> {selectedRequest.status}</p>
              </div>

              <div className="bg-yellow-50 p-3 rounded-lg">
                <p className="text-sm text-yellow-800">
                  <strong>Note:</strong> This will change the request type to "Production" and may require setting up new subscription terms.
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowUpgradeModal(false)}
                disabled={saving}
              >
                Cancel
              </Button>
              <Button
                onClick={handleConfirmUpgrade}
                disabled={saving}
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700"
              >
                {saving && <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>}
                <ArrowUp className="w-4 h-4" />
                <span>{saving ? 'Upgrading...' : 'Upgrade to Production'}</span>
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Partners Management Component
function PartnersManagement({ adminToken }: { adminToken: string }) {
  const [partners, setPartners] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [selectedPartner, setSelectedPartner] = useState<any>(null)
  const [showSchoolsModal, setShowSchoolsModal] = useState(false)
  const [partnerSchools, setPartnerSchools] = useState<any[]>([])

  useEffect(() => {
    fetchPartners()
  }, [searchTerm, statusFilter])

  const fetchPartners = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (searchTerm) params.append('search', searchTerm)
      if (statusFilter !== 'all') params.append('status', statusFilter)

      const response = await fetch(`/api/admin/partners?${params}`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (response.ok) {
        const data = await response.json()
        setPartners(data.partners)
      }
    } catch (error) {
      console.error('Error fetching partners:', error)
    } finally {
      setLoading(false)
    }
  }

  const viewPartnerSchools = async (partnerId: string) => {
    try {
      const response = await fetch(`/api/admin/partners/${partnerId}/schools`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (response.ok) {
        const data = await response.json()
        setPartnerSchools(data.schools)
        setShowSchoolsModal(true)
      }
    } catch (error) {
      console.error('Error fetching partner schools:', error)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-slate-900">Partners Management</h2>
          <p className="text-slate-600">Manage partner accounts and referral system</p>
        </div>
        <Button
          onClick={() => setShowCreateModal(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Partner
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search partners..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Partners Table */}
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
              <p className="mt-2 text-slate-600">Loading partners...</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-slate-50 border-b border-slate-200">
                  <tr>
                    <th className="text-left p-4 font-medium text-slate-900">Partner Details</th>
                    <th className="text-left p-4 font-medium text-slate-900">Contact</th>
                    <th className="text-left p-4 font-medium text-slate-900">Referral Code</th>
                    <th className="text-left p-4 font-medium text-slate-900">Profit Share</th>
                    <th className="text-left p-4 font-medium text-slate-900">Status</th>
                    <th className="text-left p-4 font-medium text-slate-900">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-200">
                  {partners.map((partner) => (
                    <tr key={partner.id} className="hover:bg-slate-50">
                      <td className="p-4">
                        <div>
                          <div className="font-medium text-slate-900">{partner.name}</div>
                          <div className="text-sm text-slate-500">{partner.companyName || 'No company'}</div>
                          <div className="text-xs text-slate-400">Code: {partner.partnerCode}</div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div>
                          <div className="text-sm text-slate-900">{partner.email}</div>
                          <div className="text-sm text-slate-500">{partner.phone}</div>
                        </div>
                      </td>
                      <td className="p-4">
                        <Badge variant="outline" className="font-mono">
                          {partner.referralCode || 'Not generated'}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <span className="text-sm font-medium">
                          {partner.profitSharePercentage || 40}%
                        </span>
                      </td>
                      <td className="p-4">
                        <Badge variant={partner.isActive ? "default" : "secondary"}>
                          {partner.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => viewPartnerSchools(partner.id)}
                          >
                            <Building2 className="w-4 h-4 mr-1" />
                            View Schools
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setSelectedPartner(partner)}
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Schools Modal */}
      {showSchoolsModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Partner Schools</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSchoolsModal(false)}
              >
                Close
              </Button>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-slate-50">
                  <tr>
                    <th className="text-left p-3 font-medium">School Name</th>
                    <th className="text-left p-3 font-medium">Contact</th>
                    <th className="text-left p-3 font-medium">Status</th>
                    <th className="text-left p-3 font-medium">Referred Date</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-200">
                  {partnerSchools.map((school) => (
                    <tr key={school.id}>
                      <td className="p-3">
                        <div>
                          <div className="font-medium">{school.schoolName}</div>
                          <div className="text-sm text-slate-500">{school.schoolCode}</div>
                        </div>
                      </td>
                      <td className="p-3">
                        <div>
                          <div className="text-sm">{school.email}</div>
                          <div className="text-sm text-slate-500">{school.phone}</div>
                        </div>
                      </td>
                      <td className="p-3">
                        <Badge variant={school.status === 'active' ? 'default' : 'secondary'}>
                          {school.status}
                        </Badge>
                      </td>
                      <td className="p-3">
                        {new Date(school.referredAt).toLocaleDateString()}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Financial Management Component
function FinancialManagement({ adminToken }: { adminToken: string }) {
  const [withdrawals, setWithdrawals] = useState<any[]>([])
  const [expenses, setExpenses] = useState<any[]>([])
  const [billingDashboard, setBillingDashboard] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [activeFinancialTab, setActiveFinancialTab] = useState('billing')

  useEffect(() => {
    fetchBillingDashboard()
    fetchWithdrawals()
    fetchExpenses()
  }, [])

  const fetchBillingDashboard = async () => {
    try {
      const response = await fetch('/api/admin/billing/dashboard', {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (response.ok) {
        const data = await response.json()
        setBillingDashboard(data)
      }
    } catch (error) {
      console.error('Error fetching billing dashboard:', error)
    }
  }

  const handleGenerateMonthlyBilling = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/scheduler/generate-billing', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        alert(`Billing generation completed!\nProcessed: ${data.result.processed}\nCreated: ${data.result.created}\nErrors: ${data.result.errors}`)
        // Refresh billing dashboard
        fetchBillingDashboard()
      } else {
        const error = await response.json()
        alert(`Error: ${error.error}`)
      }
    } catch (error) {
      console.error('Error generating monthly billing:', error)
      alert('Failed to generate monthly billing')
    } finally {
      setLoading(false)
    }
  }

  const fetchWithdrawals = async () => {
    try {
      const response = await fetch('/api/admin/withdrawals', {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (response.ok) {
        const data = await response.json()
        setWithdrawals(data.withdrawals)
      }
    } catch (error) {
      console.error('Error fetching withdrawals:', error)
    }
  }

  const fetchExpenses = async () => {
    try {
      const response = await fetch('/api/admin/expenses', {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (response.ok) {
        const data = await response.json()
        setExpenses(data.expenses)
      }
    } catch (error) {
      console.error('Error fetching expenses:', error)
    } finally {
      setLoading(false)
    }
  }

  const processWithdrawal = async (withdrawalId: string, status: string, data: any) => {
    try {
      const response = await fetch(`/api/admin/withdrawals/${withdrawalId}/process`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status, ...data })
      })

      if (response.ok) {
        fetchWithdrawals() // Refresh the list
      }
    } catch (error) {
      console.error('Error processing withdrawal:', error)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-slate-900">Financial Management</h2>
          <p className="text-slate-600">Manage withdrawals, expenses, and earnings</p>
        </div>
      </div>

      {/* Financial Tabs */}
      <div className="border-b border-slate-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'billing', label: 'Billing Dashboard', icon: TrendingUp },
            { id: 'withdrawals', label: 'Withdrawal Requests', icon: CreditCard },
            { id: 'expenses', label: 'Operational Expenses', icon: DollarSign }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveFinancialTab(tab.id)}
              className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeFinancialTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Billing Dashboard Tab */}
      {activeFinancialTab === 'billing' && (
        <div className="space-y-6">
          {loading || !billingDashboard ? (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
              <p className="mt-2 text-slate-600">Loading billing dashboard...</p>
            </div>
          ) : (
            <>
              {/* Financial Overview Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-slate-600">Active Subscriptions</p>
                        <p className="text-2xl font-bold text-slate-900">{billingDashboard.activeSubscriptions}</p>
                      </div>
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <UserCheck className="w-6 h-6 text-blue-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-slate-600">Monthly Revenue</p>
                        <p className="text-2xl font-bold text-green-600">₹{billingDashboard.monthlyRevenue}</p>
                      </div>
                      <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <TrendingUp className="w-6 h-6 text-green-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-slate-600">Outstanding Amount</p>
                        <p className="text-2xl font-bold text-red-600">₹{billingDashboard.outstanding.amount}</p>
                        <p className="text-sm text-slate-500">{billingDashboard.outstanding.count} invoices</p>
                      </div>
                      <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <AlertCircle className="w-6 h-6 text-red-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-slate-600">Overdue Amount</p>
                        <p className="text-2xl font-bold text-orange-600">₹{billingDashboard.overdue.amount}</p>
                        <p className="text-sm text-slate-500">{billingDashboard.overdue.count} invoices</p>
                      </div>
                      <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <Clock className="w-6 h-6 text-orange-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Recent Payments */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Recent Payments</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-slate-50 border-b border-slate-200">
                        <tr>
                          <th className="text-left p-4 font-medium text-slate-900">Client</th>
                          <th className="text-left p-4 font-medium text-slate-900">Invoice</th>
                          <th className="text-left p-4 font-medium text-slate-900">Amount</th>
                          <th className="text-left p-4 font-medium text-slate-900">Payment Date</th>
                          <th className="text-left p-4 font-medium text-slate-900">Status</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-slate-200">
                        {billingDashboard.recentPayments.map((payment: any, index: number) => (
                          <tr key={index} className="hover:bg-slate-50">
                            <td className="p-4">
                              <div>
                                <p className="font-medium text-slate-900">{payment.client.schoolName}</p>
                              </div>
                            </td>
                            <td className="p-4">
                              <p className="text-slate-900">{payment.invoice.invoiceNumber}</p>
                            </td>
                            <td className="p-4">
                              <p className="font-medium text-green-600">₹{payment.payment.amount}</p>
                            </td>
                            <td className="p-4">
                              <p className="text-slate-900">
                                {payment.payment.processedAt
                                  ? new Date(payment.payment.processedAt).toLocaleDateString()
                                  : 'Pending'
                                }
                              </p>
                            </td>
                            <td className="p-4">
                              <Badge variant={
                                payment.payment.status === 'success' ? 'default' :
                                payment.payment.status === 'failed' ? 'destructive' : 'secondary'
                              }>
                                {payment.payment.status}
                              </Badge>
                            </td>
                          </tr>
                        ))}
                        {billingDashboard.recentPayments.length === 0 && (
                          <tr>
                            <td colSpan={5} className="p-8 text-center text-slate-500">
                              No recent payments found
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button
                  onClick={handleGenerateMonthlyBilling}
                  className="h-16 text-left justify-start bg-blue-600 hover:bg-blue-700"
                >
                  <div>
                    <p className="font-medium">Generate Monthly Bills</p>
                    <p className="text-sm opacity-90">Create invoices for all active subscriptions</p>
                  </div>
                </Button>
                <Button className="h-16 text-left justify-start bg-green-600 hover:bg-green-700">
                  <div>
                    <p className="font-medium">View All Subscriptions</p>
                    <p className="text-sm opacity-90">Manage client subscriptions and billing</p>
                  </div>
                </Button>
                <Button className="h-16 text-left justify-start bg-purple-600 hover:bg-purple-700">
                  <div>
                    <p className="font-medium">Financial Reports</p>
                    <p className="text-sm opacity-90">Download detailed financial reports</p>
                  </div>
                </Button>
              </div>
            </>
          )}
        </div>
      )}

      {/* Withdrawals Tab */}
      {activeFinancialTab === 'withdrawals' && (
        <Card>
          <CardContent className="p-0">
            {loading ? (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
                <p className="mt-2 text-slate-600">Loading withdrawals...</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-slate-50 border-b border-slate-200">
                    <tr>
                      <th className="text-left p-4 font-medium text-slate-900">Partner</th>
                      <th className="text-left p-4 font-medium text-slate-900">Amount</th>
                      <th className="text-left p-4 font-medium text-slate-900">Status</th>
                      <th className="text-left p-4 font-medium text-slate-900">Request Date</th>
                      <th className="text-left p-4 font-medium text-slate-900">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-slate-200">
                    {withdrawals.map((withdrawal) => (
                      <tr key={withdrawal.id} className="hover:bg-slate-50">
                        <td className="p-4">
                          <div>
                            <div className="font-medium text-slate-900">{withdrawal.partnerName}</div>
                            <div className="text-sm text-slate-500">{withdrawal.partnerEmail}</div>
                          </div>
                        </td>
                        <td className="p-4">
                          <div>
                            <div className="font-medium">₹{withdrawal.requestedAmount.toLocaleString()}</div>
                            <div className="text-sm text-slate-500">
                              Available: ₹{withdrawal.availableBalance.toLocaleString()}
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          <Badge
                            variant={
                              withdrawal.status === 'processed' ? 'default' :
                              withdrawal.status === 'pending' ? 'secondary' :
                              withdrawal.status === 'rejected' ? 'destructive' : 'outline'
                            }
                          >
                            {withdrawal.status}
                          </Badge>
                        </td>
                        <td className="p-4">
                          {new Date(withdrawal.requestedAt).toLocaleDateString()}
                        </td>
                        <td className="p-4">
                          {withdrawal.status === 'pending' && (
                            <div className="flex items-center space-x-2">
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-green-600 border-green-600 hover:bg-green-50"
                                onClick={() => {
                                  const transactionRef = prompt('Enter NEFT Transaction Reference:')
                                  if (transactionRef) {
                                    processWithdrawal(withdrawal.id, 'approved', {
                                      transactionReference: transactionRef
                                    })
                                  }
                                }}
                              >
                                Approve
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-red-600 border-red-600 hover:bg-red-50"
                                onClick={() => {
                                  const reason = prompt('Enter rejection reason:')
                                  if (reason) {
                                    processWithdrawal(withdrawal.id, 'rejected', {
                                      rejectionReason: reason
                                    })
                                  }
                                }}
                              >
                                Reject
                              </Button>
                            </div>
                          )}
                          {withdrawal.status === 'processed' && withdrawal.transactionReference && (
                            <div className="text-sm text-slate-600">
                              Ref: {withdrawal.transactionReference}
                            </div>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Expenses Tab */}
      {activeFinancialTab === 'expenses' && (
        <Card>
          <CardContent className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold">Operational Expenses</h3>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                <Plus className="w-4 h-4 mr-2" />
                Add Expense
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {expenses.map((expense) => (
                <div key={expense.id} className="border border-slate-200 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium text-slate-900">{expense.categoryName}</h4>
                    <Badge variant={expense.isActive ? 'default' : 'secondary'}>
                      {expense.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                  <p className="text-sm text-slate-600 mb-3">{expense.description}</p>
                  <div className="text-lg font-semibold text-blue-600">
                    {expense.isPercentage
                      ? `${expense.percentageValue}%`
                      : `₹${expense.amountPerSchool?.toLocaleString()}/school`
                    }
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Analytics & Reporting Dashboard Component
function AnalyticsReportingDashboard({ adminToken }: { adminToken: string }) {
  const [analyticsData, setAnalyticsData] = useState<any>(null)
  const [billingAnalytics, setBillingAnalytics] = useState<any>(null)
  const [clientAnalytics, setClientAnalytics] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('30')
  const [activeAnalyticsTab, setActiveAnalyticsTab] = useState('revenue')

  useEffect(() => {
    fetchAnalyticsData()
  }, [selectedPeriod])

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true)

      // Fetch multiple analytics endpoints
      const [billingResponse, clientResponse, dashboardResponse] = await Promise.all([
        fetch(`/api/admin/billing/analytics?period=${selectedPeriod}`, {
          headers: { 'Authorization': `Bearer ${adminToken}` }
        }),
        fetch('/api/admin/clients/analytics', {
          headers: { 'Authorization': `Bearer ${adminToken}` }
        }),
        fetch('/api/admin/dashboard', {
          headers: { 'Authorization': `Bearer ${adminToken}` }
        })
      ])

      if (billingResponse.ok) {
        const billingData = await billingResponse.json()
        setBillingAnalytics(billingData)
      }

      if (clientResponse.ok) {
        const clientData = await clientResponse.json()
        setClientAnalytics(clientData.analytics)
      }

      if (dashboardResponse.ok) {
        const dashboardData = await dashboardResponse.json()
        setAnalyticsData(dashboardData)
      }

    } catch (error) {
      console.error('Error fetching analytics data:', error)
    } finally {
      setLoading(false)
    }
  }

  const exportReport = async (reportType: string) => {
    try {
      const response = await fetch(`/api/admin/reports/export?type=${reportType}&period=${selectedPeriod}`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${reportType}-report-${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error('Error exporting report:', error)
      alert('Failed to export report')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-slate-900">Analytics & Reporting</h2>
          <p className="text-slate-600">Comprehensive business insights and performance metrics</p>
        </div>
        <div className="flex items-center space-x-4">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 90 days</option>
            <option value="365">Last year</option>
          </select>
          <Button onClick={() => exportReport('comprehensive')} className="flex items-center space-x-2">
            <Download className="w-4 h-4" />
            <span>Export Report</span>
          </Button>
        </div>
      </div>

      {/* Analytics Navigation */}
      <Card>
        <CardContent className="p-0">
          <div className="flex border-b border-slate-200">
            {[
              { id: 'revenue', label: 'Revenue Analytics', icon: DollarSign },
              { id: 'clients', label: 'Client Growth', icon: TrendingUp },
              { id: 'leads', label: 'Lead Conversion', icon: Users },
              { id: 'partners', label: 'Partner Performance', icon: Handshake },
              { id: 'payments', label: 'Payment Analytics', icon: CreditCard }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveAnalyticsTab(tab.id)}
                className={`flex items-center space-x-2 px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                  activeAnalyticsTab === tab.id
                    ? 'border-blue-500 text-blue-600 bg-blue-50'
                    : 'border-transparent text-slate-500 hover:text-slate-700 hover:bg-slate-50'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Revenue Analytics */}
      {activeAnalyticsTab === 'revenue' && billingAnalytics && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Total Revenue</p>
                    <p className="text-3xl font-bold text-green-600">₹{billingAnalytics.metrics?.totalRevenue?.toLocaleString() || 0}</p>
                    <p className="text-sm text-slate-500">{billingAnalytics.period}</p>
                  </div>
                  <DollarSign className="w-8 h-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Pending Revenue</p>
                    <p className="text-3xl font-bold text-yellow-600">₹{billingAnalytics.metrics?.pendingRevenue?.toLocaleString() || 0}</p>
                    <p className="text-sm text-slate-500">Awaiting payment</p>
                  </div>
                  <Clock className="w-8 h-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Overdue Revenue</p>
                    <p className="text-3xl font-bold text-red-600">₹{billingAnalytics.metrics?.overdueRevenue?.toLocaleString() || 0}</p>
                    <p className="text-sm text-slate-500">Requires attention</p>
                  </div>
                  <AlertCircle className="w-8 h-8 text-red-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Active Subscriptions</p>
                    <p className="text-3xl font-bold text-blue-600">{billingAnalytics.metrics?.activeSubscriptions || 0}</p>
                    <p className="text-sm text-slate-500">Monthly recurring</p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Breakdown</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600">Gross Revenue</span>
                    <span className="font-semibold">₹{billingAnalytics.metrics?.totalRevenue?.toLocaleString() || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600">Partner Payouts</span>
                    <span className="font-semibold text-red-600">-₹{analyticsData?.partnerStats?.totalEarnings?.toLocaleString() || 0}</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between items-center">
                      <span className="text-slate-900 font-semibold">Net Revenue</span>
                      <span className="font-bold text-green-600">
                        ₹{((billingAnalytics.metrics?.totalRevenue || 0) - (analyticsData?.partnerStats?.totalEarnings || 0)).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Payment Status Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="text-slate-600">Paid</span>
                    </div>
                    <span className="font-semibold">₹{billingAnalytics.metrics?.totalRevenue?.toLocaleString() || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <span className="text-slate-600">Pending</span>
                    </div>
                    <span className="font-semibold">₹{billingAnalytics.metrics?.pendingRevenue?.toLocaleString() || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <span className="text-slate-600">Overdue</span>
                    </div>
                    <span className="font-semibold">₹{billingAnalytics.metrics?.overdueRevenue?.toLocaleString() || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Client Growth Analytics */}
      {activeAnalyticsTab === 'clients' && clientAnalytics && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Total Clients</p>
                    <p className="text-3xl font-bold text-blue-600">{clientAnalytics.totalClients}</p>
                    <p className="text-sm text-slate-500">Active schools</p>
                  </div>
                  <Building2 className="w-8 h-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Total Students</p>
                    <p className="text-3xl font-bold text-green-600">{clientAnalytics.totalStudents?.toLocaleString()}</p>
                    <p className="text-sm text-slate-500">Across all schools</p>
                  </div>
                  <Users className="w-8 h-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Average Class Fee</p>
                    <p className="text-3xl font-bold text-purple-600">₹{clientAnalytics.averageFee?.toLocaleString()}</p>
                    <p className="text-sm text-slate-500">Per student</p>
                  </div>
                  <DollarSign className="w-8 h-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Partner Attributed</p>
                    <p className="text-3xl font-bold text-orange-600">{clientAnalytics.partnerAttributed}</p>
                    <p className="text-sm text-slate-500">Via referrals</p>
                  </div>
                  <Handshake className="w-8 h-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Fee Range Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600">Minimum Fee</span>
                    <span className="font-semibold">₹{clientAnalytics.feeRange?.min?.toLocaleString() || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600">Maximum Fee</span>
                    <span className="font-semibold">₹{clientAnalytics.feeRange?.max?.toLocaleString() || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600">Average Fee</span>
                    <span className="font-semibold">₹{clientAnalytics.averageFee?.toLocaleString() || 0}</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between items-center">
                      <span className="text-slate-900 font-semibold">Schools with Fees Set</span>
                      <span className="font-bold text-blue-600">{clientAnalytics.clientsWithFees} / {clientAnalytics.totalClients}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Client Acquisition Sources</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="text-slate-600">Direct</span>
                    </div>
                    <span className="font-semibold">{clientAnalytics.totalClients - clientAnalytics.partnerAttributed}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                      <span className="text-slate-600">Partner Referrals</span>
                    </div>
                    <span className="font-semibold">{clientAnalytics.partnerAttributed}</span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between items-center">
                      <span className="text-slate-900 font-semibold">Referral Rate</span>
                      <span className="font-bold text-green-600">
                        {clientAnalytics.totalClients > 0 ? Math.round((clientAnalytics.partnerAttributed / clientAnalytics.totalClients) * 100) : 0}%
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Lead Conversion Analytics */}
      {activeAnalyticsTab === 'leads' && analyticsData && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Total Leads</p>
                    <p className="text-3xl font-bold text-blue-600">{analyticsData.statistics?.totalLeads || 0}</p>
                    <p className="text-sm text-slate-500">All time</p>
                  </div>
                  <Users className="w-8 h-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Converted Clients</p>
                    <p className="text-3xl font-bold text-green-600">{analyticsData.statistics?.totalClients || 0}</p>
                    <p className="text-sm text-slate-500">Successful conversions</p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Conversion Rate</p>
                    <p className="text-3xl font-bold text-purple-600">
                      {analyticsData.statistics?.totalLeads > 0
                        ? Math.round((analyticsData.statistics.totalClients / analyticsData.statistics.totalLeads) * 100)
                        : 0}%
                    </p>
                    <p className="text-sm text-slate-500">Lead to client</p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Pending Requests</p>
                    <p className="text-3xl font-bold text-yellow-600">{analyticsData.statistics?.pendingRequests || 0}</p>
                    <p className="text-sm text-slate-500">Awaiting approval</p>
                  </div>
                  <Clock className="w-8 h-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Lead Conversion Funnel</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">1</div>
                    <span className="font-medium">Total Leads</span>
                  </div>
                  <span className="text-2xl font-bold text-blue-600">{analyticsData.statistics?.totalLeads || 0}</span>
                </div>

                <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center text-white text-sm font-bold">2</div>
                    <span className="font-medium">Software Requests</span>
                  </div>
                  <span className="text-2xl font-bold text-yellow-600">{analyticsData.statistics?.pendingRequests || 0}</span>
                </div>

                <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold">3</div>
                    <span className="font-medium">Active Clients</span>
                  </div>
                  <span className="text-2xl font-bold text-green-600">{analyticsData.statistics?.totalClients || 0}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Partner Performance Analytics */}
      {activeAnalyticsTab === 'partners' && analyticsData?.partnerStats && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Total Partners</p>
                    <p className="text-3xl font-bold text-blue-600">{analyticsData.partnerStats.totalPartners}</p>
                    <p className="text-sm text-slate-500">{analyticsData.partnerStats.activePartners} active</p>
                  </div>
                  <Handshake className="w-8 h-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Total Referrals</p>
                    <p className="text-3xl font-bold text-green-600">{analyticsData.partnerStats.totalReferrals}</p>
                    <p className="text-sm text-slate-500">Schools referred</p>
                  </div>
                  <Users className="w-8 h-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Total Earnings</p>
                    <p className="text-3xl font-bold text-purple-600">₹{analyticsData.partnerStats.totalEarnings?.toLocaleString()}</p>
                    <p className="text-sm text-slate-500">Partner payouts</p>
                  </div>
                  <DollarSign className="w-8 h-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Pending Withdrawals</p>
                    <p className="text-3xl font-bold text-orange-600">{analyticsData.partnerStats.pendingWithdrawals}</p>
                    <p className="text-sm text-slate-500">Awaiting processing</p>
                  </div>
                  <Clock className="w-8 h-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Partner Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600">Active Partners</span>
                    <span className="font-semibold">{analyticsData.partnerStats.activePartners} / {analyticsData.partnerStats.totalPartners}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600">Average Referrals per Partner</span>
                    <span className="font-semibold">
                      {analyticsData.partnerStats.totalPartners > 0
                        ? Math.round(analyticsData.partnerStats.totalReferrals / analyticsData.partnerStats.totalPartners * 10) / 10
                        : 0}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-600">Average Earnings per Partner</span>
                    <span className="font-semibold">
                      ₹{analyticsData.partnerStats.totalPartners > 0
                        ? Math.round(analyticsData.partnerStats.totalEarnings / analyticsData.partnerStats.totalPartners).toLocaleString()
                        : 0}
                    </span>
                  </div>
                  <div className="border-t pt-2">
                    <div className="flex justify-between items-center">
                      <span className="text-slate-900 font-semibold">Partner Activation Rate</span>
                      <span className="font-bold text-green-600">
                        {analyticsData.partnerStats.totalPartners > 0
                          ? Math.round((analyticsData.partnerStats.activePartners / analyticsData.partnerStats.totalPartners) * 100)
                          : 0}%
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Partner Financial Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="text-slate-600">Total Earnings</span>
                    </div>
                    <span className="font-semibold">₹{analyticsData.partnerStats.totalEarnings?.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="text-slate-600">Available Balance</span>
                    </div>
                    <span className="font-semibold">₹{analyticsData.partnerStats.availableBalance?.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                      <span className="text-slate-600">Pending Withdrawals</span>
                    </div>
                    <span className="font-semibold">{analyticsData.partnerStats.pendingWithdrawals} requests</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Payment Analytics */}
      {activeAnalyticsTab === 'payments' && billingAnalytics && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Total Invoices</p>
                    <p className="text-3xl font-bold text-blue-600">{billingAnalytics.metrics?.totalInvoices || 0}</p>
                    <p className="text-sm text-slate-500">{billingAnalytics.period}</p>
                  </div>
                  <FileText className="w-8 h-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Payment Success Rate</p>
                    <p className="text-3xl font-bold text-green-600">
                      {billingAnalytics.metrics?.totalInvoices > 0
                        ? Math.round(((billingAnalytics.metrics.totalRevenue || 0) /
                          ((billingAnalytics.metrics.totalRevenue || 0) + (billingAnalytics.metrics.pendingRevenue || 0) + (billingAnalytics.metrics.overdueRevenue || 0))) * 100)
                        : 0}%
                    </p>
                    <p className="text-sm text-slate-500">Successful payments</p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Average Invoice Value</p>
                    <p className="text-3xl font-bold text-purple-600">
                      ₹{billingAnalytics.metrics?.totalInvoices > 0
                        ? Math.round((billingAnalytics.metrics.totalRevenue || 0) / billingAnalytics.metrics.totalInvoices).toLocaleString()
                        : 0}
                    </p>
                    <p className="text-sm text-slate-500">Per invoice</p>
                  </div>
                  <CreditCard className="w-8 h-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-600">Collection Efficiency</p>
                    <p className="text-3xl font-bold text-orange-600">
                      {((billingAnalytics.metrics?.totalRevenue || 0) + (billingAnalytics.metrics?.pendingRevenue || 0) + (billingAnalytics.metrics?.overdueRevenue || 0)) > 0
                        ? Math.round(((billingAnalytics.metrics.totalRevenue || 0) /
                          ((billingAnalytics.metrics.totalRevenue || 0) + (billingAnalytics.metrics.pendingRevenue || 0) + (billingAnalytics.metrics.overdueRevenue || 0))) * 100)
                        : 0}%
                    </p>
                    <p className="text-sm text-slate-500">Revenue collected</p>
                  </div>
                  <BarChart3 className="w-8 h-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Payment Status Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-6 bg-green-50 rounded-lg">
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    ₹{billingAnalytics.metrics?.totalRevenue?.toLocaleString() || 0}
                  </div>
                  <div className="text-sm font-medium text-green-700">Collected Revenue</div>
                  <div className="text-xs text-green-600 mt-1">
                    {((billingAnalytics.metrics?.totalRevenue || 0) + (billingAnalytics.metrics?.pendingRevenue || 0) + (billingAnalytics.metrics?.overdueRevenue || 0)) > 0
                      ? Math.round(((billingAnalytics.metrics.totalRevenue || 0) /
                        ((billingAnalytics.metrics.totalRevenue || 0) + (billingAnalytics.metrics.pendingRevenue || 0) + (billingAnalytics.metrics.overdueRevenue || 0))) * 100)
                      : 0}% of total
                  </div>
                </div>

                <div className="text-center p-6 bg-yellow-50 rounded-lg">
                  <div className="text-3xl font-bold text-yellow-600 mb-2">
                    ₹{billingAnalytics.metrics?.pendingRevenue?.toLocaleString() || 0}
                  </div>
                  <div className="text-sm font-medium text-yellow-700">Pending Revenue</div>
                  <div className="text-xs text-yellow-600 mt-1">
                    {((billingAnalytics.metrics?.totalRevenue || 0) + (billingAnalytics.metrics?.pendingRevenue || 0) + (billingAnalytics.metrics?.overdueRevenue || 0)) > 0
                      ? Math.round(((billingAnalytics.metrics.pendingRevenue || 0) /
                        ((billingAnalytics.metrics.totalRevenue || 0) + (billingAnalytics.metrics.pendingRevenue || 0) + (billingAnalytics.metrics.overdueRevenue || 0))) * 100)
                      : 0}% of total
                  </div>
                </div>

                <div className="text-center p-6 bg-red-50 rounded-lg">
                  <div className="text-3xl font-bold text-red-600 mb-2">
                    ₹{billingAnalytics.metrics?.overdueRevenue?.toLocaleString() || 0}
                  </div>
                  <div className="text-sm font-medium text-red-700">Overdue Revenue</div>
                  <div className="text-xs text-red-600 mt-1">
                    {((billingAnalytics.metrics?.totalRevenue || 0) + (billingAnalytics.metrics?.pendingRevenue || 0) + (billingAnalytics.metrics?.overdueRevenue || 0)) > 0
                      ? Math.round(((billingAnalytics.metrics.overdueRevenue || 0) /
                        ((billingAnalytics.metrics.totalRevenue || 0) + (billingAnalytics.metrics.pendingRevenue || 0) + (billingAnalytics.metrics.overdueRevenue || 0))) * 100)
                      : 0}% of total
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Export Options */}
          <Card>
            <CardHeader>
              <CardTitle>Export Reports</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Button
                  variant="outline"
                  onClick={() => exportReport('revenue')}
                  className="flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>Revenue Report</span>
                </Button>
                <Button
                  variant="outline"
                  onClick={() => exportReport('clients')}
                  className="flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>Client Report</span>
                </Button>
                <Button
                  variant="outline"
                  onClick={() => exportReport('partners')}
                  className="flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>Partner Report</span>
                </Button>
                <Button
                  variant="outline"
                  onClick={() => exportReport('payments')}
                  className="flex items-center space-x-2"
                >
                  <Download className="w-4 h-4" />
                  <span>Payment Report</span>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}

// Admin Users Management Component
function AdminUsersManagement({ adminToken }: { adminToken: string }) {
  const [adminUsers, setAdminUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [selectedAdmin, setSelectedAdmin] = useState<any>(null)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showViewModal, setShowViewModal] = useState(false)
  const [roles, setRoles] = useState<any>({})

  useEffect(() => {
    fetchAdminUsers()
  }, [])

  const fetchAdminUsers = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/admin-users', {
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setAdminUsers(data.adminUsers || [])
        setRoles(data.roles || {})
      } else {
        console.error('Failed to fetch admin users')
      }
    } catch (error) {
      console.error('Error fetching admin users:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateAdmin = async (adminData: any) => {
    try {
      const response = await fetch('/api/admin/admin-users', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(adminData)
      })

      if (response.ok) {
        await fetchAdminUsers()
        setShowCreateModal(false)
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to create admin user')
      }
    } catch (error) {
      console.error('Error creating admin user:', error)
      alert('Failed to create admin user')
    }
  }

  const handleUpdateAdmin = async (adminId: string, updateData: any) => {
    try {
      const response = await fetch(`/api/admin/admin-users/${adminId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      })

      if (response.ok) {
        await fetchAdminUsers()
        setShowEditModal(false)
        setSelectedAdmin(null)
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to update admin user')
      }
    } catch (error) {
      console.error('Error updating admin user:', error)
      alert('Failed to update admin user')
    }
  }

  const handleToggleStatus = async (adminId: string, isActive: boolean) => {
    try {
      const endpoint = isActive ? 'deactivate' : 'activate'
      const response = await fetch(`/api/admin/admin-users/${adminId}/${endpoint}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        await fetchAdminUsers()
      } else {
        const error = await response.json()
        alert(error.error || `Failed to ${endpoint} admin user`)
      }
    } catch (error) {
      console.error(`Error ${isActive ? 'deactivating' : 'activating'} admin user:`, error)
      alert(`Failed to ${isActive ? 'deactivate' : 'activate'} admin user`)
    }
  }

  const filteredAdmins = adminUsers.filter((admin: any) => {
    const matchesSearch = admin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         admin.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = !statusFilter ||
                         (statusFilter === 'active' && admin.isActive) ||
                         (statusFilter === 'inactive' && !admin.isActive)
    return matchesSearch && matchesStatus
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-slate-900">Admin Users Management</h2>
          <p className="text-slate-600">Manage admin users, roles, and permissions</p>
        </div>
        <Button onClick={() => setShowCreateModal(true)} className="flex items-center space-x-2">
          <Plus className="w-4 h-4" />
          <span>Create Admin User</span>
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Admin Users Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-slate-50 border-b border-slate-200">
                <tr>
                  <th className="text-left p-4 font-medium text-slate-900">Admin User</th>
                  <th className="text-left p-4 font-medium text-slate-900">Role</th>
                  <th className="text-left p-4 font-medium text-slate-900">Status</th>
                  <th className="text-left p-4 font-medium text-slate-900">Last Login</th>
                  <th className="text-left p-4 font-medium text-slate-900">Created</th>
                  <th className="text-left p-4 font-medium text-slate-900">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-slate-200">
                {filteredAdmins.map((admin: any) => (
                  <tr key={admin.id} className="hover:bg-slate-50">
                    <td className="p-4">
                      <div>
                        <p className="font-medium text-slate-900">{admin.name}</p>
                        <p className="text-sm text-slate-500">{admin.email}</p>
                      </div>
                    </td>
                    <td className="p-4">
                      <Badge variant={admin.role === 'super_admin' ? 'default' : 'secondary'}>
                        {admin.role.replace('_', ' ').toUpperCase()}
                      </Badge>
                    </td>
                    <td className="p-4">
                      <Badge variant={admin.isActive ? 'default' : 'destructive'}>
                        {admin.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </td>
                    <td className="p-4">
                      <p className="text-sm text-slate-600">
                        {admin.lastLogin ? new Date(admin.lastLogin).toLocaleDateString() : 'Never'}
                      </p>
                    </td>
                    <td className="p-4">
                      <p className="text-sm text-slate-600">
                        {new Date(admin.createdAt).toLocaleDateString()}
                      </p>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setSelectedAdmin(admin)
                            setShowViewModal(true)
                          }}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setSelectedAdmin(admin)
                            setShowEditModal(true)
                          }}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant={admin.isActive ? "destructive" : "default"}
                          onClick={() => handleToggleStatus(admin.id, admin.isActive)}
                        >
                          {admin.isActive ? 'Deactivate' : 'Activate'}
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Create Admin Modal */}
      {showCreateModal && (
        <CreateAdminModal
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateAdmin}
          roles={roles}
        />
      )}

      {/* Edit Admin Modal */}
      {showEditModal && selectedAdmin && (
        <EditAdminModal
          admin={selectedAdmin}
          onClose={() => {
            setShowEditModal(false)
            setSelectedAdmin(null)
          }}
          onSubmit={(updateData) => handleUpdateAdmin(selectedAdmin.id, updateData)}
          roles={roles}
        />
      )}

      {/* View Admin Modal */}
      {showViewModal && selectedAdmin && (
        <ViewAdminModal
          admin={selectedAdmin}
          onClose={() => {
            setShowViewModal(false)
            setSelectedAdmin(null)
          }}
        />
      )}
    </div>
  )
}

// Create Admin Modal Component
function CreateAdminModal({ onClose, onSubmit, roles }: { onClose: () => void, onSubmit: (data: any) => void, roles: any }) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    role: 'support',
    password: ''
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name || !formData.email || !formData.password) {
      alert('Please fill in all required fields')
      return
    }
    onSubmit(formData)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Create Admin User</h3>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Full Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Email Address *
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Role *
            </label>
            <select
              value={formData.role}
              onChange={(e) => setFormData({ ...formData, role: e.target.value })}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              <option value="support">Support</option>
              <option value="sales">Sales</option>
              <option value="billing">Billing</option>
              <option value="super_admin">Super Admin</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Password *
            </label>
            <input
              type="password"
              value={formData.password}
              onChange={(e) => setFormData({ ...formData, password: e.target.value })}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              minLength={8}
              required
            />
            <p className="text-xs text-slate-500 mt-1">Minimum 8 characters</p>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              Create Admin User
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}

// Edit Admin Modal Component
function EditAdminModal({ admin, onClose, onSubmit, roles }: { admin: any, onClose: () => void, onSubmit: (data: any) => void, roles: any }) {
  const [formData, setFormData] = useState({
    name: admin.name || '',
    role: admin.role || 'support',
    isActive: admin.isActive !== false
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name) {
      alert('Please fill in all required fields')
      return
    }
    onSubmit(formData)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Edit Admin User</h3>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Full Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Email Address
            </label>
            <input
              type="email"
              value={admin.email}
              disabled
              className="w-full px-3 py-2 border border-slate-300 rounded-lg bg-slate-100 text-slate-500"
            />
            <p className="text-xs text-slate-500 mt-1">Email cannot be changed</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Role *
            </label>
            <select
              value={formData.role}
              onChange={(e) => setFormData({ ...formData, role: e.target.value })}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              <option value="support">Support</option>
              <option value="sales">Sales</option>
              <option value="billing">Billing</option>
              <option value="super_admin">Super Admin</option>
            </select>
          </div>

          <div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={formData.isActive}
                onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                className="rounded border-slate-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-slate-700">Active</span>
            </label>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              Update Admin User
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}

// View Admin Modal Component
function ViewAdminModal({ admin, onClose }: { admin: any, onClose: () => void }) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Admin User Details</h3>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Full Name
            </label>
            <p className="text-slate-900">{admin.name}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Email Address
            </label>
            <p className="text-slate-900">{admin.email}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Role
            </label>
            <Badge variant={admin.role === 'super_admin' ? 'default' : 'secondary'}>
              {admin.role.replace('_', ' ').toUpperCase()}
            </Badge>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Status
            </label>
            <Badge variant={admin.isActive ? 'default' : 'destructive'}>
              {admin.isActive ? 'Active' : 'Inactive'}
            </Badge>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Permissions
            </label>
            <div className="flex flex-wrap gap-1">
              {admin.permissions && admin.permissions.length > 0 ? (
                admin.permissions.map((permission: string, index: number) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {permission}
                  </Badge>
                ))
              ) : (
                <p className="text-slate-500 text-sm">No specific permissions</p>
              )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Last Login
            </label>
            <p className="text-slate-900">
              {admin.lastLogin ? new Date(admin.lastLogin).toLocaleString() : 'Never'}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Created At
            </label>
            <p className="text-slate-900">
              {new Date(admin.createdAt).toLocaleString()}
            </p>
          </div>

          <div className="flex justify-end pt-4">
            <Button onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

// Support Tickets Management Component
function SupportTicketsManagement({ adminToken }: { adminToken: string }) {
  const [tickets, setTickets] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedTicket, setSelectedTicket] = useState<any>(null)
  const [showTicketModal, setShowTicketModal] = useState(false)
  const [showAssignModal, setShowAssignModal] = useState(false)
  const [availableAdmins, setAvailableAdmins] = useState<any[]>([])
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    assignedTo: '',
    search: ''
  })
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })

  // Fetch support tickets
  const fetchTickets = async () => {
    try {
      setLoading(true)
      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(filters.status && { status: filters.status }),
        ...(filters.priority && { priority: filters.priority }),
        ...(filters.assignedTo && { assignedTo: filters.assignedTo }),
        ...(filters.search && { search: filters.search })
      })

      const response = await fetch(`/api/admin/support/tickets?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setTickets(data.tickets)
        setPagination(data.pagination)
      }
    } catch (error) {
      console.error('Error fetching tickets:', error)
    } finally {
      setLoading(false)
    }
  }

  // Fetch available admins for assignment
  const fetchAvailableAdmins = async () => {
    try {
      const response = await fetch('/api/admin/support/available-admins', {
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setAvailableAdmins(data.admins)
      }
    } catch (error) {
      console.error('Error fetching available admins:', error)
    }
  }

  useEffect(() => {
    fetchTickets()
    fetchAvailableAdmins()
  }, [pagination.page, filters])

  // View ticket details
  const viewTicket = async (ticketId: string) => {
    try {
      const response = await fetch(`/api/admin/support/tickets/${ticketId}`, {
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        setSelectedTicket(data)
        setShowTicketModal(true)
      }
    } catch (error) {
      console.error('Error fetching ticket details:', error)
    }
  }

  // Update ticket status
  const updateTicketStatus = async (ticketId: string, status: string) => {
    try {
      const response = await fetch(`/api/admin/support/tickets/${ticketId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status })
      })

      if (response.ok) {
        fetchTickets()
        if (selectedTicket?.ticket.id === ticketId) {
          viewTicket(ticketId) // Refresh ticket details
        }
      }
    } catch (error) {
      console.error('Error updating ticket status:', error)
    }
  }

  // Assign ticket to admin
  const assignTicket = async (ticketId: string, adminId: string) => {
    try {
      const response = await fetch(`/api/admin/support/tickets/${ticketId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ assignedTo: adminId })
      })

      if (response.ok) {
        fetchTickets()
        setShowAssignModal(false)
        if (selectedTicket?.ticket.id === ticketId) {
          viewTicket(ticketId) // Refresh ticket details
        }
      }
    } catch (error) {
      console.error('Error assigning ticket:', error)
    }
  }

  // Add message to ticket
  const addMessage = async (ticketId: string, message: string) => {
    try {
      const response = await fetch(`/api/admin/support/tickets/${ticketId}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ message })
      })

      if (response.ok) {
        viewTicket(ticketId) // Refresh ticket details to show new message
      }
    } catch (error) {
      console.error('Error adding message:', error)
    }
  }

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-blue-100 text-blue-800'
      case 'in_progress': return 'bg-yellow-100 text-yellow-800'
      case 'resolved': return 'bg-green-100 text-green-800'
      case 'closed': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800'
      case 'high': return 'bg-orange-100 text-orange-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-slate-900">Support Tickets</h2>
          <p className="text-slate-600">Manage and respond to client support requests</p>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Search</label>
              <input
                type="text"
                placeholder="Search tickets..."
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Status</label>
              <select
                value={filters.status}
                onChange={(e) => setFilters({ ...filters, status: e.target.value })}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">All Statuses</option>
                <option value="open">Open</option>
                <option value="in_progress">In Progress</option>
                <option value="resolved">Resolved</option>
                <option value="closed">Closed</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Priority</label>
              <select
                value={filters.priority}
                onChange={(e) => setFilters({ ...filters, priority: e.target.value })}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">All Priorities</option>
                <option value="urgent">Urgent</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Assigned To</label>
              <select
                value={filters.assignedTo}
                onChange={(e) => setFilters({ ...filters, assignedTo: e.target.value })}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">All Admins</option>
                {availableAdmins.map((admin) => (
                  <option key={admin.id} value={admin.id}>{admin.name}</option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tickets Table */}
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-slate-50 border-b border-slate-200">
                  <tr>
                    <th className="text-left py-3 px-4 font-medium text-slate-700">Ticket</th>
                    <th className="text-left py-3 px-4 font-medium text-slate-700">Client</th>
                    <th className="text-left py-3 px-4 font-medium text-slate-700">Status</th>
                    <th className="text-left py-3 px-4 font-medium text-slate-700">Priority</th>
                    <th className="text-left py-3 px-4 font-medium text-slate-700">Assigned To</th>
                    <th className="text-left py-3 px-4 font-medium text-slate-700">Created</th>
                    <th className="text-left py-3 px-4 font-medium text-slate-700">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-200">
                  {tickets.map((ticket) => (
                    <tr key={ticket.id} className="hover:bg-slate-50">
                      <td className="py-3 px-4">
                        <div>
                          <div className="font-medium text-slate-900">{ticket.title}</div>
                          <div className="text-sm text-slate-500 truncate max-w-xs">
                            {ticket.description}
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div>
                          <div className="font-medium text-slate-900">{ticket.clientName}</div>
                          <div className="text-sm text-slate-500">{ticket.clientEmail}</div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <Badge className={getStatusBadgeColor(ticket.status)}>
                          {ticket.status.replace('_', ' ')}
                        </Badge>
                      </td>
                      <td className="py-3 px-4">
                        <Badge className={getPriorityBadgeColor(ticket.priority)}>
                          {ticket.priority}
                        </Badge>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm text-slate-900">
                          {ticket.assignedAdminName || 'Unassigned'}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm text-slate-900">
                          {new Date(ticket.createdAt).toLocaleDateString()}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => viewTicket(ticket.id)}
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setSelectedTicket({ ticket })
                              setShowAssignModal(true)
                            }}
                          >
                            Assign
                          </Button>
                          {ticket.status === 'open' && (
                            <Button
                              size="sm"
                              onClick={() => updateTicketStatus(ticket.id, 'in_progress')}
                            >
                              Start
                            </Button>
                          )}
                          {ticket.status === 'in_progress' && (
                            <Button
                              size="sm"
                              onClick={() => updateTicketStatus(ticket.id, 'resolved')}
                            >
                              Resolve
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="flex justify-center space-x-2">
          <Button
            variant="outline"
            disabled={pagination.page === 1}
            onClick={() => setPagination({ ...pagination, page: pagination.page - 1 })}
          >
            Previous
          </Button>
          <span className="flex items-center px-4 py-2 text-sm text-slate-700">
            Page {pagination.page} of {pagination.pages}
          </span>
          <Button
            variant="outline"
            disabled={pagination.page === pagination.pages}
            onClick={() => setPagination({ ...pagination, page: pagination.page + 1 })}
          >
            Next
          </Button>
        </div>
      )}

      {/* Ticket Details Modal */}
      {showTicketModal && selectedTicket && (
        <TicketDetailsModal
          ticket={selectedTicket}
          onClose={() => setShowTicketModal(false)}
          onAddMessage={addMessage}
          onUpdateStatus={updateTicketStatus}
        />
      )}

      {/* Assign Ticket Modal */}
      {showAssignModal && selectedTicket && (
        <AssignTicketModal
          ticket={selectedTicket.ticket}
          availableAdmins={availableAdmins}
          onClose={() => setShowAssignModal(false)}
          onAssign={assignTicket}
        />
      )}
    </div>
  )
}

// Ticket Details Modal Component
function TicketDetailsModal({ ticket, onClose, onAddMessage, onUpdateStatus }: {
  ticket: any,
  onClose: () => void,
  onAddMessage: (ticketId: string, message: string) => void,
  onUpdateStatus: (ticketId: string, status: string) => void
}) {
  const [newMessage, setNewMessage] = useState('')

  const handleAddMessage = (e: React.FormEvent) => {
    e.preventDefault()
    if (newMessage.trim()) {
      onAddMessage(ticket.ticket.id, newMessage)
      setNewMessage('')
    }
  }

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-blue-100 text-blue-800'
      case 'in_progress': return 'bg-yellow-100 text-yellow-800'
      case 'resolved': return 'bg-green-100 text-green-800'
      case 'closed': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800'
      case 'high': return 'bg-orange-100 text-orange-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex justify-between items-center p-6 border-b border-slate-200">
          <h3 className="text-lg font-semibold text-slate-900">Ticket Details</h3>
          <button
            onClick={onClose}
            className="text-slate-400 hover:text-slate-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Ticket Header */}
          <div className="mb-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h4 className="text-xl font-semibold text-slate-900 mb-2">{ticket.ticket.title}</h4>
                <div className="flex space-x-2 mb-2">
                  <Badge className={getStatusBadgeColor(ticket.ticket.status)}>
                    {ticket.ticket.status.replace('_', ' ')}
                  </Badge>
                  <Badge className={getPriorityBadgeColor(ticket.ticket.priority)}>
                    {ticket.ticket.priority}
                  </Badge>
                  {ticket.ticket.category && (
                    <Badge className="bg-purple-100 text-purple-800">
                      {ticket.ticket.category}
                    </Badge>
                  )}
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-slate-500">Created</div>
                <div className="text-sm font-medium text-slate-900">
                  {new Date(ticket.ticket.createdAt).toLocaleString()}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <div className="text-sm text-slate-500">Client</div>
                <div className="font-medium text-slate-900">{ticket.ticket.clientName}</div>
                <div className="text-sm text-slate-500">{ticket.ticket.clientEmail}</div>
              </div>
              <div>
                <div className="text-sm text-slate-500">Assigned To</div>
                <div className="font-medium text-slate-900">
                  {ticket.ticket.assignedAdminName || 'Unassigned'}
                </div>
              </div>
            </div>

            <div className="mb-4">
              <div className="text-sm text-slate-500 mb-2">Description</div>
              <div className="bg-slate-50 p-4 rounded-lg">
                <p className="text-slate-900 whitespace-pre-wrap">{ticket.ticket.description}</p>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex space-x-2 mb-6">
              {ticket.ticket.status === 'open' && (
                <Button onClick={() => onUpdateStatus(ticket.ticket.id, 'in_progress')}>
                  Start Working
                </Button>
              )}
              {ticket.ticket.status === 'in_progress' && (
                <Button onClick={() => onUpdateStatus(ticket.ticket.id, 'resolved')}>
                  Mark Resolved
                </Button>
              )}
              {ticket.ticket.status === 'resolved' && (
                <Button onClick={() => onUpdateStatus(ticket.ticket.id, 'closed')}>
                  Close Ticket
                </Button>
              )}
            </div>
          </div>

          {/* Messages */}
          <div className="mb-6">
            <h5 className="text-lg font-semibold text-slate-900 mb-4">Conversation</h5>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {ticket.messages.map((message: any) => (
                <div
                  key={message.id}
                  className={`p-4 rounded-lg ${
                    message.senderType === 'admin'
                      ? 'bg-blue-50 border-l-4 border-blue-500'
                      : 'bg-slate-50 border-l-4 border-slate-500'
                  }`}
                >
                  <div className="flex justify-between items-start mb-2">
                    <div className="font-medium text-slate-900">
                      {message.senderName} ({message.senderType})
                    </div>
                    <div className="text-sm text-slate-500">
                      {new Date(message.createdAt).toLocaleString()}
                    </div>
                  </div>
                  <p className="text-slate-700 whitespace-pre-wrap">{message.message}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Add Message Form */}
          <div>
            <h5 className="text-lg font-semibold text-slate-900 mb-4">Add Response</h5>
            <form onSubmit={handleAddMessage}>
              <div className="mb-4">
                <textarea
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Type your response..."
                  rows={4}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={onClose}>
                  Close
                </Button>
                <Button type="submit">
                  Send Response
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}

// Assign Ticket Modal Component
function AssignTicketModal({ ticket, availableAdmins, onClose, onAssign }: {
  ticket: any,
  availableAdmins: any[],
  onClose: () => void,
  onAssign: (ticketId: string, adminId: string) => void
}) {
  const [selectedAdmin, setSelectedAdmin] = useState('')

  const handleAssign = (e: React.FormEvent) => {
    e.preventDefault()
    if (selectedAdmin) {
      onAssign(ticket.id, selectedAdmin)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div className="flex justify-between items-center p-6 border-b border-slate-200">
          <h3 className="text-lg font-semibold text-slate-900">Assign Ticket</h3>
          <button
            onClick={onClose}
            className="text-slate-400 hover:text-slate-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleAssign} className="p-6">
          <div className="mb-4">
            <div className="text-sm text-slate-500 mb-2">Ticket</div>
            <div className="font-medium text-slate-900">{ticket.title}</div>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-slate-700 mb-2">
              Assign to Admin
            </label>
            <select
              value={selectedAdmin}
              onChange={(e) => setSelectedAdmin(e.target.value)}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            >
              <option value="">Select an admin...</option>
              {availableAdmins.map((admin) => (
                <option key={admin.id} value={admin.id}>
                  {admin.name} ({admin.role})
                </option>
              ))}
            </select>
          </div>

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              Assign Ticket
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
