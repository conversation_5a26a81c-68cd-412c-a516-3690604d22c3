# API Endpoints Specification - Hono.js

## 🚀 API Architecture Overview

RESTful API design using Hono.js method chaining with proper authentication and validation.

## 🔐 Authentication Strategy

```typescript
// JWT Authentication Middleware
const authMiddleware = async (c: Context, next: Next) => {
  const token = c.req.header('Authorization')?.replace('Bearer ', '');
  if (!token) return c.json({ error: 'Unauthorized' }, 401);
  
  try {
    const payload = await verify(token, JWT_SECRET);
    c.set('user', payload);
    await next();
  } catch {
    return c.json({ error: 'Invalid token' }, 401);
  }
};

// Role-based access control
const requireRole = (roles: string[]) => async (c: Context, next: Next) => {
  const user = c.get('user');
  if (!roles.includes(user.role)) {
    return c.json({ error: 'Forbidden' }, 403);
  }
  await next();
};
```

## 📋 API Endpoints by Portal

### 1. Landing Page API (Public)

```typescript
// Landing page routes - no authentication required
const landingRoutes = new Hono()
  .post('/leads', async (c) => {
    // Create new lead from contact form
    const body = await c.req.json();
    // Validation and database insert
    return c.json({ success: true, leadId: 'uuid' });
  })
  .post('/demo-booking', async (c) => {
    // Schedule demo booking
    const { leadId, scheduledDate, demoType } = await c.req.json();
    // Create demo booking record
    return c.json({ success: true, bookingId: 'uuid' });
  })
  .get('/pricing/:studentCount', async (c) => {
    // Calculate pricing for student count
    const studentCount = parseInt(c.req.param('studentCount'));
    const pricing = calculatePricing(studentCount);
    return c.json(pricing);
  });
```

**Endpoints:**
- `POST /api/leads` - Create lead from contact form
- `POST /api/demo-booking` - Schedule product demo
- `GET /api/pricing/:studentCount` - Get pricing calculation

### 2. Admin Dashboard API (Internal)

```typescript
// Admin routes - requires admin authentication
const adminRoutes = new Hono()
  .use('*', authMiddleware)
  .use('*', requireRole(['super_admin', 'sales', 'support', 'billing']))
  
  // Lead Management
  .get('/leads', async (c) => {
    const { status, page = 1, limit = 20 } = c.req.query();
    // Get paginated leads with filters
    return c.json({ leads, pagination });
  })
  .put('/leads/:id', async (c) => {
    // Update lead status and notes
    const leadId = c.req.param('id');
    const updates = await c.req.json();
    // Update lead record
    return c.json({ success: true });
  })
  .post('/leads/:id/convert', async (c) => {
    // Convert lead to client
    const leadId = c.req.param('id');
    const clientData = await c.req.json();
    // Create client record and subscription
    return c.json({ clientId: 'uuid' });
  })
  
  // Client Management
  .get('/clients', async (c) => {
    // Get all clients with pagination
    return c.json({ clients, pagination });
  })
  .get('/clients/:id', async (c) => {
    // Get client details with subscription info
    const clientId = c.req.param('id');
    return c.json({ client, subscription, billing });
  })
  .put('/clients/:id', async (c) => {
    // Update client information
    return c.json({ success: true });
  })
  
  // Billing Management
  .get('/billing/cycles', async (c) => {
    // Get billing cycles with filters
    return c.json({ billingCycles });
  })
  .post('/billing/generate-invoice', async (c) => {
    // Generate invoice for billing cycle
    const { billingCycleId } = await c.req.json();
    return c.json({ invoiceId: 'uuid' });
  })
  .get('/analytics/dashboard', async (c) => {
    // Get dashboard analytics
    return c.json({ 
      totalClients, 
      monthlyRevenue, 
      churnRate, 
      leadConversion 
    });
  });
```

**Key Admin Endpoints:**
- `GET /api/admin/leads` - Lead management with filters
- `POST /api/admin/leads/:id/convert` - Convert lead to client
- `GET /api/admin/clients` - Client management dashboard
- `POST /api/admin/billing/generate-invoice` - Invoice generation
- `GET /api/admin/analytics/dashboard` - Business analytics

### 3. School Portal API (Client)

```typescript
// School portal routes - requires client authentication
const schoolRoutes = new Hono()
  .use('*', authMiddleware)
  .use('*', requireRole(['client_admin', 'client_billing', 'client_viewer']))
  
  // Authentication
  .post('/auth/login', async (c) => {
    const { schoolCode, email, password } = await c.req.json();
    // Validate credentials and return JWT
    return c.json({ token, user });
  })
  
  // Subscription Management
  .get('/subscription', async (c) => {
    // Get current subscription details
    const clientId = c.get('user').clientId;
    return c.json({ subscription, plan, billing });
  })
  .put('/subscription/student-count', async (c) => {
    // Update student count
    const { studentCount } = await c.req.json();
    // Update subscription and calculate pro-rated billing
    return c.json({ success: true, newBilling });
  })
  
  // Payment Management
  .get('/payments/history', async (c) => {
    // Get payment history
    return c.json({ payments, invoices });
  })
  .post('/payments/create-order', async (c) => {
    // Create Razorpay order for payment
    const { invoiceId } = await c.req.json();
    const order = await createRazorpayOrder(invoiceId);
    return c.json({ orderId: order.id, amount: order.amount });
  })
  .post('/payments/verify', async (c) => {
    // Verify Razorpay payment
    const { paymentId, orderId, signature } = await c.req.json();
    const isValid = verifyRazorpaySignature(paymentId, orderId, signature);
    if (isValid) {
      // Update payment status and invoice
      return c.json({ success: true });
    }
    return c.json({ error: 'Payment verification failed' }, 400);
  })
  
  // Support System
  .get('/support/tickets', async (c) => {
    // Get client's support tickets
    return c.json({ tickets });
  })
  .post('/support/tickets', async (c) => {
    // Create new support ticket
    const ticketData = await c.req.json();
    return c.json({ ticketId: 'uuid' });
  })
  .post('/support/tickets/:id/messages', async (c) => {
    // Add message to ticket
    const ticketId = c.req.param('id');
    const { message } = await c.req.json();
    return c.json({ messageId: 'uuid' });
  });
```

**Key School Portal Endpoints:**
- `POST /api/school/auth/login` - School portal authentication
- `GET /api/school/subscription` - Subscription details
- `PUT /api/school/subscription/student-count` - Update student count
- `POST /api/school/payments/create-order` - Razorpay payment initiation
- `POST /api/school/support/tickets` - Create support ticket

## 🔄 Hono.js Method Chaining Examples

```typescript
// Main app with route grouping
const app = new Hono()
  .basePath('/api')
  .use('*', cors())
  .use('*', logger())
  
  // Public routes
  .route('/public', landingRoutes)
  
  // Protected routes
  .route('/admin', adminRoutes)
  .route('/school', schoolRoutes)
  
  // Health check
  .get('/health', (c) => c.json({ status: 'ok', timestamp: new Date() }))
  
  // Error handling
  .onError((err, c) => {
    console.error(err);
    return c.json({ error: 'Internal Server Error' }, 500);
  });

// Route chaining for related endpoints
const billingRoutes = new Hono()
  .get('/', getBillingCycles)
  .post('/generate', generateInvoice)
  .get('/:id', getBillingCycleDetails)
  .put('/:id', updateBillingCycle)
  .delete('/:id', cancelBillingCycle);
```

## 📊 Request/Response Schemas

### Lead Creation
```typescript
// POST /api/leads
interface CreateLeadRequest {
  email: string;
  schoolName: string;
  contactPerson: string;
  phone?: string;
  estimatedStudents: number;
  source: 'landing_page' | 'referral' | 'demo_request';
}

interface CreateLeadResponse {
  success: boolean;
  leadId: string;
  message: string;
}
```

### Client Conversion
```typescript
// POST /api/admin/leads/:id/convert
interface ConvertLeadRequest {
  schoolCode: string;
  actualStudentCount: number;
  planId: string;
  startDate: string;
  contactDetails: {
    email: string;
    phone: string;
    address: string;
  };
}
```

### Payment Processing
```typescript
// POST /api/school/payments/create-order
interface CreatePaymentOrderRequest {
  invoiceId: string;
}

interface CreatePaymentOrderResponse {
  orderId: string;
  amount: number;
  currency: string;
  key: string; // Razorpay key
}
```

## 🛡️ Security & Validation

### Input Validation with Zod
```typescript
import { z } from 'zod';

const createLeadSchema = z.object({
  email: z.string().email(),
  schoolName: z.string().min(2).max(255),
  contactPerson: z.string().min(2).max(255),
  phone: z.string().optional(),
  estimatedStudents: z.number().min(1).max(10000),
  source: z.enum(['landing_page', 'referral', 'demo_request'])
});

// Validation middleware
const validateBody = (schema: z.ZodSchema) => async (c: Context, next: Next) => {
  try {
    const body = await c.req.json();
    const validatedData = schema.parse(body);
    c.set('validatedData', validatedData);
    await next();
  } catch (error) {
    return c.json({ error: 'Validation failed', details: error.errors }, 400);
  }
};
```

### Rate Limiting
```typescript
const rateLimiter = async (c: Context, next: Next) => {
  const clientIP = c.req.header('x-forwarded-for') || 'unknown';
  // Implement rate limiting logic
  await next();
};
```

## 📈 Billing Calculation Logic

### Pro-rated Billing Implementation
```typescript
const calculateProRatedBilling = (
  startDate: Date,
  studentCount: number,
  pricePerStudent: number
) => {
  const now = new Date();
  const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
  const daysInMonth = endOfMonth.getDate();
  const remainingDays = daysInMonth - startDate.getDate() + 1;

  const dailyRate = pricePerStudent / daysInMonth;
  const proratedAmount = dailyRate * remainingDays * studentCount;

  return {
    baseAmount: pricePerStudent * studentCount,
    proratedAmount: Math.round(proratedAmount * 100) / 100,
    remainingDays,
    daysInMonth,
    nextBillingDate: new Date(now.getFullYear(), now.getMonth() + 1, 1)
  };
};
```

This API design ensures clean separation of concerns, proper authentication, and scalable architecture using Hono.js method chaining patterns.
