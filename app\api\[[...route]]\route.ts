import { handle } from "hono/vercel"
import { Hono } from "hono"
import { cors } from "hono/cors"
import { logger } from "hono/logger"

// Import route modules
import leadsRoutes from "./leads"
import demoRoutes from "./demo"
import pricingRoutes from "./pricing"
import adminRoutes from "./admin"
import schoolRoutes from "./school"
import aiChatRoutes from "./ai-chat"
import authRoutes from "./auth"
import schedulerRoutes from "./scheduler"
import paymentsRoutes from "./payments"
import webhooksRoutes from "./webhooks"

export const runtime = "nodejs"

// Create main Hono app with base path
const app = new Hono().basePath("/api")

// Global middleware
app.use("*", cors({
  origin: ["http://localhost:3000", "https://schopio.com","https://schopio.vercel.app"],
  allowHeaders: ["Content-Type", "Authorization"],
  allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  credentials: true,
}))

app.use("*", logger())

// Health check endpoint
app.get("/health", (c) => {
  return c.json({ 
    status: "ok", 
    timestamp: new Date().toISOString(),
    service: "Schopio API",
    version: "1.0.0"
  })
})

// Route registration using method chaining
const routes = app
  .route("/leads", leadsRoutes)
  .route("/demo", demoRoutes)
  .route("/pricing", pricingRoutes)
  .route("/admin", adminRoutes)
  .route("/school", schoolRoutes)
  .route("/ai-chat", aiChatRoutes)
  .route("/auth", authRoutes)
  .route("/scheduler", schedulerRoutes)
  .route("/payments", paymentsRoutes)
  .route("/webhooks", webhooksRoutes)

// Global error handler
app.onError((err, c) => {
  console.error("API Error:", err)
  
  // Return appropriate error response
  if (err.message.includes("validation")) {
    return c.json({ 
      error: "Validation failed", 
      details: err.message 
    }, 400)
  }
  
  if (err.message.includes("unauthorized")) {
    return c.json({ 
      error: "Unauthorized access" 
    }, 401)
  }
  
  if (err.message.includes("not found")) {
    return c.json({ 
      error: "Resource not found" 
    }, 404)
  }
  
  return c.json({ 
    error: "Internal server error",
    message: process.env.NODE_ENV === "development" ? err.message : "Something went wrong"
  }, 500)
})

// 404 handler for unmatched routes
app.notFound((c) => {
  return c.json({ 
    error: "Endpoint not found",
    path: c.req.path,
    method: c.req.method
  }, 404)
})

// Export handlers for Next.js App Router
export const GET = handle(app)
export const POST = handle(app)
export const PUT = handle(app)
export const DELETE = handle(app)
export const PATCH = handle(app)
export const OPTIONS = handle(app)

// Export type for client-side usage with full type safety
export type AppType = typeof routes
