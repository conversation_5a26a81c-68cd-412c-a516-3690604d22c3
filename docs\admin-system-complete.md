# 🏢 Schopio Admin System - Complete Implementation & Management Guide

## 📋 **SYSTEM OVERVIEW**

The Schopio Admin System is a comprehensive administrative interface for managing the complete Schopio SaaS ecosystem including leads, clients, partners, revenue sharing, billing, and operational expenses. It provides secure access for admin users to manage all aspects of the business from lead generation to partner payouts.

## 🎯 **CORE BUSINESS WORKFLOW**

### **Lead to Revenue Flow:**
```
Lead Generation → Demo/Production Request → Client Conversion → Subscription Setup → Partner Revenue Sharing → Monthly Payouts
```

### **Partner Revenue Model:**
1. **School pays subscription** (e.g., ₹50,000/month)
2. **Admin deducts operational expenses** (database, maintenance, support)
3. **Net profit calculated** = Revenue - Expenses
4. **Partner earns commission** = Net Profit × Share Percentage (35-50%)
5. **Monthly withdrawal** processed by admin via NEFT

## 🔐 **SECURITY FEATURES**
- **No Public Access**: Admin login only via manual URL entry (`/admin/login`)
- **JWT Authentication**: Secure token-based auth with 24-hour expiration
- **Role-Based Access Control (RBAC)**: Four admin roles with hierarchical permissions
- **Permission-Based Authorization**: Granular permissions for different operations
- **Password Security**: bcrypt hashing with salt rounds of 12
- **Security Headers**: CORS, rate limiting, and security middleware
- **Audit Trails**: Complete logging of all admin actions and financial transactions

## 👥 **ADMIN ROLES & PERMISSIONS**

### 1. **Super Admin (`super_admin`)**
- **Complete System Control**: All permissions across all modules
- **Partner Management**: Create, edit, delete partners and manage referral codes
- **Financial Control**: Set operational expenses, profit sharing percentages
- **User Management**: Create, edit, delete other admin users
- **Revenue Management**: Approve withdrawals, process NEFT transfers
- **System Configuration**: Access to all system settings and configurations
- **Permissions**: `*` (all permissions)

### 2. **Sales (`sales`)**
- **Lead Management**: Full access to lead data and conversion tracking
- **Client Onboarding**: Convert leads to clients, set subscription amounts
- **Partner Relations**: View partner performance, manage referral attributions
- **Demo Management**: Schedule and track demo requests
- **Permissions**: `leads:*`, `clients:*`, `partners:read`, `demos:*`, `requests:write`

### 3. **Support (`support`)**
- **Client Support**: Access to client data for support purposes
- **Request Management**: Handle software requests and technical issues
- **Partner Support**: Assist partners with account and payout issues
- **Ticket Management**: Handle support tickets and client communications
- **Permissions**: `clients:read`, `requests:*`, `partners:read`, `support:*`

### 4. **Billing (`billing`)**
- **Financial Management**: Access to billing and payment information
- **Expense Management**: Set and update operational expenses
- **Withdrawal Processing**: Review and process partner withdrawal requests
- **Revenue Tracking**: Monitor subscription payments and partner earnings
- **Permissions**: `clients:read`, `requests:read`, `billing:*`, `withdrawals:*`, `expenses:*`

## 🌐 **ACCESS URLS**

### **Admin Login**
```
URL: /admin/login
Method: Manual URL entry (no public buttons for security)
Authentication: Email + Password
Default Credentials: <EMAIL> / Admin@123456
```

### **Admin Dashboard**
```
URL: /admin/dashboard
Method: Automatic redirect after login
Authentication: JWT Token required
Session: 24-hour expiration
```

## 🎛️ **ADMIN DASHBOARD FEATURES**

### **1. Overview Tab - Business Analytics**
- **Revenue Metrics**: Total monthly revenue, partner payouts, net profit
- **Client Statistics**: Active clients, pending implementations, churned clients
- **Partner Performance**: Top performing partners, total referrals, conversion rates
- **Recent Activity**: Latest software requests, new client onboardings, withdrawal requests
- **Financial Summary**: Monthly expense breakdown, profit margins, growth trends

### **2. Leads Management Tab**
- **Lead Pipeline**: Visual pipeline from inquiry to conversion
- **Search & Filters**:
  - By school name, email, contact person
  - By lead status (new, contacted, demo_scheduled, converted, lost)
  - By source (landing_page, referral, partner)
  - By date range and estimated student count
- **Bulk Actions**: Export leads, bulk status updates, mass email campaigns
- **Lead Details**: Complete lead information with interaction history
- **Demo Scheduling**: Calendar integration for demo bookings
- **Conversion Tracking**: Lead to client conversion rates and analytics

### **3. Software Requests Tab**
- **Request Types**: Demo requests vs Production requests
- **Status Management**:
  - Pending → Under Review → Approved → Implementation → Activated
  - Rejection workflow with reason tracking
- **Client Conversion Process**:
  - View average fee and student count
  - Set monthly subscription amount manually
  - Move to "Pending Client/Implementation" tab
  - Track implementation progress
- **Filters**: By request type, status, date range, school size
- **Approval Workflow**: Multi-step approval with admin notes

### **4. Clients Management Tab**
- **Active Clients**: All converted and active school clients
- **Client Details**: School information, subscription details, payment history
- **Subscription Management**:
  - View current plans and billing cycles
  - Modify subscription amounts
  - Handle plan upgrades/downgrades
- **Partner Attribution**: See which partner referred each client
- **Payment Tracking**: Monitor payment status and overdue accounts
- **Support Integration**: Access to client support tickets and communications

### **5. Partners Management Tab** 🤝
- **Partner Directory**: Complete list of all registered partners
- **Partner Creation**: Admin-only partner registration with unique referral codes
- **Referral Code Management**:
  - Generate unique 8-character alphanumeric codes
  - Track code usage and attribution
  - Deactivate/reactivate codes
- **Partner Performance Metrics**:
  - Total schools referred
  - Conversion rates (demo to production)
  - Monthly earnings and lifetime value
  - Active vs inactive referrals
- **School Attribution**:
  - **"View Schools" button** in partner list
  - See all schools referred by each partner
  - Track referral source and date
- **Revenue Sharing Configuration**:
  - Set individual profit sharing percentages (35-50%)
  - Configure global default percentages
  - Track earnings calculations

### **6. Financial Management Tab** 💰
- **Operational Expenses Configuration**:
  - Database hosting charges per school
  - Website maintenance costs
  - Support infrastructure expenses
  - Custom expense categories
  - Percentage-based or fixed amount expenses
- **Revenue Calculation Engine**:
  - Real-time profit calculations
  - Expense deduction tracking
  - Partner earnings computation
- **Withdrawal Management**:
  - Monthly withdrawal request queue
  - Approval workflow with admin review
  - NEFT transfer processing
  - Transaction ID and receipt upload
  - Payment status tracking (pending → approved → paid)

### **7. Expense Management Tab**
- **Expense Categories**:
  - Database Hosting: ₹X per school per month
  - Website Maintenance: ₹X per month (distributed across schools)
  - Support Costs: ₹X per school per month
  - Infrastructure: ₹X per month (distributed)
  - Custom Categories: Admin-configurable
- **Expense Calculator**: Real-time calculation of total expenses per school
- **Profit Margin Analysis**: Visual breakdown of revenue vs expenses vs partner payouts

## 🔗 **COMPLETE API ENDPOINTS**

### **Authentication & Security**
- `POST /api/admin/login` - Admin login with email/password
- `POST /api/admin/logout` - Admin logout (token invalidation)
- `POST /api/admin/refresh-token` - Refresh JWT token

### **Dashboard Analytics**
- `GET /api/admin/dashboard` - Complete dashboard statistics and metrics
- `GET /api/admin/analytics/revenue` - Revenue analytics and trends
- `GET /api/admin/analytics/partners` - Partner performance analytics

### **Lead Management**
- `GET /api/admin/leads` - List all leads with advanced filtering
- `GET /api/admin/leads/:id` - Get specific lead details
- `PUT /api/admin/leads/:id` - Update lead information and status
- `POST /api/admin/leads/:id/convert` - Convert lead to client
- `GET /api/admin/leads/analytics` - Lead conversion analytics

### **Software Request Management**
- `GET /api/admin/software-requests` - List all software requests with filters
- `GET /api/admin/software-requests/:id` - Get specific request details
- `PUT /api/admin/software-requests/:id/status` - Update request status
- `POST /api/admin/software-requests/:id/convert-to-client` - Convert to client with subscription
- `GET /api/admin/software-requests/pending-implementation` - Get pending implementations

### **Client Management**
- `GET /api/admin/clients` - List all clients with filtering and pagination
- `GET /api/admin/clients/:id` - Get specific client details
- `PUT /api/admin/clients/:id` - Update client information
- `GET /api/admin/clients/:id/subscription` - Get client subscription details
- `PUT /api/admin/clients/:id/subscription` - Update subscription amount
- `GET /api/admin/clients/by-partner/:partnerId` - Get clients referred by specific partner

### **Partner Management** 🤝
- `GET /api/admin/partners` - List all partners with performance metrics
- `POST /api/admin/partners` - Create new partner (admin only)
- `GET /api/admin/partners/:id` - Get specific partner details
- `PUT /api/admin/partners/:id` - Update partner information
- `DELETE /api/admin/partners/:id` - Deactivate partner
- `GET /api/admin/partners/:id/schools` - Get all schools referred by partner
- `GET /api/admin/partners/:id/earnings` - Get partner earnings breakdown
- `PUT /api/admin/partners/:id/profit-share` - Update profit sharing percentage
- `POST /api/admin/partners/:id/referral-codes` - Generate new referral code
- `PUT /api/admin/referral-codes/:id/status` - Activate/deactivate referral code

### **Financial Management** 💰
- `GET /api/admin/expenses` - List all operational expense categories
- `POST /api/admin/expenses` - Create new expense category
- `PUT /api/admin/expenses/:id` - Update expense category
- `DELETE /api/admin/expenses/:id` - Remove expense category
- `GET /api/admin/revenue/calculations` - Get revenue calculations for all clients
- `GET /api/admin/revenue/partner-earnings` - Get partner earnings summary

### **Withdrawal Management**
- `GET /api/admin/withdrawals` - List all withdrawal requests
- `GET /api/admin/withdrawals/pending` - Get pending withdrawal requests
- `PUT /api/admin/withdrawals/:id/approve` - Approve withdrawal request
- `PUT /api/admin/withdrawals/:id/reject` - Reject withdrawal request with reason
- `PUT /api/admin/withdrawals/:id/mark-paid` - Mark as paid with transaction details
- `POST /api/admin/withdrawals/:id/upload-receipt` - Upload payment receipt

### **Admin User Management** (Super Admin Only)
- `GET /api/admin/users` - List all admin users
- `POST /api/admin/users` - Create new admin user
- `PUT /api/admin/users/:id` - Update admin user
- `DELETE /api/admin/users/:id` - Delete admin user
- `PUT /api/admin/users/:id/permissions` - Update user permissions

## Dashboard Features

### Overview Tab
- **Analytics Cards**: Total leads, clients, users, and software requests
- **Recent Activity**: Latest leads and software requests
- **Quick Stats**: Real-time system metrics

### Leads Management
- **Search & Filter**: Search by school name, email, or contact person
- **Status Filtering**: Filter by lead status (new, contacted, qualified, converted, lost)
- **Bulk Actions**: Export leads data
- **Lead Details**: View and edit individual lead information

### Clients Management
- **Search & Filter**: Search by school name, code, or contact information
- **Status Filtering**: Filter by client status (active, inactive, suspended)
- **Onboarding Status**: Track client onboarding progress
- **Client Details**: View and edit client information

### Software Requests Management
- **Status Management**: Update request status through dropdown
- **Type Filtering**: Filter by request type (demo, production)
- **Request Details**: View complete request information
- **Approval Workflow**: Manage request approval process

## Database Setup

### 1. Apply Schema Changes
```bash
bunx drizzle-kit push
```

### 2. Seed Admin Users
```bash
# Create default super admin
npm run seed:admin

# Create all admin roles (optional)
npm run seed:admin:all
```

### Default Admin Credentials
```
Email: <EMAIL>
Password: Admin@123
Role: super_admin
```

## Frontend Components

### Admin Login Page (`/app/admin/login/page.tsx`)
- **Secure Design**: Dark theme with security-focused UI
- **Form Validation**: Email and password validation
- **Error Handling**: Clear error messages for failed login attempts
- **Responsive**: Mobile-friendly design

### Admin Dashboard (`/app/admin/dashboard/page.tsx`)
- **Tabbed Interface**: Overview, Leads, Clients, Requests tabs
- **Real-time Data**: Live dashboard statistics
- **Interactive Tables**: Sortable and filterable data tables
- **Status Management**: Inline status updates for requests

## Security Implementation

### JWT Token Structure
```json
{
  "adminId": "uuid",
  "email": "<EMAIL>",
  "role": "super_admin",
  "permissions": ["*"],
  "type": "admin",
  "iat": 1234567890,
  "exp": 1234654290
}
```

### Middleware Protection
- **Authentication Check**: Verify JWT token on all admin routes
- **Permission Validation**: Check specific permissions for each endpoint
- **Rate Limiting**: Prevent brute force attacks
- **CORS Configuration**: Secure cross-origin requests

## Usage Instructions

### 1. Initial Setup
1. Ensure database schema is applied: `bunx drizzle-kit push`
2. Seed admin users: `npm run seed:admin`
3. Build the application: `npm run build`
4. Start the server: `npm run start`

### 2. Admin Access
1. Navigate to `/admin/login` (manual URL entry)
2. Login with admin credentials
3. Access dashboard at `/admin/dashboard`

### 3. Managing Data
- **Leads**: Use the Leads tab to track and convert prospects
- **Clients**: Monitor client status and onboarding progress
- **Requests**: Process software requests and update statuses
- **Users**: (Super Admin only) Manage admin user accounts

## Development Notes

### File Structure
```
app/
├── admin/
│   ├── login/
│   │   └── page.tsx          # Admin login page
│   └── dashboard/
│       └── page.tsx          # Admin dashboard
├── api/
│   └── [[...route]]/
│       └── admin.ts          # Admin API endpoints
src/
├── middleware/
│   └── admin-auth.ts         # Admin authentication middleware
├── db/
│   └── schema.ts             # Database schema with admin tables
└── components/
    └── admin/                # Admin-specific components
```

## 🗄️ **COMPLETE DATABASE SCHEMA FOR PARTNER SYSTEM**

### **Partners Table** 🤝
```sql
CREATE TABLE partners (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  phone VARCHAR(20),
  company_name VARCHAR(255),
  address TEXT,
  profit_share_percentage DECIMAL(5,2) DEFAULT 40.00, -- 35-50%
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### **Referral Codes Table**
```sql
CREATE TABLE referral_codes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  partner_id UUID REFERENCES partners(id) ON DELETE CASCADE,
  code VARCHAR(8) UNIQUE NOT NULL, -- 8-character alphanumeric
  is_active BOOLEAN DEFAULT true,
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  deactivated_at TIMESTAMP
);
```

### **School Referrals Table**
```sql
CREATE TABLE school_referrals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  school_id UUID REFERENCES clients(id),
  partner_id UUID REFERENCES partners(id),
  referral_code_id UUID REFERENCES referral_codes(id),
  referral_date TIMESTAMP DEFAULT NOW(),
  conversion_date TIMESTAMP, -- When school became paying client
  is_converted BOOLEAN DEFAULT false,
  attribution_locked BOOLEAN DEFAULT false -- Prevents changes once set
);
```

### **Operational Expenses Table**
```sql
CREATE TABLE operational_expenses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  category VARCHAR(100) NOT NULL, -- 'database', 'maintenance', 'support', etc.
  description TEXT,
  amount_type expense_type NOT NULL, -- 'per_school_monthly', 'fixed_monthly', 'percentage'
  amount DECIMAL(10,2) NOT NULL,
  percentage DECIMAL(5,2), -- For percentage-based expenses
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### **Partner Earnings Table**
```sql
CREATE TABLE partner_earnings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  partner_id UUID REFERENCES partners(id),
  school_id UUID REFERENCES clients(id),
  billing_cycle_id UUID REFERENCES billing_cycles(id),
  gross_revenue DECIMAL(10,2) NOT NULL, -- School's payment amount
  total_expenses DECIMAL(10,2) NOT NULL, -- Calculated operational expenses
  net_profit DECIMAL(10,2) NOT NULL, -- gross_revenue - total_expenses
  partner_share_percentage DECIMAL(5,2) NOT NULL,
  partner_earning DECIMAL(10,2) NOT NULL, -- net_profit * partner_share_percentage
  calculation_date TIMESTAMP DEFAULT NOW(),
  is_paid BOOLEAN DEFAULT false
);
```

### **Withdrawal Requests Table**
```sql
CREATE TABLE withdrawal_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  partner_id UUID REFERENCES partners(id),
  requested_amount DECIMAL(10,2) NOT NULL,
  available_balance DECIMAL(10,2) NOT NULL, -- Balance at time of request
  request_month VARCHAR(7) NOT NULL, -- 'YYYY-MM' format
  status withdrawal_status DEFAULT 'pending', -- 'pending', 'approved', 'rejected', 'paid'
  bank_details JSONB, -- Partner's bank account details
  admin_notes TEXT,
  rejection_reason TEXT,
  neft_transaction_id VARCHAR(100),
  payment_receipt_url TEXT,
  processed_by UUID REFERENCES admin_users(id),
  requested_at TIMESTAMP DEFAULT NOW(),
  processed_at TIMESTAMP
);
```

### **Partner Transactions Table** (Audit Trail)
```sql
CREATE TABLE partner_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  partner_id UUID REFERENCES partners(id),
  transaction_type transaction_type NOT NULL, -- 'earning', 'withdrawal', 'adjustment'
  amount DECIMAL(10,2) NOT NULL,
  description TEXT,
  reference_id UUID, -- Links to earning or withdrawal record
  balance_before DECIMAL(10,2) NOT NULL,
  balance_after DECIMAL(10,2) NOT NULL,
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP DEFAULT NOW()
);
```

## 💼 **PARTNER MANAGEMENT WORKFLOW**

### **1. Partner Registration (Admin Only)**
```
Admin Dashboard → Partners Tab → "Create Partner" Button → Form Submission → Referral Code Generation → Partner Account Created
```

**Process:**
1. **Admin Access**: Only admins can create partner accounts
2. **Partner Details**: Name, email, company, contact information
3. **Profit Share**: Set individual profit sharing percentage (35-50%)
4. **Referral Code**: System generates unique 8-character code
5. **Account Activation**: Partner receives login credentials

### **2. School Referral Attribution**
```
School Registration → Referral Code Entry → Permanent Attribution → Revenue Tracking → Partner Earnings
```

**Process:**
1. **Code Application**: School enters referral code during registration or in profile
2. **Validation**: System validates code and checks partner status
3. **Attribution Lock**: Once applied, school-partner relationship is permanent
4. **Conversion Tracking**: Monitor when school becomes paying client

### **3. Revenue Calculation Engine**
```
School Payment → Expense Deduction → Net Profit Calculation → Partner Share → Earnings Credit
```

**Example Calculation:**
```javascript
// Monthly calculation for each school
const schoolPayment = 50000; // ₹50,000
const operationalExpenses = 8000; // ₹8,000 (database, maintenance, support)
const netProfit = schoolPayment - operationalExpenses; // ₹42,000
const partnerSharePercentage = 40; // 40%
const partnerEarning = netProfit * (partnerSharePercentage / 100); // ₹16,800
```

### **4. Monthly Withdrawal Process**
```
Partner Request → Admin Review → Approval → NEFT Transfer → Receipt Upload → Status Update
```

**Process:**
1. **Monthly Request**: Partners can request withdrawal once per month
2. **Balance Verification**: System checks available balance
3. **Admin Approval**: Admin reviews and approves/rejects request
4. **NEFT Transfer**: Admin processes bank transfer manually
5. **Documentation**: Upload transaction ID and receipt
6. **Status Update**: Mark as "Paid" with complete audit trail

## 🎛️ **ENHANCED ADMIN DASHBOARD FEATURES**

### **Partners Management Tab** 🤝
- **Partner Directory**: Complete list with performance metrics
- **"View Schools" Button**: See all schools referred by each partner
- **Earnings Tracker**: Real-time earnings and withdrawal history
- **Referral Code Management**: Generate, activate, deactivate codes
- **Performance Analytics**: Conversion rates, revenue attribution
- **Profit Share Configuration**: Individual percentage settings

### **Financial Management Tab** 💰
- **Expense Configuration**: Set operational costs per category
- **Revenue Calculator**: Real-time profit calculations
- **Withdrawal Queue**: Pending requests with approval workflow
- **NEFT Processing**: Transaction ID and receipt management
- **Audit Trail**: Complete financial transaction history

### **Advanced Filtering & Search**
- **Partner Performance**: Filter by earnings, conversion rates
- **School Attribution**: View schools by partner, revenue range
- **Financial Status**: Filter withdrawals by status, amount
- **Date Ranges**: Custom date filtering for all metrics
- **Export Options**: CSV/Excel export for all data

## 🔐 **SECURITY & COMPLIANCE**

### **Financial Security**
- **Double-Entry Bookkeeping**: All transactions with audit trails
- **Fraud Prevention**: IP tracking, duplicate referral detection
- **Withdrawal Limits**: Maximum monthly amounts per partner
- **Multi-Step Approval**: Admin verification for all payouts
- **Encrypted Storage**: All financial data encrypted at rest

### **Data Protection**
- **PII Encryption**: Partner and financial data protection
- **Access Logging**: Complete audit trail of admin actions
- **Role Segregation**: Financial operations restricted by role
- **Backup Security**: Encrypted backups with retention policies
- **Compliance**: GDPR and financial regulation adherence

## 📊 **BUSINESS INTELLIGENCE**

### **Partner Analytics**
- **Top Performers**: Highest earning partners by period
- **Conversion Metrics**: Demo to production rates by partner
- **Revenue Attribution**: Total revenue generated per partner
- **Geographic Performance**: Partner distribution and success
- **Growth Trends**: Partner acquisition and performance over time

### **Financial Analytics**
- **Revenue Breakdown**: Gross vs net vs partner payouts
- **Profit Margins**: Net profit percentages and trends
- **Expense Analysis**: Cost per school optimization
- **ROI Analysis**: Partner program return on investment
- **Cash Flow**: Monthly inflows vs outflows including payouts

This comprehensive admin system provides complete control over the Schopio platform including the sophisticated partner referral system with transparent revenue sharing and financial management.
│   └── admin-auth.ts         # Admin authentication middleware
└── db/
    └── schema.ts             # Database schema with admin tables
scripts/
└── seed-admin.ts             # Admin user seeding script
docs/
└── admin-system-complete.md # This documentation
```

### Key Technologies
- **Next.js 15**: React framework with App Router
- **Hono.js**: API framework with method chaining
- **Drizzle ORM**: Type-safe database queries
- **JWT**: Secure token-based authentication
- **bcryptjs**: Password hashing
- **Tailwind CSS**: Styling framework
- **Framer Motion**: Animations and transitions

## Troubleshooting

### Common Issues
1. **Build Errors**: Ensure all TypeScript types are properly defined
2. **Authentication Failures**: Check JWT token expiration and format
3. **Permission Denied**: Verify admin role and permissions
4. **Database Errors**: Ensure schema is up to date with `bunx drizzle-kit push`

### Support
For technical support or questions about the admin system, contact the development team or refer to the main project documentation.

---

**Status**: ✅ Complete and Production Ready
**Last Updated**: 2025-07-04
**Version**: 1.0.0
