import { Hono } from 'hono'
import { db } from '@/src/db'
import { payments, invoices, subscriptions, clients, schoolReferrals, partners, operationalExpenses, partnerEarnings, partnerTransactions, billingCycles, webhookIdempotency } from '@/src/db/schema'
import { eq, and, sql } from 'drizzle-orm'
import { RazorpayService } from '@/src/services/razorpayService'

const app = new Hono()

// Initialize Razorpay service
const razorpayService = new RazorpayService({
  keyId: process.env.RAZORPAY_KEY_ID!,
  keySecret: process.env.RAZORPAY_KEY_SECRET!,
  webhookSecret: process.env.RAZORPAY_WEBHOOK_SECRET!
})

/**
 * Verify Razorpay webhook signature using the service
 */
function verifyWebhookSignature(body: string, signature: string): boolean {
  return razorpayService.verifyWebhookSignature(body, signature)
}

/**
 * Generate idempotency key for webhook processing
 */
function generateIdempotencyKey(webhookData: any): string {
  const { event, payload } = webhookData

  if (event.startsWith('payment.')) {
    const payment = payload.payment?.entity || payload.payment
    return `razorpay_${event}_${payment.id}_${payment.order_id}`
  }

  if (event.startsWith('subscription.')) {
    const subscription = payload.subscription?.entity || payload.subscription
    return `razorpay_${event}_${subscription.id}`
  }

  // Fallback for other events
  return `razorpay_${event}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Check and record webhook idempotency to prevent duplicate processing
 */
async function checkWebhookIdempotency(webhookData: any): Promise<{ shouldProcess: boolean; existingResult?: any }> {
  const idempotencyKey = generateIdempotencyKey(webhookData)

  try {
    // Check if this webhook has already been processed
    const [existingRecord] = await db.select()
      .from(webhookIdempotency)
      .where(eq(webhookIdempotency.idempotencyKey, idempotencyKey))
      .limit(1)

    if (existingRecord) {
      console.log('Webhook already processed:', {
        idempotencyKey,
        processedAt: existingRecord.processedAt,
        event: webhookData.event
      })

      return {
        shouldProcess: false,
        existingResult: existingRecord.processingResult
      }
    }

    // Record this webhook for idempotency tracking
    await db.insert(webhookIdempotency).values({
      idempotencyKey,
      webhookSource: 'razorpay',
      eventType: webhookData.event,
      webhookData: webhookData,
      processedAt: new Date()
    })

    console.log('Webhook recorded for processing:', {
      idempotencyKey,
      event: webhookData.event
    })

    return { shouldProcess: true }

  } catch (error) {
    console.error('Error checking webhook idempotency:', error)
    // If idempotency check fails, allow processing but log the error
    return { shouldProcess: true }
  }
}

/**
 * Update webhook processing result
 */
async function updateWebhookResult(webhookData: any, result: any): Promise<void> {
  const idempotencyKey = generateIdempotencyKey(webhookData)

  try {
    await db.update(webhookIdempotency)
      .set({
        processingResult: result
      })
      .where(eq(webhookIdempotency.idempotencyKey, idempotencyKey))

    console.log('Webhook result updated:', {
      idempotencyKey,
      success: result.success
    })
  } catch (error) {
    console.error('Error updating webhook result:', error)
  }
}

/**
 * Handle payment success webhook
 */
async function handlePaymentSuccess(paymentData: any) {
  try {
    const { payment } = paymentData

    // Use database transaction to ensure atomicity
    const result = await db.transaction(async (tx) => {
      // Find the invoice by order ID
      const [invoice] = await tx.select()
        .from(invoices)
        .where(eq(invoices.razorpayOrderId, payment.order_id))
        .limit(1)

      if (!invoice) {
        console.error('Invoice not found for order ID:', payment.order_id)
        throw new Error('Invoice not found')
      }

      // Check if payment already exists (idempotency check)
      const [existingPayment] = await tx.select()
        .from(payments)
        .where(eq(payments.razorpayPaymentId, payment.id))
        .limit(1)

      if (existingPayment) {
        console.log('Payment already processed:', payment.id)
        return { payment: existingPayment, isExisting: true }
      }

      // Create payment record
      const [newPayment] = await tx.insert(payments).values({
        invoiceId: invoice.id,
        clientId: invoice.clientId,
        razorpayPaymentId: payment.id,
        razorpayOrderId: payment.order_id,
        amount: (payment.amount / 100).toString(), // Convert from paise
        currency: payment.currency,
        status: 'success',
        paymentMethod: payment.method,
        processedAt: new Date(payment.created_at * 1000)
      }).returning()

      // Update invoice status
      await tx.update(invoices)
        .set({
          status: 'paid',
          paidDate: new Date(payment.created_at * 1000).toISOString().split('T')[0] // Convert to date string
        })
        .where(eq(invoices.id, invoice.id))

      // Update billing cycle status if applicable
      if (invoice.billingCycleId) {
        await tx.update(billingCycles)
          .set({ status: 'paid' })
          .where(eq(billingCycles.id, invoice.billingCycleId))
      }

      // Check for partner attribution and calculate earnings (only if invoice has clientId)
      if (!invoice.clientId) {
        console.log('Payment processed successfully (no client attribution):', {
          paymentId: newPayment.id,
          invoiceId: invoice.id,
          amount: newPayment.amount
        })
        return { payment: newPayment, isExisting: false }
      }

      const [partnerAttribution] = await tx
      .select({
        partnerId: schoolReferrals.partnerId,
        partnerName: partners.name,
        partnerCode: partners.partnerCode,
        profitSharePercentage: partners.profitSharePercentage,
        clientName: clients.schoolName
      })
      .from(schoolReferrals)
      .leftJoin(partners, eq(schoolReferrals.partnerId, partners.id))
      .leftJoin(clients, eq(schoolReferrals.clientId, clients.id))
      .where(and(
        eq(schoolReferrals.clientId, invoice.clientId),
        eq(schoolReferrals.isActive, true),
        eq(partners.isActive, true)
      ))
      .limit(1)

    if (partnerAttribution) {
      try {
        // Calculate partner earnings
        const grossAmount = parseFloat(newPayment.amount)

        // Get operational expenses (general expenses that apply to all clients)
        const expenses = await tx.select()
          .from(operationalExpenses)
          .where(eq(operationalExpenses.isActive, true))

        const totalExpenses = expenses.reduce((sum, expense) => {
          if (expense.amountPerSchool) {
            return sum + parseFloat(expense.amountPerSchool)
          }
          return sum
        }, 0)
        const netProfit = Math.max(0, grossAmount - totalExpenses)

        // Use partner-specific profit share or default 40%
        const partnerSharePercentage = parseFloat(partnerAttribution.profitSharePercentage?.toString() || '40')
        const partnerEarning = (netProfit * partnerSharePercentage) / 100

        if (partnerEarning > 0) {
          // Create partner earning record
          const [newEarning] = await tx.insert(partnerEarnings).values({
            partnerId: partnerAttribution.partnerId,
            clientId: invoice.clientId,
            invoiceId: invoice.id,
            paymentId: newPayment.id,
            grossAmount: grossAmount.toString(),
            totalExpenses: totalExpenses.toString(),
            netProfit: netProfit.toString(),
            partnerSharePercentage: partnerSharePercentage.toString(),
            partnerEarning: partnerEarning.toString(),
            status: 'available'
          }).returning()

          // Get current partner balance
          const [balanceResult] = await tx
            .select({
              balance: sql<number>`COALESCE(SUM(CASE
                WHEN ${partnerTransactions.transactionType} IN ('EARNING', 'BONUS') THEN ${partnerTransactions.amount}
                WHEN ${partnerTransactions.transactionType} IN ('WITHDRAWAL', 'PENALTY') THEN -${partnerTransactions.amount}
                ELSE 0
              END), 0)`
            })
            .from(partnerTransactions)
            .where(eq(partnerTransactions.partnerId, partnerAttribution.partnerId))

          const currentBalance = balanceResult?.balance || 0
          const newBalance = currentBalance + partnerEarning

          // Create transaction record
          await tx.insert(partnerTransactions).values({
            partnerId: partnerAttribution.partnerId,
            transactionType: 'EARNING',
            amount: partnerEarning.toString(),
            description: `Earning from ${partnerAttribution.clientName} - Payment ${newPayment.id}`,
            referenceId: newEarning.id,
            referenceType: 'partner_earning',
            balanceBefore: currentBalance.toString(),
            balanceAfter: newBalance.toString(),
            createdBy: null // Automated system transaction
          })

          console.log('Partner earnings calculated:', {
            partnerId: partnerAttribution.partnerId,
            partnerName: partnerAttribution.partnerName,
            clientName: partnerAttribution.clientName,
            grossAmount,
            partnerEarning,
            newBalance
          })
        }
      } catch (earningsError) {
        console.error('Error calculating partner earnings:', earningsError)
        // Don't fail the payment processing if earnings calculation fails
      }
    }

      return { payment: newPayment, isExisting: false }
    })

    console.log('Payment processed successfully:', {
      paymentId: result.payment.id,
      invoiceId: result.payment.invoiceId,
      amount: result.payment.amount,
      isExisting: result.isExisting
    })

    return { success: true, payment: result.payment }
  } catch (error) {
    console.error('Error handling payment success:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

/**
 * Handle payment failure webhook
 */
async function handlePaymentFailure(paymentData: any) {
  try {
    const { payment } = paymentData

    // Use database transaction to ensure atomicity
    const result = await db.transaction(async (tx) => {
      // Find the invoice by order ID
      const [invoice] = await tx.select()
        .from(invoices)
        .where(eq(invoices.razorpayOrderId, payment.order_id))
        .limit(1)

      if (!invoice) {
        console.error('Invoice not found for order ID:', payment.order_id)
        throw new Error('Invoice not found')
      }

      // Check if payment failure already recorded (idempotency check)
      const [existingPayment] = await tx.select()
        .from(payments)
        .where(eq(payments.razorpayPaymentId, payment.id))
        .limit(1)

      if (existingPayment) {
        console.log('Payment failure already recorded:', payment.id)
        return { payment: existingPayment, isExisting: true }
      }

      // Create failed payment record
      const [failedPayment] = await tx.insert(payments).values({
        invoiceId: invoice.id,
        clientId: invoice.clientId,
        razorpayPaymentId: payment.id,
        razorpayOrderId: payment.order_id,
        amount: (payment.amount / 100).toString(),
        currency: payment.currency,
        status: 'failed',
        paymentMethod: payment.method,
        failureReason: payment.error_description || 'Payment failed',
        processedAt: new Date(payment.created_at * 1000)
      }).returning()

      return { payment: failedPayment, isExisting: false }
    })

    console.log('Payment failure recorded:', {
      paymentId: result.payment.id,
      invoiceId: result.payment.invoiceId,
      reason: payment.error_description,
      isExisting: result.isExisting
    })

    return { success: true, payment: result.payment }
  } catch (error) {
    console.error('Error handling payment failure:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

/**
 * Handle subscription webhook events
 */
async function handleSubscriptionEvent(subscriptionData: any, eventType: string) {
  try {
    const { subscription } = subscriptionData
    
    // Find subscription by Razorpay subscription ID
    const [existingSubscription] = await db.select()
      .from(subscriptions)
      .where(eq(subscriptions.razorpaySubscriptionId, subscription.id))
      .limit(1)

    if (!existingSubscription) {
      console.error('Subscription not found:', subscription.id)
      return { success: false, error: 'Subscription not found' }
    }

    let updateData: any = {}

    switch (eventType) {
      case 'subscription.activated':
        updateData.status = 'active'
        break
      case 'subscription.cancelled':
        updateData.status = 'cancelled'
        break
      case 'subscription.completed':
        updateData.status = 'completed'
        break
      case 'subscription.charged':
        // Handle successful subscription charge
        updateData.status = 'active'
        break
      case 'subscription.pending':
        updateData.status = 'pending'
        break
      case 'subscription.halted':
        updateData.status = 'suspended'
        break
      default:
        console.log('Unhandled subscription event:', eventType)
        return { success: true, message: 'Event ignored' }
    }

    // Update subscription
    await db.update(subscriptions)
      .set(updateData)
      .where(eq(subscriptions.id, existingSubscription.id))

    console.log('Subscription updated:', {
      subscriptionId: existingSubscription.id,
      event: eventType,
      status: updateData.status
    })

    return { success: true, subscription: existingSubscription }
  } catch (error) {
    console.error('Error handling subscription event:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

/**
 * Main webhook endpoint
 */
app.post('/razorpay', async (c) => {
  try {
    const body = await c.req.text()
    const signature = c.req.header('x-razorpay-signature')

    if (!signature) {
      return c.json({ error: 'Missing signature' }, 400)
    }

    // Verify webhook signature
    const isValid = verifyWebhookSignature(body, signature)

    if (!isValid) {
      console.error('Invalid webhook signature')
      return c.json({ error: 'Invalid signature' }, 401)
    }

    const webhookData = JSON.parse(body)
    const { event, payload } = webhookData

    console.log('Webhook received:', { event, entity: payload.payment?.entity || payload.subscription?.entity })

    // Check webhook idempotency to prevent duplicate processing
    const idempotencyCheck = await checkWebhookIdempotency(webhookData)

    if (!idempotencyCheck.shouldProcess) {
      console.log('Webhook already processed, returning cached result')
      return c.json(idempotencyCheck.existingResult || { message: 'Webhook already processed' }, 200)
    }

    let result

    switch (event) {
      case 'payment.captured':
      case 'payment.authorized':
        result = await handlePaymentSuccess(payload)
        break

      case 'payment.failed':
        result = await handlePaymentFailure(payload)
        break

      case 'subscription.activated':
      case 'subscription.cancelled':
      case 'subscription.completed':
      case 'subscription.charged':
      case 'subscription.pending':
      case 'subscription.halted':
        result = await handleSubscriptionEvent(payload, event)
        break

      default:
        console.log('Unhandled webhook event:', event)
        return c.json({ message: 'Event received but not processed' }, 200)
    }

    // Update webhook processing result for idempotency tracking
    await updateWebhookResult(webhookData, result)

    if (result.success) {
      return c.json({ message: 'Webhook processed successfully', data: result }, 200)
    } else {
      console.error('Webhook processing failed:', result.error)
      return c.json({ error: 'Webhook processing failed', details: result.error }, 500)
    }

  } catch (error) {
    console.error('Webhook error:', error)
    return c.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, 500)
  }
})

/**
 * Test webhook endpoint for development
 */
app.post('/test', async (c) => {
  try {
    const body = await c.req.json()
    
    console.log('Test webhook received:', body)
    
    // Simulate webhook processing
    const result = await handlePaymentSuccess({
      payment: {
        id: 'pay_test_123',
        order_id: body.order_id || 'order_test_123',
        amount: body.amount || 1500000, // 15000 INR in paise
        currency: 'INR',
        method: 'card',
        created_at: Math.floor(Date.now() / 1000)
      }
    })

    return c.json({ 
      message: 'Test webhook processed', 
      result 
    }, 200)

  } catch (error) {
    console.error('Test webhook error:', error)
    return c.json({ 
      error: 'Test webhook failed', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    }, 500)
  }
})

/**
 * Webhook status endpoint
 */
app.get('/status', async (c) => {
  return c.json({
    status: 'active',
    timestamp: new Date().toISOString(),
    webhookSecret: process.env.RAZORPAY_WEBHOOK_SECRET ? 'configured' : 'missing',
    razorpayKeys: {
      keyId: process.env.RAZORPAY_KEY_ID ? 'configured' : 'missing',
      keySecret: process.env.RAZORPAY_KEY_SECRET ? 'configured' : 'missing'
    }
  })
})

export default app
