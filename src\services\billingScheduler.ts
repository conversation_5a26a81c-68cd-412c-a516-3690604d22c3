import * as cron from 'node-cron'
import { db } from '@/db'
import { subscriptions, billingCycles, invoices, clients } from '@/db/schema'
import { eq, and, lte, gte, count } from 'drizzle-orm'
import { DueDateManager } from './dueDateManager'
import { billingMonitor } from './billingMonitor'
import { dunningManager } from './dunningManager'
import { paymentFailureHandler } from './paymentFailureHandler'
import { auditLogger } from './auditLogger'
import { securityMonitor } from './securityMonitor'

interface BillingSchedulerConfig {
  enabled: boolean
  timezone: string
  dryRun: boolean
}

class BillingScheduler {
  private config: BillingSchedulerConfig
  private tasks: Map<string, cron.ScheduledTask> = new Map()

  constructor(config: BillingSchedulerConfig = {
    enabled: true,
    timezone: 'Asia/Kolkata',
    dryRun: false
  }) {
    this.config = config
  }

  /**
   * Initialize all billing schedulers
   */
  public init(): void {
    if (!this.config.enabled) {
      console.log('🔄 Billing Scheduler: Disabled')
      return
    }

    console.log('🚀 Billing Scheduler: Initializing...')

    // Monthly billing generation - runs on 1st of every month at 6:00 AM
    this.scheduleMonthlyBilling()

    // Daily overdue check - runs every day at 9:00 AM
    this.scheduleOverdueCheck()

    // Daily due date processing - runs every day at 8:00 AM
    this.scheduleDueDateProcessing()

    // Weekly payment reminder - runs every Monday at 10:00 AM
    this.schedulePaymentReminders()

    // Billing health monitoring - runs every 6 hours
    this.scheduleBillingHealthMonitoring()

    // Dunning management - runs daily at 11:00 AM
    this.scheduleDunningManagement()

    // Payment failure processing - runs every 2 hours
    this.schedulePaymentFailureProcessing()

    console.log('✅ Billing Scheduler: All tasks scheduled')
  }

  /**
   * Schedule monthly billing generation
   * Runs on 1st of every month at 6:00 AM IST
   */
  private scheduleMonthlyBilling(): void {
    const task = cron.schedule('0 6 1 * *', async () => {
      console.log('🔄 Starting monthly billing generation...')

      try {
        const result = await this.generateMonthlyBilling()
        console.log('✅ Monthly billing completed:', result)

        // Log successful billing generation
        await auditLogger.logAdmin('monthly_billing_generated', {
          adminId: 'system',
          resource: 'billing_scheduler',
          details: {
            processed: result.processed,
            created: result.created,
            errors: result.errors
          },
          ipAddress: 'system',
          userAgent: 'billing-scheduler',
          success: true
        })

      } catch (error) {
        console.error('❌ Monthly billing failed:', error)
        await this.handleBillingFailure('monthly-billing', error as Error)
      }
    }, {
      timezone: this.config.timezone
    })

    this.tasks.set('monthly-billing', task)
    task.start()
    console.log('📅 Monthly billing scheduled: 1st of every month at 6:00 AM IST')
  }

  /**
   * Schedule daily overdue invoice check
   * Runs every day at 9:00 AM IST
   */
  private scheduleOverdueCheck(): void {
    const task = cron.schedule('0 9 * * *', async () => {
      console.log('🔄 Checking overdue invoices...')

      try {
        const result = await this.checkOverdueInvoices()
        console.log('✅ Overdue check completed:', result)
      } catch (error) {
        console.error('❌ Overdue check failed:', error)
      }
    }, {
      timezone: this.config.timezone
    })

    this.tasks.set('overdue-check', task)
    task.start()
    console.log('📅 Overdue check scheduled: Daily at 9:00 AM IST')
  }

  /**
   * Schedule daily due date processing
   * Runs every day at 8:00 AM IST
   */
  private scheduleDueDateProcessing(): void {
    const task = cron.schedule('0 8 * * *', async () => {
      console.log('🔄 Processing due dates and penalties...')

      try {
        const result = await DueDateManager.processAllDueDateUpdates()
        console.log('✅ Due date processing completed:', result)

        // Log summary if there were updates
        if (result.billingCycleUpdates.length > 0) {
          console.log(`📊 Updated ${result.billingCycleUpdates.length} billing cycles:`)
          result.billingCycleUpdates.forEach(update => {
            console.log(`  - Cycle ${update.id}: ${update.status} (${update.daysOverdue} days overdue, ₹${update.penaltyAmount} penalty)`)
          })
        }

        // Log successful due date processing
        await auditLogger.logAdmin('due_date_processing', {
          adminId: 'system',
          resource: 'billing_scheduler',
          details: {
            billingCycleUpdates: result.billingCycleUpdates.length,
            subscriptionUpdates: result.subscriptionsUpdated
          },
          ipAddress: 'system',
          userAgent: 'billing-scheduler',
          success: true
        })

      } catch (error) {
        console.error('❌ Due date processing failed:', error)
        await this.handleBillingFailure('due-date-processing', error as Error)
      }
    }, {
      timezone: this.config.timezone
    })

    this.tasks.set('due-date-processing', task)
    task.start()
    console.log('📅 Due date processing scheduled: Daily at 8:00 AM IST')
  }

  /**
   * Schedule weekly payment reminders
   * Runs every Monday at 10:00 AM IST
   */
  private schedulePaymentReminders(): void {
    const task = cron.schedule('0 10 * * 1', async () => {
      console.log('🔄 Sending payment reminders...')

      try {
        const result = await this.sendPaymentReminders()
        console.log('✅ Payment reminders sent:', result)
      } catch (error) {
        console.error('❌ Payment reminders failed:', error)
      }
    }, {
      timezone: this.config.timezone
    })

    this.tasks.set('payment-reminders', task)
    task.start()
    console.log('📅 Payment reminders scheduled: Every Monday at 10:00 AM IST')
  }

  /**
   * Generate monthly billing for all active subscriptions
   */
  public async generateMonthlyBilling(): Promise<{
    processed: number
    created: number
    errors: number
    details: any[]
  }> {
    const now = new Date()
    const currentMonth = now.getMonth() + 1
    const currentYear = now.getFullYear()

    console.log(`🔄 Generating billing for ${currentMonth}/${currentYear}`)

    if (this.config.dryRun) {
      console.log('🧪 DRY RUN MODE - No actual billing will be generated')
    }

    // Get all active subscriptions that are due for billing
    const activeSubscriptions = await db.select({
      id: subscriptions.id,
      clientId: subscriptions.clientId,
      planName: subscriptions.planName,
      studentCount: subscriptions.studentCount,
      monthlyAmount: subscriptions.monthlyAmount,
      nextBillingDate: subscriptions.nextBillingDate,
      billingCycle: subscriptions.billingCycle,
      client: {
        schoolName: clients.schoolName,
        email: clients.email,
        contactPerson: clients.contactPerson
      }
    })
    .from(subscriptions)
    .leftJoin(clients, eq(subscriptions.clientId, clients.id))
    .where(and(
      eq(subscriptions.status, 'active'),
      lte(subscriptions.nextBillingDate, now.toISOString().split('T')[0])
    ))

    const results = {
      processed: 0,
      created: 0,
      errors: 0,
      details: [] as any[]
    }

    for (const subscription of activeSubscriptions) {
      try {
        results.processed++

        // Check if billing cycle already exists for this month
        const existingCycle = await db.select()
          .from(billingCycles)
          .where(and(
            eq(billingCycles.subscriptionId, subscription.id),
            gte(billingCycles.cycleStart, `${currentYear}-${currentMonth.toString().padStart(2, '0')}-01`),
            lte(billingCycles.cycleStart, `${currentYear}-${currentMonth.toString().padStart(2, '0')}-31`)
          ))
          .limit(1)

        if (existingCycle.length > 0) {
          console.log(`⏭️  Billing cycle already exists for subscription ${subscription.id}`)
          results.details.push({
            subscriptionId: subscription.id,
            status: 'skipped',
            reason: 'Billing cycle already exists'
          })
          continue
        }

        if (!this.config.dryRun) {
          // Create billing cycle and invoice
          const billingResult = await this.createBillingCycleAndInvoice(subscription, currentMonth, currentYear)
          
          if (billingResult.success) {
            results.created++
            results.details.push({
              subscriptionId: subscription.id,
              status: 'created',
              billingCycleId: billingResult.billingCycleId,
              invoiceId: billingResult.invoiceId
            })

            // Update next billing date
            await this.updateNextBillingDate(subscription.id, subscription.billingCycle || 'monthly')
          } else {
            results.errors++
            results.details.push({
              subscriptionId: subscription.id,
              status: 'error',
              error: billingResult.error
            })
          }
        } else {
          console.log(`🧪 DRY RUN: Would create billing for subscription ${subscription.id}`)
          results.details.push({
            subscriptionId: subscription.id,
            status: 'dry-run',
            clientName: subscription.client?.schoolName
          })
        }

      } catch (error) {
        results.errors++
        console.error(`❌ Error processing subscription ${subscription.id}:`, error)
        results.details.push({
          subscriptionId: subscription.id,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return results
  }

  /**
   * Create billing cycle and invoice for a subscription
   */
  private async createBillingCycleAndInvoice(
    subscription: any,
    month: number,
    year: number
  ): Promise<{ success: boolean; billingCycleId?: string; invoiceId?: string; error?: string }> {
    try {
      // Calculate cycle dates
      const cycleStart = `${year}-${month.toString().padStart(2, '0')}-01`
      const cycleEndDate = new Date(year, month, 0) // Last day of month
      const cycleEnd = cycleEndDate.toISOString().split('T')[0]

      // Get subscription details for due date calculation
      const [subscriptionDetails] = await db.select({
        dueDate: subscriptions.dueDate,
        gracePeriodDays: subscriptions.gracePeriodDays,
        billingCycle: subscriptions.billingCycle,
        pricePerStudent: subscriptions.pricePerStudent,
        yearlyDiscountPercentage: subscriptions.yearlyDiscountPercentage
      })
      .from(subscriptions)
      .where(eq(subscriptions.id, subscription.id))
      .limit(1)

      // Calculate due date using DueDateManager
      const cycleStartDate = new Date(cycleStart)
      const dueDateCalc = DueDateManager.calculateDueDate(
        cycleStartDate,
        (subscriptionDetails?.billingCycle as 'monthly' | 'yearly') || 'monthly',
        subscriptionDetails?.dueDate || 15,
        subscriptionDetails?.gracePeriodDays || 3
      )
      const dueDateStr = dueDateCalc.dueDate.toISOString().split('T')[0]

      // Calculate billing amount based on billing cycle
      let baseAmount: number
      let discountAmount = 0

      if (subscriptionDetails?.billingCycle === 'yearly') {
        // For yearly billing, calculate annual amount with discount
        const monthlyAmount = parseFloat(subscription.monthlyAmount)
        const annualAmountBeforeDiscount = monthlyAmount * 12
        const discountPercentage = parseFloat(subscriptionDetails.yearlyDiscountPercentage?.toString() || '16.67')
        discountAmount = (annualAmountBeforeDiscount * discountPercentage) / 100
        baseAmount = annualAmountBeforeDiscount - discountAmount
      } else {
        // For monthly billing, use the monthly amount
        baseAmount = parseFloat(subscription.monthlyAmount)
      }

      const taxAmount = baseAmount * 0.18 // 18% GST
      const totalAmount = baseAmount + taxAmount

      // Create billing cycle
      const [newCycle] = await db.insert(billingCycles).values({
        subscriptionId: subscription.id,
        cycleStart,
        cycleEnd,
        studentCount: subscription.studentCount,
        baseAmount: (baseAmount + discountAmount).toString(), // Store original amount before discount
        discountAmount: discountAmount.toString(),
        setupFee: '0',
        taxAmount: taxAmount.toString(),
        totalAmount: totalAmount.toString(),
        isProrated: false,
        status: 'active', // Start as active, will be updated by due date processor
        dueDate: dueDateStr,
        gracePeriodDays: subscriptionDetails?.gracePeriodDays || 3,
        penaltyAmount: '0'
      }).returning()

      // Generate invoice number
      const invoiceNumber = `INV-${year}${month.toString().padStart(2, '0')}-${subscription.clientId.slice(-6).toUpperCase()}`

      // Create invoice
      const [newInvoice] = await db.insert(invoices).values({
        billingCycleId: newCycle.id,
        clientId: subscription.clientId,
        invoiceNumber,
        amount: baseAmount.toString(),
        taxAmount: taxAmount.toString(),
        totalAmount: totalAmount.toString(),
        status: 'sent',
        issuedDate: new Date().toISOString().split('T')[0],
        dueDate: dueDateStr
      }).returning()

      return {
        success: true,
        billingCycleId: newCycle.id,
        invoiceId: newInvoice.id
      }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Update next billing date for subscription
   */
  private async updateNextBillingDate(subscriptionId: string, billingCycle: string): Promise<void> {
    const nextBillingDate = new Date()
    
    if (billingCycle === 'monthly') {
      nextBillingDate.setMonth(nextBillingDate.getMonth() + 1)
    } else if (billingCycle === 'yearly') {
      nextBillingDate.setFullYear(nextBillingDate.getFullYear() + 1)
    }

    await db.update(subscriptions)
      .set({
        nextBillingDate: nextBillingDate.toISOString().split('T')[0]
      })
      .where(eq(subscriptions.id, subscriptionId))
  }

  /**
   * Check for overdue invoices and update status
   */
  public async checkOverdueInvoices(): Promise<{ updated: number; details: any[] }> {
    try {
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      console.log('🔍 Checking for overdue invoices...')

      // Get all unpaid invoices that are past due date
      const overdueInvoices = await db.select({
        id: invoices.id,
        clientId: invoices.clientId,
        invoiceNumber: invoices.invoiceNumber,
        totalAmount: invoices.totalAmount,
        dueDate: invoices.dueDate,
        status: invoices.status,
        billingCycleId: invoices.billingCycleId,
        // Client information
        schoolName: clients.schoolName,
        email: clients.email
      })
      .from(invoices)
      .leftJoin(clients, eq(invoices.clientId, clients.id))
      .where(and(
        eq(invoices.status, 'sent'),
        lte(invoices.dueDate, today.toISOString().split('T')[0])
      ))

      const results = {
        updated: 0,
        details: [] as any[]
      }

      for (const invoice of overdueInvoices) {
        try {
          if (!this.config.dryRun) {
            // Update invoice status to overdue
            await db.update(invoices)
              .set({
                status: 'overdue'
              })
              .where(eq(invoices.id, invoice.id))

            // Update corresponding billing cycle status
            if (invoice.billingCycleId) {
              await db.update(billingCycles)
                .set({
                  status: 'overdue'
                })
                .where(eq(billingCycles.id, invoice.billingCycleId))
            }

            results.updated++
            results.details.push({
              invoiceId: invoice.id,
              invoiceNumber: invoice.invoiceNumber,
              clientName: invoice.schoolName,
              amount: invoice.totalAmount,
              status: 'updated_to_overdue'
            })

            console.log(`📋 Updated invoice ${invoice.invoiceNumber} to overdue status`)
          } else {
            console.log(`🧪 DRY RUN: Would mark invoice ${invoice.invoiceNumber} as overdue`)
            results.details.push({
              invoiceId: invoice.id,
              invoiceNumber: invoice.invoiceNumber,
              clientName: invoice.schoolName,
              amount: invoice.totalAmount,
              status: 'dry_run_overdue'
            })
          }

        } catch (error) {
          console.error(`❌ Error updating invoice ${invoice.id}:`, error)
          results.details.push({
            invoiceId: invoice.id,
            invoiceNumber: invoice.invoiceNumber,
            status: 'error',
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      console.log(`✅ Overdue check completed. Updated ${results.updated} invoices`)
      return results

    } catch (error) {
      console.error('❌ Error in overdue invoice check:', error)
      throw error
    }
  }

  /**
   * Send payment reminders for pending invoices
   */
  public async sendPaymentReminders(): Promise<{ sent: number; details: any[] }> {
    try {
      const today = new Date()
      const reminderDate = new Date(today)
      reminderDate.setDate(reminderDate.getDate() + 3) // Remind 3 days before due date

      console.log('📧 Sending payment reminders...')

      // Get invoices that are due within 3 days and haven't been paid
      const upcomingInvoices = await db.select({
        id: invoices.id,
        clientId: invoices.clientId,
        invoiceNumber: invoices.invoiceNumber,
        totalAmount: invoices.totalAmount,
        dueDate: invoices.dueDate,
        status: invoices.status,
        // Client information
        schoolName: clients.schoolName,
        email: clients.email,
        contactPerson: clients.contactPerson,
        phone: clients.phone
      })
      .from(invoices)
      .leftJoin(clients, eq(invoices.clientId, clients.id))
      .where(and(
        eq(invoices.status, 'sent'),
        lte(invoices.dueDate, reminderDate.toISOString().split('T')[0]),
        gte(invoices.dueDate, today.toISOString().split('T')[0])
      ))

      const results = {
        sent: 0,
        details: [] as any[]
      }

      for (const invoice of upcomingInvoices) {
        try {
          if (!this.config.dryRun) {
            // Here you would integrate with your email service
            // For now, we'll just log the reminder
            console.log(`📧 Sending payment reminder for invoice ${invoice.invoiceNumber} to ${invoice.email}`)

            // TODO: Integrate with email service (Resend, etc.)
            // await emailService.sendPaymentReminder({
            //   to: invoice.email,
            //   invoiceNumber: invoice.invoiceNumber,
            //   amount: invoice.totalAmount,
            //   dueDate: invoice.dueDate,
            //   schoolName: invoice.schoolName
            // })

            results.sent++
            results.details.push({
              invoiceId: invoice.id,
              invoiceNumber: invoice.invoiceNumber,
              clientName: invoice.schoolName,
              email: invoice.email,
              amount: invoice.totalAmount,
              dueDate: invoice.dueDate,
              status: 'reminder_sent'
            })

          } else {
            console.log(`🧪 DRY RUN: Would send reminder for invoice ${invoice.invoiceNumber} to ${invoice.email}`)
            results.details.push({
              invoiceId: invoice.id,
              invoiceNumber: invoice.invoiceNumber,
              clientName: invoice.schoolName,
              email: invoice.email,
              amount: invoice.totalAmount,
              dueDate: invoice.dueDate,
              status: 'dry_run_reminder'
            })
          }

        } catch (error) {
          console.error(`❌ Error sending reminder for invoice ${invoice.id}:`, error)
          results.details.push({
            invoiceId: invoice.id,
            invoiceNumber: invoice.invoiceNumber,
            status: 'error',
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      console.log(`✅ Payment reminders completed. Sent ${results.sent} reminders`)
      return results

    } catch (error) {
      console.error('❌ Error in payment reminders:', error)
      throw error
    }
  }

  /**
   * Stop all scheduled tasks
   */
  public stop(): void {
    this.tasks.forEach((task, name) => {
      task.stop()
      console.log(`🛑 Stopped task: ${name}`)
    })
    this.tasks.clear()
  }

  /**
   * Get status of all scheduled tasks
   */
  public getStatus(): { [key: string]: boolean } {
    const status: { [key: string]: boolean } = {}
    this.tasks.forEach((task, name) => {
      status[name] = task.getStatus() === 'scheduled'
    })
    return status
  }

  /**
   * Handle billing failures and send alerts
   */
  private async handleBillingFailure(taskName: string, error: Error, details?: any): Promise<void> {
    const errorInfo = {
      task: taskName,
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      details
    }

    console.error(`🚨 BILLING FAILURE - ${taskName}:`, errorInfo)

    try {
      // Log security event for billing failure
      await securityMonitor.logSecurityEvent({
        type: 'billing_system_error',
        severity: 'high',

        details: errorInfo,
        ipAddress: 'system',
        userAgent: 'billing-scheduler',

      })

      // Log audit trail
      await auditLogger.logAdmin('billing_task_failed', {
        adminId: 'system',
        resource: 'billing_scheduler',
        details: {
          taskName,
          error: error.message,
          stack: error.stack,
          ...details
        },
        ipAddress: 'system',
        userAgent: 'billing-scheduler',
        success: false
      })

      // TODO: Send email/SMS alerts to administrators
      // TODO: Create support ticket for critical failures
      // TODO: Send Slack/Discord notification

    } catch (logError) {
      console.error('❌ Failed to log billing error:', logError)
    }
  }

  /**
   * Get comprehensive billing system health status
   */
  public async getSystemHealth(): Promise<{
    status: 'healthy' | 'warning' | 'critical'
    tasks: { [key: string]: boolean }
    metrics: {
      activeSubscriptions: number
      pendingInvoices: number
      overdueInvoices: number
      failedPayments: number
      lastBillingRun: string | null
    }
    issues: string[]
  }> {
    try {
      const tasks = this.getStatus()
      const issues: string[] = []

      // Check if all tasks are running
      Object.entries(tasks).forEach(([name, isRunning]) => {
        if (!isRunning) {
          issues.push(`Task '${name}' is not running`)
        }
      })

      // Get system metrics
      const [activeSubscriptions] = await db.select({ count: count() })
        .from(subscriptions)
        .where(eq(subscriptions.status, 'active'))

      const [pendingInvoices] = await db.select({ count: count() })
        .from(invoices)
        .where(eq(invoices.status, 'sent'))

      const [overdueInvoices] = await db.select({ count: count() })
        .from(invoices)
        .where(eq(invoices.status, 'overdue'))

      const [failedPayments] = await db.select({ count: count() })
        .from(invoices)
        .where(eq(invoices.status, 'failed'))

      // Check for critical issues
      if (overdueInvoices.count > 10) {
        issues.push(`High number of overdue invoices: ${overdueInvoices.count}`)
      }

      if (failedPayments.count > 5) {
        issues.push(`High number of failed payments: ${failedPayments.count}`)
      }

      // Determine overall status
      let status: 'healthy' | 'warning' | 'critical' = 'healthy'
      if (issues.length > 0) {
        status = issues.some(issue => issue.includes('critical') || issue.includes('failed')) ? 'critical' : 'warning'
      }

      return {
        status,
        tasks,
        metrics: {
          activeSubscriptions: activeSubscriptions.count,
          pendingInvoices: pendingInvoices.count,
          overdueInvoices: overdueInvoices.count,
          failedPayments: failedPayments.count,
          lastBillingRun: null // TODO: Track last billing run
        },
        issues
      }

    } catch (error) {
      console.error('❌ Error getting system health:', error)
      return {
        status: 'critical',
        tasks: this.getStatus(),
        metrics: {
          activeSubscriptions: 0,
          pendingInvoices: 0,
          overdueInvoices: 0,
          failedPayments: 0,
          lastBillingRun: null
        },
        issues: ['Failed to retrieve system health metrics']
      }
    }
  }

  /**
   * Manual trigger for billing generation (for testing/emergency)
   */
  public async triggerManualBilling(adminId?: string): Promise<any> {
    console.log('🔄 Manual billing trigger initiated...')

    try {
      const result = await this.generateMonthlyBilling()

      console.log('✅ Manual billing completed:', result)

      // TODO: Log manual trigger
      // await db.insert(systemLogs).values({
      //   level: 'info',
      //   category: 'billing',
      //   message: 'Manual billing triggered',
      //   data: JSON.stringify({ adminId, result }),
      //   createdAt: new Date()
      // })

      return result
    } catch (error) {
      await this.handleBillingFailure('manual-billing', error as Error, { adminId })
      throw error
    }
  }

  /**
   * Schedule billing health monitoring
   * Runs every 6 hours
   */
  private scheduleBillingHealthMonitoring(): void {
    const task = cron.schedule('0 */6 * * *', async () => {
      console.log('🔍 Starting billing health monitoring...')

      try {
        const healthReport = await billingMonitor.monitorBillingHealth()
        console.log('✅ Billing health monitoring completed:', {
          status: healthReport.status,
          alertCount: healthReport.alerts.length,
          criticalAlerts: healthReport.alerts.filter(a => a.severity === 'critical').length
        })

        // Send alerts if critical issues found
        if (healthReport.status === 'critical') {
          await this.sendCriticalAlert(healthReport)
        }

        // Log monitoring activity
        await auditLogger.logAdmin('billing_health_monitoring', {
          adminId: 'system',
          resource: 'billing_monitor',
          details: {
            status: healthReport.status,
            alertCount: healthReport.alerts.length,
            metrics: healthReport.metrics
          },
          ipAddress: 'system',
          userAgent: 'billing-scheduler',
          success: true
        })

      } catch (error) {
        console.error('❌ Billing health monitoring failed:', error)
        await this.handleBillingFailure('billing-health-monitoring', error as Error)
      }
    }, {
      timezone: this.config.timezone
    })

    this.tasks.set('billing-health-monitoring', task)
    task.start()
    console.log('📅 Billing health monitoring scheduled: Every 6 hours')
  }

  /**
   * Schedule dunning management
   * Runs daily at 11:00 AM IST
   */
  private scheduleDunningManagement(): void {
    const task = cron.schedule('0 11 * * *', async () => {
      console.log('⚖️ Starting dunning management...')

      try {
        const result = await dunningManager.processDunningActions()
        console.log('✅ Dunning management completed:', result)

        // Log dunning activity
        await auditLogger.logAdmin('dunning_management', {
          adminId: 'system',
          resource: 'dunning_manager',
          details: {
            processed: result.processed,
            actionsExecuted: result.actionsExecuted,
            errors: result.errors
          },
          ipAddress: 'system',
          userAgent: 'billing-scheduler',
          success: true
        })

      } catch (error) {
        console.error('❌ Dunning management failed:', error)
        await this.handleBillingFailure('dunning-management', error as Error)
      }
    }, {
      timezone: this.config.timezone
    })

    this.tasks.set('dunning-management', task)
    task.start()
    console.log('📅 Dunning management scheduled: Daily at 11:00 AM IST')
  }

  /**
   * Schedule payment failure processing
   * Runs every 2 hours
   */
  private schedulePaymentFailureProcessing(): void {
    const task = cron.schedule('0 */2 * * *', async () => {
      console.log('💳 Starting payment failure processing...')

      try {
        const result = await paymentFailureHandler.processPaymentRetries()
        console.log('✅ Payment failure processing completed:', result)

        // Log payment retry activity
        await auditLogger.logAdmin('payment_failure_processing', {
          adminId: 'system',
          resource: 'payment_failure_handler',
          details: {
            processed: result.processed,
            retried: result.retried,
            failed: result.failed
          },
          ipAddress: 'system',
          userAgent: 'billing-scheduler',
          success: true
        })

      } catch (error) {
        console.error('❌ Payment failure processing failed:', error)
        await this.handleBillingFailure('payment-failure-processing', error as Error)
      }
    }, {
      timezone: this.config.timezone
    })

    this.tasks.set('payment-failure-processing', task)
    task.start()
    console.log('📅 Payment failure processing scheduled: Every 2 hours')
  }

  /**
   * Send critical alert to administrators
   */
  private async sendCriticalAlert(healthReport: any): Promise<void> {
    try {
      // Log critical alert
      await securityMonitor.logSecurityEvent({
        type: 'billing_critical_alert',
        severity: 'critical',

        details: {
          status: healthReport.status,
          alerts: healthReport.alerts.filter((a: any) => a.severity === 'critical'),
          metrics: healthReport.metrics,
          timestamp: new Date().toISOString()
        },
        ipAddress: 'system',
        userAgent: 'billing-scheduler',

      })

      // TODO: Send email/SMS alerts to administrators
      console.log('🚨 CRITICAL BILLING ALERT: Administrators notified')

    } catch (error) {
      console.error('❌ Failed to send critical alert:', error)
    }
  }
}

// Export singleton instance
export const billingScheduler = new BillingScheduler()

// Export class for testing
export { BillingScheduler }
