import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "../globals.css";
import { Button } from "@/components/ui/Button";
import {
  Home,
  CreditCard,
  FileText,
  Settings,
  LogOut,
  School,
  BarChart3
} from "lucide-react";
import Link from "next/link";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

export const metadata: Metadata = {
  title: "School Portal | Schopio",
  description: "Schopio School Portal - Manage your school's subscription and billing",
};

export default function SchoolPortalLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={`${inter.variable} font-sans antialiased`}>
        <div className="min-h-screen bg-gray-50">
          {/* Header */}
          <header className="bg-white shadow-sm border-b">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between items-center h-16">
                <div className="flex items-center">
                  <Link href="/profile" className="flex items-center space-x-2">
                    <School className="h-8 w-8 text-blue-600" />
                    <span className="text-xl font-bold text-gray-900">Schopio</span>
                  </Link>
                  <span className="ml-4 px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded">
                    School Portal
                  </span>
                </div>
                
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-600">Welcome, Demo School</span>
                  <Button variant="outline" size="sm">
                    <LogOut className="h-4 w-4 mr-2" />
                    Logout
                  </Button>
                </div>
              </div>
            </div>
          </header>

          <div className="flex">
            {/* Sidebar */}
            <nav className="w-64 bg-white shadow-sm min-h-screen">
              <div className="p-4">
                <div className="space-y-2">
                  <Link href="/profile">
                    <Button variant="ghost" className="w-full justify-start">
                      <Home className="h-4 w-4 mr-3" />
                      Profile
                    </Button>
                  </Link>

                  <Link href="/profile/dashboard">
                    <Button variant="ghost" className="w-full justify-start">
                      <BarChart3 className="h-4 w-4 mr-3" />
                      Dashboard
                    </Button>
                  </Link>

                  <Link href="/profile/billing">
                    <Button variant="ghost" className="w-full justify-start">
                      <CreditCard className="h-4 w-4 mr-3" />
                      Billing & Payments
                    </Button>
                  </Link>
                  
                  <Link href="/profile/invoices">
                    <Button variant="ghost" className="w-full justify-start">
                      <FileText className="h-4 w-4 mr-3" />
                      Invoices
                    </Button>
                  </Link>
                  
                  <Link href="/profile/settings">
                    <Button variant="ghost" className="w-full justify-start">
                      <Settings className="h-4 w-4 mr-3" />
                      Settings
                    </Button>
                  </Link>
                </div>
              </div>
            </nav>

            {/* Main Content */}
            <main className="flex-1">
              {children}
            </main>
          </div>
        </div>

        {/* Razorpay Script */}
        <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
      </body>
    </html>
  );
}
