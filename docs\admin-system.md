# Schopio Admin System Documentation

## 🔐 Overview

The Schopio Admin System provides a secure, role-based administrative interface for managing the School Management SaaS platform. The admin system is designed to be accessed only by authorized personnel and is not publicly accessible through the frontend.

## 🚀 Getting Started

### 1. Database Setup

Ensure your database schema is up to date:

```bash
# Apply database migrations
bunx drizzle-kit push
```

### 2. Create Initial Admin User

Run the admin seeding script to create the first super admin user:

```bash
# Create super admin only
npm run seed:admin

# Create super admin + additional role-based admins
npm run seed:admin:all
```

**Default Super Admin Credentials:**
- Email: `<EMAIL>`
- Password: `Admin@123456`
- Role: `super_admin`

⚠️ **IMPORTANT**: Change the default password immediately after first login!

### 3. Install Dependencies

If you haven't already, install the tsx dependency:

```bash
npm install --save-dev tsx
```

## 🔑 Authentication

### Admin Login

**Endpoint:** `POST /api/admin/login`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "Admin@123456"
}
```

**Response:**
```json
{
  "message": "Admin login successful",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "admin": {
    "id": "uuid",
    "email": "<EMAIL>",
    "name": "Super Administrator",
    "role": "super_admin",
    "permissions": ["*"]
  }
}
```

### Using the Token

Include the JWT token in all subsequent requests:

```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -X GET \
     https://your-domain.com/api/admin/dashboard
```

## 👥 Admin Roles & Permissions

### Role Hierarchy

1. **Super Admin** (`super_admin`)
   - Full system access
   - Can manage other admin users
   - Permissions: `["*"]`

2. **Sales Manager** (`sales`)
   - Lead and client management
   - Demo scheduling
   - Permissions: `["leads:read", "leads:write", "leads:convert", "clients:read", "clients:write", "demos:read", "demos:write"]`

3. **Support Agent** (`support`)
   - Customer support and billing inquiries
   - Permissions: `["clients:read", "tickets:read", "tickets:write", "billing:read", "subscriptions:read"]`

4. **Billing Manager** (`billing`)
   - Financial operations and billing management
   - Permissions: `["billing:read", "billing:write", "invoices:read", "invoices:write", "payments:read", "subscriptions:read", "subscriptions:write"]`

## 📊 API Endpoints

### Authentication Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/admin/login` | Admin login | No |
| GET | `/api/admin/profile` | Get admin profile | Yes |

### Dashboard & Analytics

| Method | Endpoint | Description | Permission Required |
|--------|----------|-------------|-------------------|
| GET | `/api/admin/dashboard` | Dashboard statistics | Any admin role |
| GET | `/api/admin/system-info` | System information | `super_admin` only |

### Lead Management

| Method | Endpoint | Description | Permission Required |
|--------|----------|-------------|-------------------|
| GET | `/api/admin/leads` | List leads with pagination | `leads:read` |
| PUT | `/api/admin/leads/:id` | Update lead status | `leads:write` |

### Client Management

| Method | Endpoint | Description | Permission Required |
|--------|----------|-------------|-------------------|
| GET | `/api/admin/clients` | List clients with pagination | `clients:read` |
| GET | `/api/admin/clients/:id` | Get client details | `clients:read` |
| PUT | `/api/admin/clients/:id` | Update client information | `clients:write` |

### Software Request Management

| Method | Endpoint | Description | Permission Required |
|--------|----------|-------------|-------------------|
| GET | `/api/admin/software-requests` | List software requests | `requests:read` |
| PUT | `/api/admin/software-requests/:id` | Update request status | `requests:write` |

### Admin User Management (Super Admin Only)

| Method | Endpoint | Description | Role Required |
|--------|----------|-------------|---------------|
| GET | `/api/admin/admin-users` | List all admin users | `super_admin` |
| POST | `/api/admin/admin-users` | Create new admin user | `super_admin` |
| PUT | `/api/admin/admin-users/:id` | Update admin user | `super_admin` |

## 🔒 Security Features

### Security Headers

All admin routes include enhanced security headers:
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Strict-Transport-Security: max-age=31536000; includeSubDomains; preload`
- `Content-Security-Policy: default-src 'self'; ...`
- `Referrer-Policy: strict-origin-when-cross-origin`
- `Permissions-Policy: camera=(), microphone=(), geolocation=()`

### Access Logging

All admin access attempts are logged with:
- IP address
- User agent
- Request path and method
- Timestamp

### Rate Limiting

Admin routes include rate limiting middleware to prevent abuse.

## 📝 Usage Examples

### Dashboard Data

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -X GET \
     "https://your-domain.com/api/admin/dashboard"
```

### List Leads with Filters

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -X GET \
     "https://your-domain.com/api/admin/leads?status=new&page=1&limit=20&search=school"
```

### Update Lead Status

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -X PUT \
     -d '{"status": "contacted", "notes": "Initial contact made"}' \
     "https://your-domain.com/api/admin/leads/lead-uuid"
```

### Create New Admin User

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -X POST \
     -d '{
       "email": "<EMAIL>",
       "name": "Sales Manager",
       "role": "sales",
       "password": "SecurePassword123!"
     }' \
     "https://your-domain.com/api/admin/admin-users"
```

## 🛡️ Security Best Practices

1. **Change Default Passwords**: Always change default passwords after initial setup
2. **Use Strong Passwords**: Enforce strong password policies for all admin users
3. **Regular Token Rotation**: JWT tokens expire after 24 hours
4. **Monitor Access Logs**: Regularly review admin access logs for suspicious activity
5. **Principle of Least Privilege**: Assign minimal required permissions to each admin role
6. **Secure Environment Variables**: Ensure `JWT_SECRET` is properly configured
7. **HTTPS Only**: Always use HTTPS in production environments

## 🔧 Troubleshooting

### Common Issues

1. **"Invalid admin token" Error**
   - Check if token is properly formatted with "Bearer " prefix
   - Verify token hasn't expired (24-hour limit)
   - Ensure admin user is still active in database

2. **"Insufficient permissions" Error**
   - Verify admin user has required role or permission
   - Check if super admin access is required for the endpoint

3. **"Admin user not found" Error**
   - Ensure admin user exists in database
   - Check if admin account is active (`isActive: true`)

### Database Queries

Check admin users in database:
```sql
SELECT id, email, name, role, is_active, last_login, created_at 
FROM admin_users 
ORDER BY created_at DESC;
```

## 📞 Support

For technical support or questions about the admin system, contact the development team or refer to the main project documentation.
