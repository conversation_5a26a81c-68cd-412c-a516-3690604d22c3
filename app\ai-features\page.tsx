'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import {
  Brain,
  TrendingUp,
  Target,
  BookOpen,
  Users,
  Award,
  Lightbulb,
  BarChart3,
  CheckCircle,
  ArrowRight,
  Zap,
  Star,
  Clock,
  Heart,
  Shield,
  Sparkles
} from 'lucide-react'
import { Metadata } from 'next'

export default function AIFeaturesPage() {
  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 }
  }

  const studentImprovementAreas = [
    {
      icon: BookOpen,
      title: "Academic Performance",
      description: "AI identifies learning gaps and provides personalized study recommendations",
      benefits: [
        "Personalized learning paths based on individual strengths",
        "Early detection of subjects where students struggle",
        "Adaptive content difficulty matching student capability",
        "Predictive insights for exam preparation"
      ],
      color: "from-blue-500 to-blue-600",
      improvement: "35% average grade improvement"
    },
    {
      icon: Lightbulb,
      title: "Critical Thinking Skills",
      description: "AI-powered exercises that develop analytical and problem-solving abilities",
      benefits: [
        "Logic puzzles adapted to student's cognitive level",
        "Real-world problem scenarios for practical application",
        "Pattern recognition training through interactive exercises",
        "Decision-making simulations with immediate feedback"
      ],
      color: "from-emerald-500 to-emerald-600",
      improvement: "42% improvement in analytical skills"
    },
    {
      icon: Users,
      title: "Social & Emotional Development",
      description: "AI monitors and supports emotional intelligence and social skills growth",
      benefits: [
        "Emotion recognition and regulation training",
        "Peer interaction analysis and improvement suggestions",
        "Empathy building through guided scenarios",
        "Conflict resolution skill development"
      ],
      color: "from-purple-500 to-purple-600",
      improvement: "28% better social interaction scores"
    },
    {
      icon: Target,
      title: "Goal Setting & Achievement",
      description: "AI helps students set realistic goals and tracks progress systematically",
      benefits: [
        "SMART goal creation with AI guidance",
        "Progress tracking with motivational milestones",
        "Achievement celebration and reward systems",
        "Long-term career path planning assistance"
      ],
      color: "from-orange-500 to-orange-600",
      improvement: "67% higher goal completion rate"
    }
  ]

  const aiCapabilities = [
    {
      icon: Brain,
      title: "Intelligent Learning Analytics",
      description: "AI analyzes how each student learns best and adapts content delivery accordingly",
      features: [
        "Learning style identification (visual, auditory, kinesthetic)",
        "Optimal study time recommendations",
        "Memory retention pattern analysis",
        "Cognitive load optimization"
      ]
    },
    {
      icon: TrendingUp,
      title: "Predictive Performance Modeling",
      description: "Forecast student outcomes and intervene before problems develop",
      features: [
        "Early warning system for academic struggles",
        "Dropout risk prediction and prevention",
        "Career aptitude assessment",
        "University readiness evaluation"
      ]
    },
    {
      icon: Sparkles,
      title: "Personalized Skill Development",
      description: "AI creates custom skill-building programs for each student's unique needs",
      features: [
        "Individual strength and weakness mapping",
        "Customized practice exercises",
        "Skill progression tracking",
        "Competency-based advancement"
      ]
    }
  ]

  const realWorldApplications = [
    {
      scenario: "Mathematics Struggling Student",
      problem: "Sarah consistently scores below average in algebra",
      aiSolution: "AI identifies she's a visual learner struggling with abstract concepts",
      outcome: "Provides visual representations, interactive graphs, and real-world applications",
      result: "Sarah's math scores improve by 40% in 3 months"
    },
    {
      scenario: "Shy Student Development",
      problem: "Alex avoids group activities and has difficulty making friends",
      aiSolution: "AI suggests gradual social exposure through structured activities",
      outcome: "Recommends compatible peer groups and conversation starters",
      result: "Alex develops 3 close friendships and joins debate club"
    },
    {
      scenario: "Gifted Student Engagement",
      problem: "Emma finishes assignments quickly and becomes disruptive",
      aiSolution: "AI provides advanced challenges and enrichment activities",
      outcome: "Creates personalized advanced projects and mentorship opportunities",
      result: "Emma becomes a peer tutor and excels in science competitions"
    }
  ]

  return (
    <main className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-slate-900 via-blue-900 to-emerald-900 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#ffffff12_1px,transparent_1px),linear-gradient(to_bottom,#ffffff12_1px,transparent_1px)] bg-[size:24px_24px] opacity-30"></div>
        <div className="container mx-auto px-4 relative">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-5xl mx-auto"
          >
            <div className="inline-flex items-center gap-2 bg-emerald-500/20 border border-emerald-400/30 px-6 py-3 rounded-full text-sm font-bold mb-8">
              <Brain className="w-4 h-4 text-emerald-400" />
              AI-Powered Student Development
            </div>
            <h1 className="text-5xl lg:text-7xl font-bold mb-8 leading-tight">
              How AI Transforms
              <span className="bg-gradient-to-r from-emerald-400 to-blue-400 bg-clip-text text-transparent"> Every Student&apos;s Journey</span>
            </h1>
            <p className="text-xl lg:text-2xl text-blue-200 leading-relaxed mb-12 max-w-4xl mx-auto">
              Discover how Schopio&apos;s AI doesn&apos;t just manage data—it actively helps students excel in academics,
              develop critical life skills, and build confidence for their future success.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="xl"
                icon={ArrowRight}
                iconPosition="right"
                className="bg-gradient-to-r from-emerald-600 to-blue-600 hover:from-emerald-700 hover:to-blue-700 text-white font-bold"
                onClick={() => window.location.href = '/demo'}
              >
                See AI in Action
              </Button>
              <Button
                size="xl"
                variant="outline"
                className="border-2 border-white/30 text-white hover:bg-white/10"
                onClick={() => window.location.href = '/solutions'}
              >
                Explore Solutions
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Student Improvement Areas */}
      <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
        <div className="container mx-auto px-4">
          <motion.div
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 border border-blue-200 px-4 py-2 rounded-full text-sm font-bold mb-4">
              <Star className="w-4 h-4" />
              Student Development Focus
            </div>
            <h2 className="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
              Where AI Makes the Biggest Impact on
              <span className="bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent">Student Growth</span>
            </h2>
            <p className="text-xl text-slate-600 max-w-4xl mx-auto">
              Our AI doesn&apos;t just track performance—it actively identifies opportunities for growth and
              provides personalized interventions that help every student reach their full potential.
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-8 max-w-7xl mx-auto">
            {studentImprovementAreas.map((area, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="h-full bg-white border-0 shadow-xl hover:shadow-2xl transition-all duration-300 group">
                  <CardHeader padding="lg">
                    <div className="flex items-start gap-4">
                      <div className={`w-16 h-16 bg-gradient-to-br ${area.color} rounded-2xl flex items-center justify-center text-white group-hover:scale-110 transition-transform duration-300`}>
                        <area.icon className="w-8 h-8" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-2xl font-bold text-slate-900 mb-2">{area.title}</h3>
                        <p className="text-slate-600 mb-4">{area.description}</p>
                        <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-700 px-3 py-1 rounded-full text-sm font-bold">
                          <TrendingUp className="w-3 h-3" />
                          {area.improvement}
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent padding="lg">
                    <div className="space-y-3">
                      {area.benefits.map((benefit, benefitIndex) => (
                        <div key={benefitIndex} className="flex items-start gap-3">
                          <CheckCircle className="w-5 h-5 text-emerald-500 flex-shrink-0 mt-0.5" />
                          <span className="text-slate-700">{benefit}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* AI Capabilities Deep Dive */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
              Advanced AI Capabilities That
              <span className="bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">Drive Results</span>
            </h2>
            <p className="text-xl text-slate-600 max-w-4xl mx-auto">
              Powered by cutting-edge machine learning algorithms, our AI system provides
              unprecedented insights into student learning patterns and development opportunities.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {aiCapabilities.map((capability, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
              >
                <Card className="h-full bg-gradient-to-br from-slate-50 to-blue-50 border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                  <CardContent padding="lg">
                    <div className="text-center mb-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-emerald-500 rounded-2xl flex items-center justify-center text-white mx-auto mb-4">
                        <capability.icon className="w-8 h-8" />
                      </div>
                      <h3 className="text-xl font-bold text-slate-900 mb-2">{capability.title}</h3>
                      <p className="text-slate-600">{capability.description}</p>
                    </div>
                    <div className="space-y-3">
                      {capability.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-start gap-3">
                          <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-full mt-2 flex-shrink-0"></div>
                          <span className="text-slate-700 text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Real-World Applications */}
      <section className="py-20 bg-gradient-to-br from-emerald-50 to-blue-50">
        <div className="container mx-auto px-4">
          <motion.div
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <div className="inline-flex items-center gap-2 bg-emerald-100 text-emerald-700 border border-emerald-200 px-4 py-2 rounded-full text-sm font-bold mb-4">
              <Heart className="w-4 h-4" />
              Real Student Success Stories
            </div>
            <h2 className="text-4xl lg:text-5xl font-bold text-slate-900 mb-6">
              See How AI Transforms
              <span className="bg-gradient-to-r from-emerald-600 to-blue-600 bg-clip-text text-transparent">Individual Lives</span>
            </h2>
            <p className="text-xl text-slate-600 max-w-4xl mx-auto">
              These scenarios demonstrate how our AI identifies unique student needs and
              provides targeted interventions that create lasting positive change.
            </p>
          </motion.div>

          <div className="space-y-8 max-w-5xl mx-auto">
            {realWorldApplications.map((application, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
              >
                <Card className="bg-white border-0 shadow-xl">
                  <CardContent padding="lg">
                    <div className="grid md:grid-cols-4 gap-6 items-center">
                      <div className="md:col-span-1">
                        <div className="text-center">
                          <div className="w-12 h-12 bg-gradient-to-br from-red-100 to-red-200 rounded-xl flex items-center justify-center mx-auto mb-3">
                            <span className="text-red-600 font-bold text-lg">{index + 1}</span>
                          </div>
                          <h4 className="font-bold text-slate-900 mb-2">{application.scenario}</h4>
                          <p className="text-sm text-red-600 font-medium">{application.problem}</p>
                        </div>
                      </div>
                      <div className="md:col-span-1">
                        <div className="text-center">
                          <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center mx-auto mb-3">
                            <Brain className="w-6 h-6 text-blue-600" />
                          </div>
                          <h5 className="font-semibold text-slate-900 mb-2">AI Analysis</h5>
                          <p className="text-sm text-blue-600">{application.aiSolution}</p>
                        </div>
                      </div>
                      <div className="md:col-span-1">
                        <div className="text-center">
                          <div className="w-12 h-12 bg-gradient-to-br from-emerald-100 to-emerald-200 rounded-xl flex items-center justify-center mx-auto mb-3">
                            <Target className="w-6 h-6 text-emerald-600" />
                          </div>
                          <h5 className="font-semibold text-slate-900 mb-2">AI Action</h5>
                          <p className="text-sm text-emerald-600">{application.outcome}</p>
                        </div>
                      </div>
                      <div className="md:col-span-1">
                        <div className="text-center">
                          <div className="w-12 h-12 bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-xl flex items-center justify-center mx-auto mb-3">
                            <Award className="w-6 h-6 text-yellow-600" />
                          </div>
                          <h5 className="font-semibold text-slate-900 mb-2">Result</h5>
                          <p className="text-sm text-yellow-600 font-medium">{application.result}</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-slate-900 via-blue-900 to-emerald-900 text-white">
        <div className="container mx-auto px-4">
          <motion.div
            variants={fadeInUp}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="inline-flex items-center gap-2 bg-emerald-500/20 border border-emerald-400/30 px-6 py-3 rounded-full text-sm font-bold mb-8">
              <Zap className="w-4 h-4 text-emerald-400" />
              Ready to Transform Student Outcomes?
            </div>
            <h2 className="text-4xl lg:text-5xl font-bold mb-6">
              Give Every Student the
              <span className="bg-gradient-to-r from-emerald-400 to-blue-400 bg-clip-text text-transparent"> AI Advantage</span>
            </h2>
            <p className="text-xl text-blue-200 leading-relaxed mb-12">
              Join forward-thinking schools that are already using AI to unlock every student&apos;s potential.
              See how Schopio&apos;s intelligent system can transform your educational outcomes.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="xl"
                icon={ArrowRight}
                iconPosition="right"
                className="bg-gradient-to-r from-emerald-600 to-blue-600 hover:from-emerald-700 hover:to-blue-700 text-white font-bold"
                onClick={() => window.location.href = '/demo'}
              >
                Schedule AI Demo
              </Button>
              <Button
                size="xl"
                variant="outline"
                className="border-2 border-white/30 text-white hover:bg-white/10"
                onClick={() => window.location.href = '/packages'}
              >
                Explore Packages
              </Button>
            </div>
            <div className="mt-8 flex items-center justify-center gap-6 text-sm text-blue-200">
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                30-minute demo
              </div>
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4" />
                No commitment required
              </div>
              <div className="flex items-center gap-2">
                <Star className="w-4 h-4" />
                Personalized for your school
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </main>
  )
}
