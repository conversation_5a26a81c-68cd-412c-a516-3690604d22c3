# User Flow Diagrams - School Management SaaS

## 🔄 Complete User Journey Overview

```mermaid
graph TD
    A[Visitor on Landing Page] --> B{Interested?}
    B -->|Yes| C[Fill Contact Form]
    B -->|No| D[Exit]
    C --> E[Lead Created in System]
    E --> F[Admin Reviews Lead]
    F --> G[Demo Scheduled]
    G --> H[Demo Conducted]
    H --> I{Convert to Client?}
    I -->|Yes| J[Client Onboarding]
    I -->|No| K[Lead Marked Lost]
    J --> L[School Portal Access]
    L --> M[Ongoing Subscription]
    M --> N[Monthly Billing Cycle]
    N --> O[Payment Processing]
    O --> P[Service Continuation]
```

## 📋 1. Lead Generation Flow (Landing Page)

### User Journey: Prospect to Lead
```
Landing Page Visit → Feature Exploration → Trust Building → Contact Form → Lead Creation
```

**Detailed Steps:**
1. **Landing Page Entry**
   - Visitor arrives via marketing channels
   - Views hero section with competitive advantages
   - Explores feature comparison table

2. **Interest Building**
   - Reads trust-building content
   - Views testimonials and case studies
   - Calculates pricing for their school size

3. **Contact Form Submission**
   - Fills contact form with school details
   - Provides estimated student count
   - Selects preferred demo type

4. **Lead Capture**
   - System creates lead record
   - Sends confirmation email
   - Triggers admin notification

**API Flow:**
```
POST /api/leads → Lead Creation → Email Notification → Admin Dashboard Update
```

## 🎯 2. Demo Booking Flow

### User Journey: Lead to Demo
```
Lead Creation → Demo Scheduling → Calendar Integration → Confirmation → Demo Execution
```

**Detailed Steps:**
1. **Demo Request**
   - Lead receives follow-up email
   - Clicks demo booking link
   - Selects available time slot

2. **Scheduling**
   - Calendar integration shows availability
   - Lead selects preferred date/time
   - Demo type selection (online/onsite)

3. **Confirmation**
   - Booking confirmation sent
   - Calendar invite generated
   - Admin receives notification

4. **Demo Execution**
   - Admin conducts demo
   - Updates lead status
   - Adds demo notes

**API Flow:**
```
POST /api/demo-booking → Calendar Check → Booking Creation → Email Confirmation
```

## 🏢 3. Client Onboarding Flow (Admin Dashboard)

### Admin Journey: Lead to Active Client
```
Demo Completion → Proposal Creation → Contract Signing → Client Setup → Portal Access
```

**Detailed Steps:**
1. **Post-Demo Assessment**
   - Admin reviews demo feedback
   - Evaluates lead qualification
   - Decides on conversion

2. **Proposal Creation**
   - Admin creates custom proposal
   - Sets pricing based on student count
   - Selects subscription plan

3. **Contract Process**
   - Proposal sent to prospect
   - Contract negotiation
   - Agreement signing

4. **Client Setup**
   - Admin converts lead to client
   - Creates school portal account
   - Sets up initial subscription

5. **Portal Access**
   - Client receives login credentials
   - Initial onboarding call scheduled
   - Training materials provided

**API Flow:**
```
POST /api/admin/leads/:id/convert → Client Creation → Subscription Setup → Portal Access
```

## 💳 4. Billing & Payment Flow

### System Journey: Subscription to Payment
```
Billing Cycle Start → Invoice Generation → Payment Request → Payment Processing → Service Continuation
```

**Detailed Steps:**
1. **Billing Cycle Initiation**
   - System calculates billing amount
   - Considers pro-rated charges
   - Generates billing cycle record

2. **Invoice Creation**
   - Invoice generated automatically
   - PDF created and stored
   - Email sent to client

3. **Payment Processing**
   - Client receives payment reminder
   - Accesses school portal
   - Initiates payment via Razorpay

4. **Payment Verification**
   - Razorpay webhook received
   - Payment status updated
   - Invoice marked as paid

5. **Service Continuation**
   - Subscription remains active
   - Next billing cycle scheduled
   - Client receives receipt

**API Flow:**
```
Billing Cycle → Invoice Generation → Razorpay Order → Payment Verification → Status Update
```

## 🏫 5. School Portal User Flow

### Client Journey: Login to Service Management
```
Portal Login → Dashboard View → Subscription Management → Payment Processing → Support Access
```

**Detailed Steps:**
1. **Authentication**
   - School code and email login
   - JWT token generation
   - Dashboard access granted

2. **Dashboard Overview**
   - Subscription status display
   - Recent billing information
   - Quick action buttons

3. **Subscription Management**
   - View current plan details
   - Update student count
   - Request plan changes

4. **Payment Management**
   - View billing history
   - Download invoices
   - Process pending payments

5. **Support Access**
   - Create support tickets
   - View ticket history
   - Communicate with support team

**API Flow:**
```
POST /api/school/auth/login → Dashboard Data → Subscription Info → Payment History
```

## 🎫 6. Support Ticket Flow

### Client Journey: Issue to Resolution
```
Issue Identification → Ticket Creation → Admin Assignment → Communication → Resolution
```

**Detailed Steps:**
1. **Issue Identification**
   - Client encounters problem
   - Accesses support section
   - Selects issue category

2. **Ticket Creation**
   - Fills ticket form
   - Provides detailed description
   - Attaches relevant files

3. **Admin Processing**
   - Ticket auto-assigned
   - Admin reviews issue
   - Begins investigation

4. **Communication**
   - Admin responds to ticket
   - Client provides additional info
   - Back-and-forth messaging

5. **Resolution**
   - Issue resolved
   - Ticket marked closed
   - Client satisfaction survey

**API Flow:**
```
POST /api/school/support/tickets → Admin Notification → Message Exchange → Resolution
```

## 📊 7. Admin Analytics Flow

### Admin Journey: Data Collection to Insights
```
Data Collection → Processing → Dashboard Display → Report Generation → Decision Making
```

**Detailed Steps:**
1. **Data Collection**
   - System collects usage metrics
   - Billing data aggregation
   - Client activity tracking

2. **Analytics Processing**
   - Data transformation
   - KPI calculations
   - Trend analysis

3. **Dashboard Display**
   - Real-time metrics
   - Visual charts and graphs
   - Performance indicators

4. **Report Generation**
   - Monthly business reports
   - Client health scores
   - Revenue forecasting

**API Flow:**
```
GET /api/admin/analytics/dashboard → Data Aggregation → Chart Generation → Display
```

## 🔄 8. Subscription Lifecycle Flow

### System Journey: Activation to Renewal/Cancellation
```
Activation → Active Billing → Renewal Notice → Payment → Continuation/Cancellation
```

**Detailed Steps:**
1. **Subscription Activation**
   - Initial payment processed
   - Service activated
   - Welcome email sent

2. **Active Billing Cycles**
   - Monthly/yearly billing
   - Automatic invoice generation
   - Payment processing

3. **Renewal Management**
   - Renewal notices sent
   - Payment reminders
   - Grace period handling

4. **Subscription Changes**
   - Plan upgrades/downgrades
   - Student count adjustments
   - Pro-rated billing calculations

5. **End of Lifecycle**
   - Successful renewal
   - Voluntary cancellation
   - Involuntary suspension

This comprehensive flow documentation ensures smooth user experiences across all touchpoints of the SaaS platform.
