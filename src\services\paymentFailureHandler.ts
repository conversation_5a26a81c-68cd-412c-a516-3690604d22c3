import { db } from '@/src/db'
import { 
  payments, 
  invoices, 
  clients, 
  subscriptions,
  paymentReminders
} from '@/src/db/schema'
import { eq, and, desc, count, sql, gte } from 'drizzle-orm'
import { auditLogger } from './auditLogger'
import { billingMonitor } from './billingMonitor'

export interface PaymentFailure {
  id: string
  paymentId: string
  invoiceId: string
  clientId: string
  amount: number
  currency: string
  failureReason: string
  failureCode?: string
  retryCount: number
  maxRetries: number
  nextRetryAt?: Date
  status: 'pending_retry' | 'max_retries_reached' | 'resolved' | 'cancelled'
  createdAt: Date
  updatedAt: Date
  client: {
    schoolName: string
    email: string
    contactPerson: string
    phone: string
  }
}

export interface RetryStrategy {
  maxRetries: number
  retryIntervals: number[] // in hours
  backoffMultiplier: number
  maxRetryInterval: number // in hours
}

export interface PaymentFailureStats {
  totalFailures: number
  pendingRetries: number
  maxRetriesReached: number
  resolved: number
  failureRate: number
  commonFailureReasons: { reason: string; count: number }[]
  retrySuccessRate: number
}

class PaymentFailureHandler {
  private static instance: PaymentFailureHandler
  private retryStrategy: RetryStrategy = {
    maxRetries: 3,
    retryIntervals: [2, 24, 72], // 2 hours, 1 day, 3 days
    backoffMultiplier: 1.5,
    maxRetryInterval: 168 // 7 days
  }

  private constructor() {}

  public static getInstance(): PaymentFailureHandler {
    if (!PaymentFailureHandler.instance) {
      PaymentFailureHandler.instance = new PaymentFailureHandler()
    }
    return PaymentFailureHandler.instance
  }

  /**
   * Handle a payment failure
   */
  public async handlePaymentFailure(
    paymentId: string,
    failureReason: string,
    failureCode?: string
  ): Promise<{
    success: boolean
    retryScheduled: boolean
    nextRetryAt?: Date
    message: string
  }> {
    try {
      console.log(`💳 Handling payment failure: ${paymentId}`)

      // Get payment details
      const [payment] = await db
        .select({
          id: payments.id,
          invoiceId: payments.invoiceId,
          clientId: payments.clientId,
          amount: payments.amount,
          currency: payments.currency,
          status: payments.status,
          // Invoice details
          invoiceNumber: invoices.invoiceNumber,
          // Client details
          schoolName: clients.schoolName,
          email: clients.email,
          contactPerson: clients.contactPerson,
          phone: clients.phone
        })
        .from(payments)
        .leftJoin(invoices, eq(payments.invoiceId, invoices.id))
        .leftJoin(clients, eq(payments.clientId, clients.id))
        .where(eq(payments.id, paymentId))
        .limit(1)

      if (!payment) {
        throw new Error(`Payment not found: ${paymentId}`)
      }

      // Update payment status to failed
      await db
        .update(payments)
        .set({
          status: 'failed',
          failureReason,
          processedAt: new Date()
        })
        .where(eq(payments.id, paymentId))

      // Get retry count for this invoice
      const [retryCount] = await db
        .select({ count: count() })
        .from(payments)
        .where(and(
          eq(payments.invoiceId, payment.invoiceId || ''),
          eq(payments.status, 'failed')
        ))

      const currentRetryCount = retryCount.count
      const shouldRetry = currentRetryCount < this.retryStrategy.maxRetries

      let nextRetryAt: Date | undefined
      let retryScheduled = false

      if (shouldRetry) {
        // Calculate next retry time
        const retryIntervalIndex = Math.min(currentRetryCount - 1, this.retryStrategy.retryIntervals.length - 1)
        const retryInterval = this.retryStrategy.retryIntervals[retryIntervalIndex]
        
        nextRetryAt = new Date()
        nextRetryAt.setHours(nextRetryAt.getHours() + retryInterval)
        retryScheduled = true

        console.log(`🔄 Payment retry scheduled for ${nextRetryAt.toISOString()}`)
      } else {
        console.log(`❌ Max retries reached for payment ${paymentId}`)
        
        // Create alert for max retries reached
        await billingMonitor.monitorBillingHealth()
      }

      // Log the failure
      await auditLogger.logPayment('payment_failed', {
        paymentId,

        clientId: payment.clientId || undefined,
        amount: parseFloat(payment.amount),
        errorMessage: failureReason,

        retryCount: currentRetryCount,
        nextRetryAt: nextRetryAt?.toISOString(),
        ipAddress: 'system',
        userAgent: 'payment-failure-handler'
      })

      // Send failure notification
      await this.sendFailureNotification(payment, failureReason, retryScheduled, nextRetryAt)

      return {
        success: true,
        retryScheduled,
        nextRetryAt,
        message: retryScheduled 
          ? `Payment failure handled. Retry scheduled for ${nextRetryAt?.toISOString()}`
          : 'Payment failure handled. Max retries reached.'
      }

    } catch (error) {
      console.error('❌ Failed to handle payment failure:', error)
      throw error
    }
  }

  /**
   * Process payment retries
   */
  public async processPaymentRetries(): Promise<{
    processed: number
    retried: number
    failed: number
    details: any[]
  }> {
    try {
      console.log('🔄 Processing payment retries...')

      // Get failed payments that are due for retry
      const now = new Date()
      const failedPayments = await db
        .select({
          id: payments.id,
          invoiceId: payments.invoiceId,
          clientId: payments.clientId,
          amount: payments.amount,
          currency: payments.currency,
          razorpayOrderId: payments.razorpayOrderId,
          failureReason: payments.failureReason,
          // Invoice details
          invoiceNumber: invoices.invoiceNumber,
          totalAmount: invoices.totalAmount,
          // Client details
          schoolName: clients.schoolName,
          email: clients.email
        })
        .from(payments)
        .leftJoin(invoices, eq(payments.invoiceId, invoices.id))
        .leftJoin(clients, eq(payments.clientId, clients.id))
        .where(and(
          eq(payments.status, 'failed'),
          // Add logic to check if retry is due based on failure time and retry strategy
        ))

      const results = {
        processed: 0,
        retried: 0,
        failed: 0,
        details: [] as any[]
      }

      for (const payment of failedPayments) {
        try {
          results.processed++

          // Check retry count
          const [retryCount] = await db
            .select({ count: count() })
            .from(payments)
            .where(and(
              eq(payments.invoiceId, payment.invoiceId || ''),
              eq(payments.status, 'failed')
            ))

          if (retryCount.count >= this.retryStrategy.maxRetries) {
            results.details.push({
              paymentId: payment.id,
              status: 'max_retries_reached',
              message: 'Maximum retry attempts reached'
            })
            continue
          }

          // Attempt to retry payment
          const retryResult = await this.retryPayment(payment)

          if (retryResult.success) {
            results.retried++
            results.details.push({
              paymentId: payment.id,
              status: 'retried',
              newPaymentId: retryResult.newPaymentId
            })
          } else {
            results.failed++
            results.details.push({
              paymentId: payment.id,
              status: 'retry_failed',
              error: retryResult.error
            })
          }

        } catch (error) {
          results.failed++
          results.details.push({
            paymentId: payment.id,
            status: 'error',
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }

      console.log(`✅ Payment retry processing completed: ${results.retried} retried, ${results.failed} failed`)

      return results

    } catch (error) {
      console.error('❌ Payment retry processing failed:', error)
      throw error
    }
  }

  /**
   * Retry a failed payment
   */
  private async retryPayment(payment: any): Promise<{
    success: boolean
    newPaymentId?: string
    error?: string
  }> {
    try {
      // TODO: Integrate with Razorpay to create new payment attempt
      // For now, we'll create a new pending payment record
      
      const [newPayment] = await db.insert(payments).values({
        invoiceId: payment.invoiceId,
        clientId: payment.clientId,
        amount: payment.amount,
        currency: payment.currency,
        status: 'pending',
        paymentMethod: 'razorpay',
        createdAt: new Date()
      }).returning()

      console.log(`🔄 Payment retry created: ${newPayment.id} for invoice ${payment.invoiceNumber}`)

      return {
        success: true,
        newPaymentId: newPayment.id
      }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Send failure notification to client
   */
  private async sendFailureNotification(
    payment: any,
    failureReason: string,
    retryScheduled: boolean,
    nextRetryAt?: Date
  ): Promise<void> {
    try {
      const message = retryScheduled
        ? `Payment failed for invoice ${payment.invoiceNumber}. Reason: ${failureReason}. We will automatically retry the payment on ${nextRetryAt?.toLocaleDateString()}.`
        : `Payment failed for invoice ${payment.invoiceNumber}. Reason: ${failureReason}. Please update your payment method and try again.`

      // Create notification record
      await db.insert(paymentReminders).values({
        invoiceId: payment.invoiceId,
        clientId: payment.clientId,
        reminderType: 'payment_failure',
        sentDate: new Date(),
        emailSent: true,
        smsSent: false
      })

      // TODO: Send email notification using Resend
      console.log(`📧 Payment failure notification sent to ${payment.email}`)

    } catch (error) {
      console.error('Failed to send failure notification:', error)
    }
  }

  /**
   * Get payment failure statistics
   */
  public async getPaymentFailureStats(days: number = 30): Promise<PaymentFailureStats> {
    try {
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)

      // Total failures
      const [totalFailures] = await db
        .select({ count: count() })
        .from(payments)
        .where(and(
          eq(payments.status, 'failed'),
          gte(payments.createdAt, startDate)
        ))

      // Pending retries (failed payments that haven't reached max retries)
      const [pendingRetries] = await db
        .select({ count: count() })
        .from(payments)
        .where(eq(payments.status, 'failed'))

      // Total payments for failure rate calculation
      const [totalPayments] = await db
        .select({ count: count() })
        .from(payments)
        .where(gte(payments.createdAt, startDate))

      const failureRate = totalPayments.count > 0 ? (totalFailures.count / totalPayments.count) * 100 : 0

      // Common failure reasons
      const failureReasons = await db
        .select({
          reason: payments.failureReason,
          count: count()
        })
        .from(payments)
        .where(and(
          eq(payments.status, 'failed'),
          gte(payments.createdAt, startDate)
        ))
        .groupBy(payments.failureReason)
        .orderBy(desc(count()))
        .limit(5)

      return {
        totalFailures: totalFailures.count,
        pendingRetries: pendingRetries.count,
        maxRetriesReached: 0, // TODO: Calculate actual count
        resolved: 0, // TODO: Calculate resolved failures
        failureRate,
        commonFailureReasons: failureReasons.map(r => ({
          reason: r.reason || 'Unknown',
          count: r.count
        })),
        retrySuccessRate: 0 // TODO: Calculate retry success rate
      }

    } catch (error) {
      console.error('Failed to get payment failure stats:', error)
      throw error
    }
  }

  /**
   * Get active payment failures
   */
  public async getActivePaymentFailures(): Promise<PaymentFailure[]> {
    try {
      const failedPayments = await db
        .select({
          id: payments.id,
          invoiceId: payments.invoiceId,
          clientId: payments.clientId,
          amount: payments.amount,
          currency: payments.currency,
          failureReason: payments.failureReason,
          createdAt: payments.createdAt,
          // Client details
          schoolName: clients.schoolName,
          email: clients.email,
          contactPerson: clients.contactPerson,
          phone: clients.phone
        })
        .from(payments)
        .leftJoin(clients, eq(payments.clientId, clients.id))
        .where(eq(payments.status, 'failed'))
        .orderBy(desc(payments.createdAt))

      return failedPayments
        .filter(payment => payment.invoiceId !== null && payment.clientId !== null)
        .map(payment => ({
        id: payment.id,
        paymentId: payment.id,
        invoiceId: payment.invoiceId!,
        clientId: payment.clientId!,
        amount: parseFloat(payment.amount),
        currency: payment.currency || 'INR',
        failureReason: payment.failureReason || 'Unknown',
        retryCount: 0, // TODO: Calculate actual retry count
        maxRetries: this.retryStrategy.maxRetries,
        status: 'pending_retry' as const,
        createdAt: payment.createdAt || new Date(),
        updatedAt: payment.createdAt || new Date(),
        client: {
          schoolName: payment.schoolName || 'Unknown School',
          email: payment.email || '<EMAIL>',
          contactPerson: payment.contactPerson || 'Unknown Contact',
          phone: payment.phone || 'N/A'
        }
      }))

    } catch (error) {
      console.error('Failed to get active payment failures:', error)
      throw error
    }
  }

  /**
   * Update retry strategy
   */
  public updateRetryStrategy(strategy: Partial<RetryStrategy>): void {
    this.retryStrategy = { ...this.retryStrategy, ...strategy }
    console.log('🔧 Payment retry strategy updated:', this.retryStrategy)
  }

  /**
   * Manual retry for a specific payment
   */
  public async manualRetryPayment(paymentId: string, adminId: string): Promise<{
    success: boolean
    newPaymentId?: string
    message: string
  }> {
    try {
      const [payment] = await db
        .select()
        .from(payments)
        .where(eq(payments.id, paymentId))
        .limit(1)

      if (!payment) {
        throw new Error(`Payment not found: ${paymentId}`)
      }

      if (payment.status !== 'failed') {
        throw new Error(`Payment is not in failed status: ${payment.status}`)
      }

      const retryResult = await this.retryPayment(payment)

      // Log manual retry
      await auditLogger.logAdmin('manual_payment_retry', {
        adminId,
        resource: 'payment',
        resourceId: paymentId,
        details: {
          originalPaymentId: paymentId,
          newPaymentId: retryResult.newPaymentId,
          success: retryResult.success
        },
        ipAddress: 'admin',
        userAgent: 'admin-dashboard',
        success: retryResult.success
      })

      return {
        success: retryResult.success,
        newPaymentId: retryResult.newPaymentId,
        message: retryResult.success 
          ? `Payment retry initiated successfully. New payment ID: ${retryResult.newPaymentId}`
          : `Payment retry failed: ${retryResult.error}`
      }

    } catch (error) {
      console.error('❌ Manual payment retry failed:', error)
      throw error
    }
  }
}

export const paymentFailureHandler = PaymentFailureHandler.getInstance()
