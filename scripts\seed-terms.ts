import { config } from 'dotenv'
import { resolve } from 'path'

// Load environment variables from .env.local
config({ path:'.env.local'})

import { db } from '../src/db'
import { termsConditions } from '../src/db/schema'

const defaultTermsContent = `
# Schopio School Management System - Terms & Conditions

## 1. Payment Terms
- Monthly payment due on the same date each month
- 3-day grace period after due date
- Late payment penalty: 2% per day after grace period
- Automatic suspension after 15 days overdue
- Account reactivation fee: ₹500 if suspended
- Data backup retention: 30 days during suspension
- Termination clause: Non-payment beyond 30 days

## 2. Service Terms
- 99.5% uptime guarantee
- 24/7 technical support
- Data backup and security
- Feature updates included
- Training and onboarding support

## 3. Subscription Pricing
- Base rate: ₹80/student/month (monthly plan)
- Yearly discount: 18.75% (₹65/student/month)
- Minimum billing: 50 students
- Maximum billing: Actual student count

## 4. Cancellation Policy
- 30-day notice required for cancellation
- No refunds for partial months
- Data export available for 30 days post-cancellation
- Account reactivation within 90 days possible

## 5. Data Protection
- Encrypted sensitive data storage
- GDPR compliance for data handling
- Secure data retention policies
- Right to data deletion upon request

## 6. Support & Maintenance
- Regular system updates and maintenance
- Bug fixes and security patches
- Feature enhancements based on feedback
- Training materials and documentation

## 7. Liability & Disclaimers
- Service provided "as is" with reasonable care
- Limited liability for indirect damages
- User responsible for data backup
- Force majeure clause for service interruptions

## 8. Agreement Terms
- Terms effective from subscription start date
- Modifications require 30-day notice
- Governing law: Indian jurisdiction
- Dispute resolution through arbitration

By accepting these terms, you agree to comply with all conditions outlined above.
`

async function seedTerms() {
  try {
    console.log('Seeding default terms & conditions...')
    
    // Check if terms already exist
    const existingTerms = await db.select().from(termsConditions).limit(1)
    
    if (existingTerms.length > 0) {
      console.log('Terms & conditions already exist, skipping seed.')
      return
    }
    
    // Insert default terms
    await db.insert(termsConditions).values({
      version: 'v1.0',
      title: 'Schopio School Management System - Terms & Conditions',
      content: defaultTermsContent.trim(),
      effectiveDate: new Date().toISOString().split('T')[0], // Today's date
      isActive: true
    })
    
    console.log('✅ Default terms & conditions seeded successfully!')
    
  } catch (error) {
    console.error('❌ Error seeding terms & conditions:', error)
  }
}

// Run if called directly
if (require.main === module) {
  seedTerms().then(() => process.exit(0))
}

export { seedTerms }
