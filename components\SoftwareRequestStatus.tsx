'use client'

import React from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/Button'
import { Separator } from '@/components/ui/separator'
import { 
  Clock, 
  Eye, 
  CheckCircle, 
  XCircle, 
  Settings, 
  Zap,
  FileText,
  Calendar,
  User,
  Building,
  Phone,
  Mail,
  Calculator,
  ArrowUp
} from 'lucide-react'

interface SoftwareRequestStatusProps {
  request: {
    id: string
    requestType: 'demo' | 'production'
    status: string
    studentCount: number
    facultyCount: number
    completeAddress: string
    contactNumber: string
    primaryEmail: string
    calculatedAverageFee?: string
    termsAccepted: boolean
    termsVersion?: string
    createdAt: string
    approvedAt?: string
    activatedAt?: string
    reviewNotes?: string
    rejectionReason?: string
  }
  statusHistory: Array<{
    fromStatus: string | null
    toStatus: string
    changeReason: string
    createdAt: string
  }>
  canUpgrade?: boolean
  onUpgrade?: () => void
}

const statusConfig = {
  pending: {
    icon: Clock,
    color: 'bg-yellow-100 text-yellow-800',
    label: 'Pending Review',
    description: 'Your request is waiting for admin review'
  },
  under_review: {
    icon: Eye,
    color: 'bg-blue-100 text-blue-800',
    label: 'Under Review',
    description: 'Admin is currently reviewing your request'
  },
  approved: {
    icon: CheckCircle,
    color: 'bg-green-100 text-green-800',
    label: 'Approved',
    description: 'Your request has been approved'
  },
  rejected: {
    icon: XCircle,
    color: 'bg-red-100 text-red-800',
    label: 'Rejected',
    description: 'Your request was not approved'
  },
  setup_in_progress: {
    icon: Settings,
    color: 'bg-purple-100 text-purple-800',
    label: 'Setup in Progress',
    description: 'Your system is being configured'
  },
  activated: {
    icon: Zap,
    color: 'bg-emerald-100 text-emerald-800',
    label: 'Activated',
    description: 'Your system is ready to use'
  }
}

export default function SoftwareRequestStatus({ request, statusHistory, canUpgrade, onUpgrade }: SoftwareRequestStatusProps) {
  const currentStatus = statusConfig[request.status as keyof typeof statusConfig]
  const StatusIcon = currentStatus?.icon || Clock

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* Current Status Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Software Request Status
              </CardTitle>
              <CardDescription>
                Request ID: {request.id.slice(0, 8)}...
              </CardDescription>
            </div>
            <Badge 
              variant="secondary" 
              className={currentStatus?.color}
            >
              <StatusIcon className="h-4 w-4 mr-1" />
              {currentStatus?.label}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-gray-600">{currentStatus?.description}</p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center gap-2">
                <Building className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Request Type</p>
                  <p className="text-sm text-gray-600 capitalize">{request.requestType}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm font-medium">Submitted</p>
                  <p className="text-sm text-gray-600">{formatDate(request.createdAt)}</p>
                </div>
              </div>
              
              {request.approvedAt && (
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <div>
                    <p className="text-sm font-medium">Approved</p>
                    <p className="text-sm text-gray-600">{formatDate(request.approvedAt)}</p>
                  </div>
                </div>
              )}
            </div>

            {request.reviewNotes && (
              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Review Notes</h4>
                <p className="text-blue-800 text-sm">{request.reviewNotes}</p>
              </div>
            )}

            {request.rejectionReason && (
              <div className="mt-4 p-4 bg-red-50 rounded-lg">
                <h4 className="font-medium text-red-900 mb-2">Rejection Reason</h4>
                <p className="text-red-800 text-sm">{request.rejectionReason}</p>
              </div>
            )}

            {/* Upgrade to Production Button */}
            {canUpgrade && (
              <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-emerald-50 rounded-lg border border-blue-200">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">Ready to Go Live?</h4>
                    <p className="text-sm text-gray-600">
                      Upgrade your demo to a full production subscription with billing and all features.
                    </p>
                  </div>
                  <Button
                    onClick={onUpgrade}
                    className="bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700"
                  >
                    <ArrowUp className="h-4 w-4 mr-2" />
                    Upgrade to Production
                  </Button>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Request Details */}
      <Card>
        <CardHeader>
          <CardTitle>Request Details</CardTitle>
          <CardDescription>
            Information submitted with your request
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Basic Information */}
            <div>
              <h4 className="font-medium mb-3">School Information</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium">Students</p>
                    <p className="text-sm text-gray-600">{request.studentCount.toLocaleString()}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium">Faculty</p>
                    <p className="text-sm text-gray-600">{request.facultyCount.toLocaleString()}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium">Contact</p>
                    <p className="text-sm text-gray-600">{request.contactNumber}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium">Email</p>
                    <p className="text-sm text-gray-600">{request.primaryEmail}</p>
                  </div>
                </div>
              </div>
              
              <div className="mt-4">
                <p className="text-sm font-medium mb-1">Address</p>
                <p className="text-sm text-gray-600">{request.completeAddress}</p>
              </div>
            </div>

            {/* Production Request Details */}
            {request.requestType === 'production' && (
              <>
                <Separator />
                <div>
                  <h4 className="font-medium mb-3">Production Details</h4>
                  <div className="space-y-3">
                    {request.calculatedAverageFee && (
                      <div className="flex items-center gap-2">
                        <Calculator className="h-4 w-4 text-gray-500" />
                        <div>
                          <p className="text-sm font-medium">Calculated Average Fee</p>
                          <p className="text-sm text-gray-600">₹{parseFloat(request.calculatedAverageFee).toFixed(2)}</p>
                        </div>
                      </div>
                    )}
                    
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-gray-500" />
                      <div>
                        <p className="text-sm font-medium">Terms Accepted</p>
                        <p className="text-sm text-gray-600">
                          {request.termsAccepted ? 'Yes' : 'No'}
                          {request.termsVersion && ` (${request.termsVersion})`}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Status History */}
      <Card>
        <CardHeader>
          <CardTitle>Status History</CardTitle>
          <CardDescription>
            Track the progress of your request
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {statusHistory.map((history, index) => {
              const statusInfo = statusConfig[history.toStatus as keyof typeof statusConfig]
              const StatusIcon = statusInfo?.icon || Clock
              
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-start gap-3"
                >
                  <div className={`p-2 rounded-full ${statusInfo?.color || 'bg-gray-100 text-gray-600'}`}>
                    <StatusIcon className="h-4 w-4" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <p className="font-medium">{statusInfo?.label || history.toStatus}</p>
                      <p className="text-sm text-gray-500">{formatDate(history.createdAt)}</p>
                    </div>
                    {history.changeReason && (
                      <p className="text-sm text-gray-600 mt-1">{history.changeReason}</p>
                    )}
                  </div>
                </motion.div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
